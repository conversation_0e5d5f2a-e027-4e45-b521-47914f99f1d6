body {
    margin: 0px;
    padding: 0px;
    font-family: "Helvetica Neue", Helvetica, Roboto, Arial, sans-serif;
    font-size: 12px;
    color: #3d3d3d;
}

.text-center {
    text-align: center;
}


hr {
    border: none;
    height: 1px;
    background-color: #dddddd;
    margin: 0px;
    padding: 0px;
}

table {
    page-break-inside: avoid;
    width: 100%;
}
table tr td {
    font-size: 11px !important;
    font-weight: bold;
}
a[href]::after {
    content: none;
}

h1 {
    font-size: 18px;
}

h2 {
    font-size: 1em;
    margin: 0px 0px 0.25em 0px;
}

ul {
    list-style-type: none;
    margin: 0px;
    padding: 0px;
}

i {
    display: inline-block;
    margin: 0px;
    padding: 0em 0em 0em 0.25em;
    vertical-align: middle;
}

.type {
    text-align: center;
    font-size: 1.8em;
    font-weight: bold;
    color: #3d3d3d;
    margin-bottom: 0.25em;
}

.type address {
    font-size: 11px;
    font-weight: lighter;
}

#header {
    width: 100%;
    margin-bottom: 0.5cm;
    background-color: #eee;
    overflow: hidden;
}

#items {
    width: 100%;
    margin: 2em 0px 0.5em 0px;
}

#items table {
    width: 100%;
    text-align: left;
    margin-bottom: 0.5em;
    border-collapse: collapse;
}

#items table thead tr th {
    color: #3d3d3d;
    font-size: 1em;
    text-transform: uppercase;
    padding-bottom: 0.25cm;
}

#items table tbody tr {
    border-bottom: solid 1px #3d3d3d;
}

#items table tbody tr td {
    color: #3d3d3d;
    font-size: 12px;
    font-weight: normal;
    padding: 0.5em 0em;
    text-align: left;
}

.vendor {
    margin-top: 2px;
    font-size: 10px;
}