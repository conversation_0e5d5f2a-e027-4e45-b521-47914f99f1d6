.order {
	page-break-after: always;
    break-after: always;
    position: relative;
}

.giftCard__container {
	display: flex;
	align-items: center;
	justify-content: center;
	flex-wrap: wrap;
	width: 4.5in;
	border: dashed 4px #3d3d3d;
	text-align: center;
	margin: 2rem auto;
	border-radius: 0.25rem;
	padding: 2rem 0 1rem 0;
	page-break-inside: avoid;
}

.giftCard__innerContainer {
	flex: 1 1 auto;
}

.giftCard__farm_name {
	font-size: 1.25rem;
	margin-bottom: 1rem;
	color: #444;
	max-width: 100%;
}

.giftCard__subtitle {
	color: #777;
}

.giftCard__amount {
	font-size: 2rem;
	margin: 1rem 0;
	font-weight: bold;
}

.giftCard__instructions {
	font-size: 0.6rem;
	color: #555;
	margin: 1rem auto 0 auto;
	padding: 0 1rem;
}