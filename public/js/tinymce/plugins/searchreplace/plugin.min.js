!function(){function e(e){return e&&1==e.nodeType&&"false"===e.contentEditable}function t(t,n,r,i,o){function a(e,t){if(t=t||0,!e[0])throw"findAndReplaceDOMText cannot handle zero-length matches";var n=e.index;if(t>0){var r=e[t];if(!r)throw"Invalid capture group";n+=e[0].indexOf(r),e[0]=r}return[n,n+e[0].length,[e[0]]]}function s(t){var n;if(3===t.nodeType)return t.data;if(h[t.nodeName]&&!p[t.nodeName])return"";if(n="",e(t))return"\n";if((p[t.nodeName]||m[t.nodeName])&&(n+="\n"),t=t.firstChild)do n+=s(t);while(t=t.nextSibling);return n}function l(t,n,r){var i,o,a,s,l=[],u=0,c=t,d=n.shift(),f=0;e:for(;;){if((p[c.nodeName]||m[c.nodeName]||e(c))&&u++,3===c.nodeType&&(!o&&c.length+u>=d[1]?(o=c,s=d[1]-u):i&&l.push(c),!i&&c.length+u>d[0]&&(i=c,a=d[0]-u),u+=c.length),i&&o){if(c=r({startNode:i,startNodeIndex:a,endNode:o,endNodeIndex:s,innerNodes:l,match:d[2],matchIndex:f}),u-=o.length-s,i=null,o=null,l=[],d=n.shift(),f++,!d)break}else if(h[c.nodeName]&&!p[c.nodeName]||!c.firstChild){if(c.nextSibling){c=c.nextSibling;continue}}else if(!e(c)){c=c.firstChild;continue}for(;;){if(c.nextSibling){c=c.nextSibling;break}if(c.parentNode===t)break e;c=c.parentNode}}}function u(e){var t;if("function"!=typeof e){var n=e.nodeType?e:f.createElement(e);t=function(e,t){var r=n.cloneNode(!1);return r.setAttribute("data-mce-index",t),e&&r.appendChild(f.createTextNode(e)),r}}else t=e;return function(e){var n,r,i,o=e.startNode,a=e.endNode,s=e.matchIndex;if(o===a){var l=o;i=l.parentNode,e.startNodeIndex>0&&(n=f.createTextNode(l.data.substring(0,e.startNodeIndex)),i.insertBefore(n,l));var u=t(e.match[0],s);return i.insertBefore(u,l),e.endNodeIndex<l.length&&(r=f.createTextNode(l.data.substring(e.endNodeIndex)),i.insertBefore(r,l)),l.parentNode.removeChild(l),u}n=f.createTextNode(o.data.substring(0,e.startNodeIndex)),r=f.createTextNode(a.data.substring(e.endNodeIndex));for(var c=t(o.data.substring(e.startNodeIndex),s),d=[],p=0,h=e.innerNodes.length;p<h;++p){var m=e.innerNodes[p],g=t(m.data,s);m.parentNode.replaceChild(g,m),d.push(g)}var v=t(a.data.substring(0,e.endNodeIndex),s);return i=o.parentNode,i.insertBefore(n,o),i.insertBefore(c,o),i.removeChild(o),i=a.parentNode,i.insertBefore(v,a),i.insertBefore(r,a),i.removeChild(a),v}}var c,d,f,p,h,m,g=[],v=0;if(f=n.ownerDocument,p=o.getBlockElements(),h=o.getWhiteSpaceElements(),m=o.getShortEndedElements(),d=s(n)){if(t.global)for(;c=t.exec(d);)g.push(a(c,i));else c=d.match(t),g.push(a(c,i));return g.length&&(v=g.length,l(n,g,u(r))),v}}function n(e){function n(){function t(){o.statusbar.find("#next").disabled(!a(d+1).length),o.statusbar.find("#prev").disabled(!a(d-1).length)}function n(){e.windowManager.alert("Could not find the specified string.",function(){o.find("#find")[0].focus()})}var r,i={};r=tinymce.trim(e.selection.getContent({format:"text"}));var o=e.windowManager.open({layout:"flex",pack:"center",align:"center",onClose:function(){e.focus(),c.done()},onSubmit:function(e){var r,s,l,u;return e.preventDefault(),s=o.find("#case").checked(),u=o.find("#words").checked(),l=o.find("#find").value(),l.length?i.text==l&&i.caseState==s&&i.wholeWord==u?0===a(d+1).length?void n():(c.next(),void t()):(r=c.find(l,s,u),r||n(),o.statusbar.items().slice(1).disabled(0===r),t(),void(i={text:l,caseState:s,wholeWord:u})):(c.done(!1),void o.statusbar.items().slice(1).disabled(!0))},buttons:[{text:"Find",subtype:"primary",onclick:function(){o.submit()}},{text:"Replace",disabled:!0,onclick:function(){c.replace(o.find("#replace").value())||(o.statusbar.items().slice(1).disabled(!0),d=-1,i={})}},{text:"Replace all",disabled:!0,onclick:function(){c.replace(o.find("#replace").value(),!0,!0),o.statusbar.items().slice(1).disabled(!0),i={}}},{type:"spacer",flex:1},{text:"Prev",name:"prev",disabled:!0,onclick:function(){c.prev(),t()}},{text:"Next",name:"next",disabled:!0,onclick:function(){c.next(),t()}}],title:"Find and replace",items:{type:"form",padding:20,labelGap:30,spacing:10,items:[{type:"textbox",name:"find",size:40,label:"Find",value:r},{type:"textbox",name:"replace",size:40,label:"Replace with"},{type:"checkbox",name:"case",text:"Match case",label:" "},{type:"checkbox",name:"words",text:"Whole words",label:" "}]}})}function r(e){var t=e.getAttribute("data-mce-index");return"number"==typeof t?""+t:t}function i(n){var r,i;return i=e.dom.create("span",{"data-mce-bogus":1}),i.className="mce-match-marker",r=e.getBody(),c.done(!1),t(n,r,i,!1,e.schema)}function o(e){var t=e.parentNode;e.firstChild&&t.insertBefore(e.firstChild,e),e.parentNode.removeChild(e)}function a(t){var n,i=[];if(n=tinymce.toArray(e.getBody().getElementsByTagName("span")),n.length)for(var o=0;o<n.length;o++){var a=r(n[o]);null!==a&&a.length&&a===t.toString()&&i.push(n[o])}return i}function s(t){var n=d,r=e.dom;t=t!==!1,t?n++:n--,r.removeClass(a(d),"mce-match-marker-selected");var i=a(n);return i.length?(r.addClass(a(n),"mce-match-marker-selected"),e.selection.scrollIntoView(i[0]),n):-1}function l(t){var n=e.dom,r=t.parentNode;n.remove(t),n.isEmpty(r)&&n.remove(r)}function u(e){var t=r(e);return null!==t&&t.length>0}var c=this,d=-1;c.init=function(e){e.addMenuItem("searchreplace",{text:"Find and replace",shortcut:"Meta+F",onclick:n,separator:"before",context:"edit"}),e.addButton("searchreplace",{tooltip:"Find and replace",shortcut:"Meta+F",onclick:n}),e.addCommand("SearchReplace",n),e.shortcuts.add("Meta+F","",n)},c.find=function(e,t,n){e=e.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&"),e=n?"\\b"+e+"\\b":e;var r=i(new RegExp(e,t?"g":"gi"));return r&&(d=-1,d=s(!0)),r},c.next=function(){var e=s(!0);e!==-1&&(d=e)},c.prev=function(){var e=s(!1);e!==-1&&(d=e)},c.replace=function(t,n,i){var s,f,p,h,m,g,v=d;for(n=n!==!1,p=e.getBody(),f=tinymce.grep(tinymce.toArray(p.getElementsByTagName("span")),u),s=0;s<f.length;s++){var y=r(f[s]);if(h=m=parseInt(y,10),i||h===d){for(t.length?(f[s].firstChild.nodeValue=t,o(f[s])):l(f[s]);f[++s];){if(h=parseInt(r(f[s]),10),h!==m){s--;break}l(f[s])}n&&v--}else m>d&&f[s].setAttribute("data-mce-index",m-1)}return e.undoManager.add(),d=v,n?(g=a(v+1).length>0,c.next()):(g=a(v-1).length>0,c.prev()),!i&&g},c.done=function(t){var n,i,a,s;for(i=tinymce.toArray(e.getBody().getElementsByTagName("span")),n=0;n<i.length;n++){var l=r(i[n]);null!==l&&l.length&&(l===d.toString()&&(a||(a=i[n].firstChild),s=i[n].firstChild),o(i[n]))}if(a&&s){var u=e.dom.createRng();return u.setStart(a,0),u.setEnd(s,s.data.length),t!==!1&&e.selection.setRng(u),u}}}tinymce.PluginManager.add("searchreplace",n)}();