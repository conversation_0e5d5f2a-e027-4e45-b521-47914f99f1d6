<?php

namespace Tests\Feature\API;

use App\Actions\Product\ConfirmGiftCardPurchase;
use App\Events\Order\OrderWasConfirmed;
use App\Exceptions\OrderChargeException;
use App\Models\Card;
use App\Models\Date;
use App\Models\Order;
use App\Models\Payment;
use App\Models\Pickup;
use App\Models\Product;
use App\Models\Schedule;
use App\Models\User;
use App\Support\Enums\ProductType;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Str;
use Mockery\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class GiftCardCheckoutTest extends TenantTestCase
{
    #[Test]
    public function it_requires_a_logged_in_user(): void
    {
        $product = Product::factory()->create();

        $this->postJson(route('api.products.checkout.store', ['product' => $product->slug]))
            ->assertUnauthorized();
    }

    #[Test]
    public function it_requires_a_valid_product(): void
    {
        $user = User::factory()->create();

        $this->actingAsApiCustomer($user)
            ->postJson(route('api.products.checkout.store', ['product' => 'somethinginvalid']))
            ->assertNotFound();
    }

    #[Test]
    public function it_validates_the_gift_card_confirmation_request(): void
    {
        $user = User::factory()->create();
        $product = Product::factory()->create();

        $this->actingAs($user)
            ->postJson(route('api.products.checkout.store', ['product' => $product->slug]))
            ->assertUnprocessable()
            ->assertJsonFragment([
                'type' => ['The type field is required.'],
                'items' => ['The items field is required.'],
                'customer' => ['The customer field is required.'],
                'billing' => ['The billing field is required.'],
            ]);

        $this->actingAs($user)
            ->postJson(route('api.products.checkout.store', ['product' => $product->slug]), ['type' => 'something'])
            ->assertUnprocessable() // inactive date
            ->assertJsonFragment(['type' => ['The selected type is invalid.']]);

        $date = Date::factory()->create(['schedule_id' => Schedule::factory(), 'active' => false]);
        $this->actingAs($user)
            ->postJson(route('api.products.checkout.store', ['product' => $product->slug]), ['type' => 'gift-card'])
            ->assertUnprocessable() // inactive date
            ->assertJsonMissing([
                'date_id' => ['The selected date id is invalid.'],
                'shipping' => ['The shipping field is required.'],
            ]);

        $location = Pickup::factory()->create(['status_id' => 2]);
        $this->actingAs($user)
            ->postJson(route('api.products.checkout.store', ['product' => $product->slug]), ['type' => 'gift-card'])
            ->assertUnprocessable() // closed location
            ->assertJsonMissing(['delivery_method_id' => ['The selected delivery method id is invalid.']]);

        $this->actingAs($user)
            ->postJson(route('api.products.checkout.store', ['product' => $product->slug]), [
                'items' => 'foo',
                'customer' => 'foo',
                'shipping' => 'foo',
                'billing' => 'foo',
            ])
            ->assertUnprocessable()
            ->assertJsonFragment([
                'items' => ['The items field must be an array.'],
                'customer' => ['The customer field must be an array.'],
                'shipping' => ['The shipping field must be an array.'],
                'billing' => ['The billing field must be an array.'],
            ]);

        $this->actingAs($user)
            ->postJson(route('api.products.checkout.store', ['product' => $product->slug]), ['items' => []])
            ->assertUnprocessable()
            ->assertJsonFragment(['items' => ['The items field is required.']]);

        $this->actingAs($user)
            ->postJson(route('api.products.checkout.store', ['product' => $product->slug]), ['items' => [[]]])
            ->assertUnprocessable()
            ->assertJsonFragment([
                'items.0.product_id' => ['The items.0.product_id field is required.'],
                'items.0.quantity' => ['The items.0.quantity field is required.'],
            ]);

        $this->actingAs($user)
            ->postJson(route('api.products.checkout.store', ['product' => $product->slug]), ['items' => [[
                'product_id' => 'foo',
                'quantity' => 0,
            ]]])
            ->assertUnprocessable()
            ->assertJsonFragment([
                'items.0.product_id' => ['The selected items.0.product_id is invalid.'],
                'items.0.quantity' => ['The items.0.quantity field must be at least 1.'],
            ]);

        $this->actingAs($user)
            ->postJson(route('api.products.checkout.store', ['product' => $product->slug]), ['customer' => [
                'save_for_later' => 'foo',
                'opt_in_to_sms' => 'foo',
            ]])
            ->assertUnprocessable()
            ->assertJsonFragment([
                'customer.first_name' => ['The customer.first name field is required.'],
                'customer.last_name' => ['The customer.last name field is required.'],
                'customer.email' => ['The customer.email field is required.'],
                'customer.phone' => ['The customer.phone field is required.'],
                'customer.save_for_later' => ['The customer.save for later field must be true or false.'],
                'customer.opt_in_to_sms' => ['The customer.opt in to sms field must be true or false.'],
            ]);

        $method = Payment::factory()->create(['enabled' => false]);
        $this->actingAs($user)
            ->postJson(route('api.products.checkout.store', ['product' => $product->slug]), ['billing' => [
                'method' => $method->key,
                'save_for_later' => 'foo',
            ]])
            ->assertUnprocessable()
            ->assertJsonFragment([
                'billing.method' => ['The selected billing.method is invalid.'],
                'billing.save_for_later' => ['The billing.save for later field must be true or false.'],
            ]);

        $method = Payment::where('key', 'card')->first();
        $this->actingAs($user)
            ->postJson(route('api.products.checkout.store', ['product' => $product->slug]), ['billing' => [
                'method' => $method->key,
            ]])
            ->assertUnprocessable()
            ->assertJsonFragment([
                'billing.source_id' => ['The billing.source id field is required when billing.method is card.'],
            ]);

        $this->actingAs($user)
            ->postJson(route('api.products.checkout.store', ['product' => $product->slug]),[
                'notes' => Str::random(1001),
            ])
            ->assertUnprocessable()
            ->assertJsonFragment([
                'notes' => ['The notes field must not be greater than 1000 characters.'],
            ]);
    }

    #[Test]
    public function it_confirms_a_gift_card_purchase(): void
    {
        Event::fake([OrderWasConfirmed::class]);

        $user = User::factory()->create();
        $card = Card::factory()->create(['user_id' => $user->id]);

        $method = Payment::where('key', 'card')->first();
        $method->enabled = true;
        $method->save();
        $product = Product::factory()->create();

        $cart = [
            'type' => 'gift-card',
            'date_id' => null,
            'delivery_method_id' => null,
            'items' => [
                [
                    'product_id' => $product->id,
                    'quantity' => 2
                ]
            ],
            'notes' => 'These are my order notes.',
            'customer' => [
                'first_name' => 'First test',
                'last_name' => 'Last test',
                'email' => '<EMAIL>',
                'phone' => '************',
                'save_for_later' => true,
                'opt_in_to_sms' => true,
            ],
            'shipping' => [
                'street' => '',
                'street_2' => '',
                'city' => '',
                'state' => '',
                'zip' => '',
                'country' => '',
                'save_for_later' => false
            ],
            'billing' => [
                'method' => $method->key,
                'source_id' => $card->source_id,
                'save_for_later' => true
            ],
        ];

        $this->mock(ConfirmGiftCardPurchase::class, function (MockInterface $mock) use ($cart, $user) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(\Mockery::on(fn(User $arg) => $arg->id === $user->id), $cart)
                ->andReturn(Order::factory()->create());
        });

        $this->actingAs($user)
            ->postJson(route('api.products.checkout.store', ['product' => $product->slug]), $cart)
            ->assertCreated()
            ->assertJsonStructure(['order']);

        Event::assertDispatched(OrderWasConfirmed::class);
    }

    #[Test]
    public function it_does_not_confirm_purchase_when_there_is_a_billing_failure(): void
    {
        Event::fake([OrderWasConfirmed::class]);

        $user = User::factory()->create();
        $card = Card::factory()->create(['user_id' => $user->id]);

        $method = Payment::where('key', 'card')->first();
        $method->enabled = true;
        $method->save();
        $product = Product::factory()->create();

        $cart = [
            'type' => 'gift-card',
            'date_id' => null,
            'delivery_method_id' => null,
            'items' => [
                [
                    'product_id' => $product->id,
                    'quantity' => 2
                ]
            ],
            'notes' => 'These are my order notes.',
            'customer' => [
                'first_name' => 'First test',
                'last_name' => 'Last test',
                'email' => '<EMAIL>',
                'phone' => '************',
                'save_for_later' => true,
                'opt_in_to_sms' => true,
            ],
            'shipping' => [
                'street' => '',
                'street_2' => '',
                'city' => '',
                'state' => '',
                'zip' => '',
                'country' => '',
                'save_for_later' => false
            ],
            'billing' => [
                'method' => $method->key,
                'source_id' => $card->source_id,
                'save_for_later' => true
            ],
        ];

        $this->mock(ConfirmGiftCardPurchase::class, function (MockInterface $mock) use ($cart, $user) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(\Mockery::on(fn(User $arg) => $arg->id === $user->id), $cart)
                ->andThrow(new OrderChargeException('Could not do it.'));
        });

        $this->actingAs($user)
            ->postJson(route('api.products.checkout.store', ['product' => $product->slug]), $cart)
            ->assertStatus(400)
            ->assertJsonFragment(['error' => ['message' => 'Could not do it.']]);

        Event::assertNotDispatched(OrderWasConfirmed::class);
    }

    #[Test]
    public function it_confirms_a_gift_card_purchased_using_customer_credit(): void
    {
        Event::fake([OrderWasConfirmed::class]);

        $user = User::factory()->create(['credit' => 15000]);
        $card = Card::factory()->create(['user_id' => $user->id]);

        $product = Product::factory()->create([
            'schedule_id' => Schedule::factory(),
            'unit_price' => '100',
            'type_id' => ProductType::GIFT_CARD->value,
            'settings' => ['fulfilment_method' => 'virtual']
        ]);

        $method = Payment::where('key', 'card')->first();
        $method->enabled = true;
        $method->save();
        $cart = [
            'type' => 'gift-card',
            'date_id' => null,
            'delivery_method_id' => null,
            'items' => [
                [
                    'product_id' => $product->id,
                    'quantity' => 1
                ]
            ],
            'notes' => 'These are my order notes.',
            'customer' => [
                'first_name' => 'First test',
                'last_name' => 'Last test',
                'email' => '<EMAIL>',
                'phone' => '************',
                'save_for_later' => true,
                'opt_in_to_sms' => true,
            ],
            'shipping' => [
                'street' => '',
                'street_2' => '',
                'city' => '',
                'state' => '',
                'zip' => '',
                'country' => '',
                'save_for_later' => false
            ],
            'billing' => [
                'method' => $method->key,
                'source_id' => $card->source_id,
                'save_for_later' => true
            ],
        ];

        $this->mock(ConfirmGiftCardPurchase::class, function (MockInterface $mock) use ($cart, $user) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(\Mockery::on(fn(User $arg) => $arg->id === $user->id), $cart)
                ->andReturn(Order::factory()->create());
        });

        $this->actingAs($user)
            ->postJson(route('api.products.checkout.store', ['product' => $product->slug]), $cart)
            ->assertCreated()
            ->assertJsonStructure(['order'])
            ->assertSessionHas('orderWasConfirmed');

        Event::assertDispatched(OrderWasConfirmed::class);
    }
}
