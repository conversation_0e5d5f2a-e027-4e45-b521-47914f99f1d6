<?php

namespace Tests\Feature\API\Theme;

use App\Contracts\Geocoder;
use App\Exceptions\NoGeocodeResultsException;
use App\Models\Location;
use App\Models\Pickup;
use App\Repositories\DeliveryOptionRepository;
use Mo<PERSON>y\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class PickupsControllerTest extends TenantTestCase
{
    #[Test]
    public function it_validates_the_request(): void
    {
        $this->mock(
            Geocoder::class,
            fn(MockInterface $mock) => $mock->shouldNotReceive('fromZipcode')->shouldNotReceive('fromAddress')
        );

        // min zip length
        $this->getJson(route('api.theme.pickups.index', ['type' => 'market', 'zip' => '123']))
            ->assertUnprocessable()
            ->assertJsonValidationErrors(['zip' => 'The address you entered could not be found.']);

        // max zip length
        $this->getJson(route('api.theme.pickups.index', ['type' => 'market', 'zip' => '1234567890 abcdef']))
            ->assertUnprocessable()
            ->assertJsonValidationErrors(['zip' => 'The address you entered could not be found.']);

        // min address length
        $this->getJson(route('api.theme.pickups.index', ['type' => 'market', 'address' => '123']))
            ->assertUnprocessable()
            ->assertJsonValidationErrors(['address' => 'The address you entered could not be found.']);
    }

    #[Test]
    public function it_returns_no_results_when_no_results_exception_is_thrown_during_zip_geocoding_using_type_filter(): void
    {
        $this->mock(Geocoder::class, function (MockInterface $mock) {
            $mock->shouldReceive('fromZipcode')->once()->with('12345')
                ->andThrow(NoGeocodeResultsException::class);
        });

        $this->getJson(route('api.theme.pickups.index', ['type' => 'market', 'zip' => '12345']))
            ->assertUnprocessable()
            ->assertExactJson(['Sorry, no market locations near your address could be found.']);
    }

    #[Test]
    public function it_returns_no_results_when_no_results_exception_is_thrown_during_address_geocoding_using_type_filter(): void
    {
        $this->mock(Geocoder::class, function (MockInterface $mock) {
            $mock->shouldReceive('fromAddress')->once()->with('123 Main')
                ->andThrow(NoGeocodeResultsException::class);
        });

        $this->getJson(route('api.theme.pickups.index', ['type' => 'market', 'address' => '123 Main']))
            ->assertUnprocessable()
            ->assertExactJson(['Sorry, no market locations near your address could be found.']);
    }

    #[Test]
    public function it_can_fetch_market_locations(): void
    {
        $market = Location::factory()->create(['type' => 'market']);
        $restaurant = Location::factory()->create(['type' => 'restaurant']);
        $retail = Location::factory()->create(['type' => 'retail']);

        $response = $this->getJson(route('api.theme.pickups.index', ['type' => 'market']))
            ->assertOk()
            ->assertJsonStructure([
                'locations' => [
                    '*' => ['id']
                ],
                'zoom',
                'center'
            ])
            ->json();

        $locations = collect($response['locations']);
        $this->assertNotNull($locations->firstWhere('id', $market->id));
        $this->assertNull($locations->firstWhere('id', $restaurant->id));
        $this->assertNull($locations->firstWhere('id', $retail->id));

        foreach ($locations as $location) {
            $this->assertArrayNotHasKey('distance', $location);
        }
    }

    #[Test]
    public function it_sorts_market_locations_by_state_by_default(): void
    {
        $location_one = Location::factory()->create(['type' => 'market', 'state' => 'ZZ', 'visible' => true]);
        $location_two = Location::factory()->create(['type' => 'market', 'state' => 'AA', 'visible' => true]);
        $location_three = Location::factory()->create(['type' => 'market', 'state' => 'AA', 'visible' => false]);

        $response = $this->getJson(route('api.theme.pickups.index', ['type' => 'market']))
            ->assertOk()
            ->assertJsonStructure([
                'locations' => [
                    '*' => ['id']
                ],
                'zoom',
                'center'
            ])
            ->json();

        $locations = collect($response['locations']);

        $this->assertNotNull($locations->firstWhere('id', $location_one->id));
        $this->assertNotNull($locations->firstWhere('id', $location_two->id));
        $this->assertNull($locations->firstWhere('id', $location_three->id));

        // index of location two is lower
        $this->assertTrue(
            $locations->search(fn($location) => $location['id'] === $location_two->id)
            < $locations->search(fn($location) => $location['id'] === $location_one->id)
        );

        foreach ($locations as $location) {
            $this->assertArrayNotHasKey('distance', $location);
        }
    }

    #[Test]
    public function it_filters_hidden_market_locations_by_state_by_default(): void
    {
        $location_one = Location::factory()->create(['type' => 'market', 'visible' => false]);
        $location_two = Location::factory()->create(['type' => 'market', 'visible' => true]);

        $response = $this->getJson(route('api.theme.pickups.index', ['type' => 'market']))
            ->assertOk()
            ->assertJsonStructure([
                'locations' => [
                    '*' => ['id']
                ],
                'zoom',
                'center'
            ])
            ->json();

        $locations = collect($response['locations']);

        $this->assertNotNull($locations->firstWhere('id', $location_two->id));
        $this->assertNull($locations->firstWhere('id', $location_one->id));
    }

    #[Test]
    public function it_can_sort_market_locations_by_zip_distance(): void
    {
        $location_one = Location::factory()->create(['type' => 'market', 'visible' => true]);
        $location_two = Location::factory()->create(['type' => 'market', 'visible' => true]);
        $location_three = Location::factory()->create(['type' => 'market', 'visible' => false]);

        $response = $this->getJson(route('api.theme.pickups.index', ['type' => 'market', 'zip' => '12345']))
            ->assertOk()
            ->assertJsonStructure([
                'locations' => [
                     '*' => ['distance']
                ],
                'zoom',
                'center'
            ])
            ->json();

        $locations = collect($response['locations']);

        $this->assertNotNull($locations->firstWhere('id', $location_one->id));
        $this->assertNotNull($locations->firstWhere('id', $location_two->id));
        $this->assertNull($locations->firstWhere('id', $location_three->id));

        $distance = 0;

        foreach ($locations as $location) {
            $this->assertTrue($location['distance'] > $distance);
            $distance = $location['distance'];
        }
    }

    #[Test]
    public function it_can_sort_market_locations_by_address_distance(): void
    {
        $location_one = Location::factory()->create(['type' => 'market', 'visible' => true]);
        $location_two = Location::factory()->create(['type' => 'market', 'visible' => true]);
        $location_three = Location::factory()->create(['type' => 'market', 'visible' => false]);

        $response = $this->getJson(route('api.theme.pickups.index', ['type' => 'market', 'address' => '123 Main']))
            ->assertOk()
            ->assertJsonStructure([
                'locations' => [
                    '*' => ['distance']
                ],
                'zoom',
                'center'
            ])
            ->json();

        $locations = collect($response['locations']);

        $this->assertNotNull($locations->firstWhere('id', $location_one->id));
        $this->assertNotNull($locations->firstWhere('id', $location_two->id));
        $this->assertNull($locations->firstWhere('id', $location_three->id));


        $distance = 0;

        foreach ($locations as $location) {
            $this->assertTrue($location['distance'] > $distance);
            $distance = $location['distance'];
        }
    }

    #[Test]
    public function it_can_fetch_restaurant_locations(): void
    {
        $market = Location::factory()->create(['type' => 'market']);
        $restaurant = Location::factory()->create(['type' => 'restaurant']);
        $retail = Location::factory()->create(['type' => 'retail']);

        $response = $this->getJson(route('api.theme.pickups.index', ['type' => 'restaurant']))
            ->assertOk()
            ->assertJsonStructure([
                'locations',
                'zoom',
                'center'
            ])
            ->json();

        $locations = collect($response['locations']);
        $this->assertNull($locations->firstWhere('id', $market->id));
        $this->assertNotNull($locations->firstWhere('id', $restaurant->id));
        $this->assertNull($locations->firstWhere('id', $retail->id));

        foreach ($locations as $location) {
            $this->assertArrayNotHasKey('distance', $location);
        }
    }

    #[Test]
    public function it_sorts_restaurant_locations_by_state_by_default(): void
    {
        $location_one = Location::factory()->create(['type' => 'restaurant', 'state' => 'ZZ', 'visible' => true]);
        $location_two = Location::factory()->create(['type' => 'restaurant', 'state' => 'AA', 'visible' => true]);
        $location_three = Location::factory()->create(['type' => 'restaurant', 'state' => 'AA', 'visible' => false]);

        $response = $this->getJson(route('api.theme.pickups.index', ['type' => 'restaurant']))
            ->assertOk()
            ->assertJsonStructure([
                'locations' => [
                    '*' => ['id']
                ],
                'zoom',
                'center'
            ])
            ->json();

        $locations = collect($response['locations']);

        $this->assertNotNull($locations->firstWhere('id', $location_one->id));
        $this->assertNotNull($locations->firstWhere('id', $location_two->id));
        $this->assertNull($locations->firstWhere('id', $location_three->id));

        // index of location two is lower
        $this->assertTrue(
            $locations->search(fn($location) => $location['id'] === $location_two->id)
            < $locations->search(fn($location) => $location['id'] === $location_one->id)
        );

        foreach ($locations as $location) {
            $this->assertArrayNotHasKey('distance', $location);
        }
    }

    #[Test]
    public function it_filters_hidden_restaurant_locations_by_state_by_default(): void
    {
        $location_one = Location::factory()->create(['type' => 'restaurant', 'visible' => false]);
        $location_two = Location::factory()->create(['type' => 'restaurant', 'visible' => true]);

        $response = $this->getJson(route('api.theme.pickups.index', ['type' => 'restaurant']))
            ->assertOk()
            ->assertJsonStructure([
                'locations' => [
                    '*' => ['id']
                ],
                'zoom',
                'center'
            ])
            ->json();

        $locations = collect($response['locations']);

        $this->assertNotNull($locations->firstWhere('id', $location_two->id));
        $this->assertNull($locations->firstWhere('id', $location_one->id));
    }

    #[Test]
    public function it_can_sort_restaurant_locations_by_zip_distance(): void
    {
        $location_one = Location::factory()->create(['type' => 'restaurant']);
        $location_two = Location::factory()->create(['type' => 'restaurant']);

        $response = $this->getJson(route('api.theme.pickups.index', ['type' => 'restaurant', 'zip' => '12345']))
            ->assertOk()
            ->assertJsonStructure([
                'locations' => [
                    '*' => ['distance']
                ],
                'zoom',
                'center'
            ])
            ->json();

        $locations = collect($response['locations']);

        $this->assertNotNull($locations->firstWhere('id', $location_one->id));
        $this->assertNotNull($locations->firstWhere('id', $location_two->id));

        $distance = 0;

        foreach ($locations as $location) {
            $this->assertTrue($location['distance'] > $distance);
            $distance = $location['distance'];
        }
    }

    #[Test]
    public function it_can_sort_restaurant_locations_by_address_distance(): void
    {
        $location_one = Location::factory()->create(['type' => 'restaurant']);
        $location_two = Location::factory()->create(['type' => 'restaurant']);

        $response = $this->getJson(route('api.theme.pickups.index', ['type' => 'restaurant', 'address' => '123 Main']))
            ->assertOk()
            ->assertJsonStructure([
                'locations' => [
                    '*' => ['distance']
                ],
                'zoom',
                'center'
            ])
            ->json();

        $locations = collect($response['locations']);

        $this->assertNotNull($locations->firstWhere('id', $location_one->id));
        $this->assertNotNull($locations->firstWhere('id', $location_two->id));

        $distance = 0;

        foreach ($locations as $location) {
            $this->assertTrue($location['distance'] > $distance);
            $distance = $location['distance'];
        }
    }

    #[Test]
    public function it_filters_hidden_retail_locations_by_state_by_default(): void
    {
        $location_one = Location::factory()->create(['type' => 'retail', 'visible' => false]);
        $location_two = Location::factory()->create(['type' => 'retail', 'visible' => true]);

        $response = $this->getJson(route('api.theme.pickups.index', ['type' => 'retail']))
            ->assertOk()
            ->assertJsonStructure([
                'locations' => [
                    '*' => ['id']
                ],
                'zoom',
                'center'
            ])
            ->json();

        $locations = collect($response['locations']);

        $this->assertNotNull($locations->firstWhere('id', $location_two->id));
        $this->assertNull($locations->firstWhere('id', $location_one->id));
    }

    #[Test]
    public function it_can_fetch_retail_locations(): void
    {
        $market = Location::factory()->create(['type' => 'market']);
        $restaurant = Location::factory()->create(['type' => 'restaurant']);
        $retail = Location::factory()->create(['type' => 'retail']);

        $response = $this->getJson(route('api.theme.pickups.index', ['type' => 'retail']))
            ->assertOk()
            ->assertJsonStructure([
                'locations',
                'zoom',
                'center'
            ])
            ->json();

        $locations = collect($response['locations']);
        $this->assertNull($locations->firstWhere('id', $market->id));
        $this->assertNull($locations->firstWhere('id', $restaurant->id));
        $this->assertNotNull($locations->firstWhere('id', $retail->id));

        foreach ($locations as $location) {
            $this->assertArrayNotHasKey('distance', $location);
        }
    }

    #[Test]
    public function it_sorts_retail_locations_by_state_by_default(): void
    {
        $location_one = Location::factory()->create(['type' => 'retail', 'state' => 'ZZ', 'visible' => true]);
        $location_two = Location::factory()->create(['type' => 'retail', 'state' => 'AA', 'visible' => true]);
        $location_three = Location::factory()->create(['type' => 'retail', 'state' => 'AA', 'visible' => false]);

        $response = $this->getJson(route('api.theme.pickups.index', ['type' => 'retail']))
            ->assertOk()
            ->assertJsonStructure([
                'locations' => [
                    '*' => ['id']
                ],
                'zoom',
                'center'
            ])
            ->json();

        $locations = collect($response['locations']);

        $this->assertNotNull($locations->firstWhere('id', $location_one->id));
        $this->assertNotNull($locations->firstWhere('id', $location_two->id));
        $this->assertNull($locations->firstWhere('id', $location_three->id));


        // index of location two is lower
        $this->assertTrue(
            $locations->search(fn($location) => $location['id'] === $location_two->id)
            < $locations->search(fn($location) => $location['id'] === $location_one->id)
        );

        foreach ($locations as $location) {
            $this->assertArrayNotHasKey('distance', $location);
        }
    }

    #[Test]
    public function it_can_sort_retail_locations_by_zip_distance(): void
    {
        $location_one = Location::factory()->create(['type' => 'retail']);
        $location_two = Location::factory()->create(['type' => 'retail']);

        $response = $this->getJson(route('api.theme.pickups.index', ['type' => 'retail', 'zip' => '12345']))
            ->assertOk()
            ->assertJsonStructure([
                'locations' => [
                    '*' => ['distance']
                ],
                'zoom',
                'center'
            ])
            ->json();

        $locations = collect($response['locations']);

        $this->assertNotNull($locations->firstWhere('id', $location_one->id));
        $this->assertNotNull($locations->firstWhere('id', $location_two->id));

        $distance = 0;

        foreach ($locations as $location) {
            $this->assertTrue($location['distance'] > $distance);
            $distance = $location['distance'];
        }
    }

    #[Test]
    public function it_can_sort_retail_locations_by_address_distance(): void
    {
        $location_one = Location::factory()->create(['type' => 'retail']);
        $location_two = Location::factory()->create(['type' => 'retail']);

        $response = $this->getJson(route('api.theme.pickups.index', ['type' => 'retail', 'address' => '123 Main']))
            ->assertOk()
            ->assertJsonStructure([
                'locations' => [
                    '*' => ['distance']
                ],
                'zoom',
                'center'
            ])
            ->json();

        $locations = collect($response['locations']);

        $this->assertNotNull($locations->firstWhere('id', $location_one->id));
        $this->assertNotNull($locations->firstWhere('id', $location_two->id));

        $distance = 0;

        foreach ($locations as $location) {
            $this->assertTrue($location['distance'] > $distance);
            $distance = $location['distance'];
        }
    }

    #[Test]
    public function it_fetches_pickup_locations_by_default(): void
    {
        $location_one = Pickup::factory()->create(['visible' => true, 'fulfillment_type' => 1]);
        $location_two = Pickup::factory()->create(['visible' => true, 'fulfillment_type' => 2]);
        $location_three = Pickup::factory()->create(['visible' => false, 'fulfillment_type' => 1]);

        $response = $this->getJson(route('api.theme.pickups.index'))
            ->assertOk()
            ->assertJsonStructure([
                'address',
                'locations' => [
                    '*' => ['next_date']
                ],
                'zoom',
                'center',
                'closest',
                'out_of_range',
                'home_delivery',
                'zip',
            ])
            ->json();

        $locations = collect($response['locations']);
        $this->assertNotNull($locations->firstWhere('id', $location_one->id));
        $this->assertNull($locations->firstWhere('id', $location_two->id));
        $this->assertNull($locations->firstWhere('id', $location_three->id));

        foreach ($locations as $location) {
            $this->assertArrayNotHasKey('distance', $location);
        }
    }

    #[Test]
    public function it_sorts_pickup_locations_by_state_and_then_title_by_default(): void
    {
        $location_one = Pickup::factory()->create(['visible' => true, 'fulfillment_type' => 1, 'title' => 'ZZ', 'state' => 'AA']);
        $location_two = Pickup::factory()->create(['visible' => true, 'fulfillment_type' => 1, 'title' => 'AA', 'state' => 'ZZ']);
        $location_three = Pickup::factory()->create(['visible' => true, 'fulfillment_type' => 1, 'title' => 'ZZ', 'state' => 'ZZ']);
        $location_four = Pickup::factory()->create(['visible' => true, 'fulfillment_type' => 1, 'title' => 'AA', 'state' => 'AA']);

        $response = $this->getJson(route('api.theme.pickups.index'))
            ->assertOk()
            ->assertJsonStructure([
                'address',
                'locations' => [
                    '*' => ['next_date']
                ],
                'zoom',
                'center',
                'closest',
                'out_of_range',
                'home_delivery',
                'zip',
            ])
            ->json();

        $locations = collect($response['locations']);
        $this->assertNotNull($locations->firstWhere('id', $location_one->id));
        $this->assertNotNull($locations->firstWhere('id', $location_two->id));
        $this->assertNotNull($locations->firstWhere('id', $location_three->id));
        $this->assertNotNull($locations->firstWhere('id', $location_four->id));

        // correct order: four, one, two, three
        $this->assertTrue(
            $locations->search(fn($location) => $location['id'] === $location_four->id)
            < $locations->search(fn($location) => $location['id'] === $location_one->id)
        );

        $this->assertTrue(
            $locations->search(fn($location) => $location['id'] === $location_one->id)
            < $locations->search(fn($location) => $location['id'] === $location_two->id)
        );

        $this->assertTrue(
            $locations->search(fn($location) => $location['id'] === $location_two->id)
            < $locations->search(fn($location) => $location['id'] === $location_three->id)
        );

        foreach ($locations as $location) {
            $this->assertArrayNotHasKey('distance', $location);
        }
    }

    #[Test]
    public function it_sorts_pickup_locations_by_zip_distance(): void
    {
        $location_one = Pickup::factory()->create(['visible' => true, 'fulfillment_type' => 1]);
        $location_two = Pickup::factory()->create(['visible' => true, 'fulfillment_type' => 1]);
        $location_three = Pickup::factory()->create(['visible' => false, 'fulfillment_type' => 1]);

        $response = $this->getJson(route('api.theme.pickups.index', ['zip' => 12345]))
            ->assertOk()
            ->assertJsonStructure([
                'address',
                'locations' => [
                    '*' => ['next_date', 'distance']
                ],
                'zoom',
                'center',
                'closest',
                'out_of_range',
                'home_delivery',
                'zip',
            ])
            ->json();

        $locations = collect($response['locations']);
        $this->assertNotNull($locations->firstWhere('id', $location_one->id));
        $this->assertNotNull($locations->firstWhere('id', $location_two->id));
        $this->assertNull($locations->firstWhere('id', $location_three->id));

        $distance = 0;

        foreach ($locations as $location) {
            $this->assertTrue($location['distance'] > $distance);
            $distance = $location['distance'];
        }
    }

    #[Test]
    public function it_finds_closest_options_when_filtering_by_zip(): void
    {
        $this->mock(DeliveryOptionRepository::class, function (MockInterface $mock) {
            $mock->shouldReceive('findByZip')->once()->andReturnSelf();
            $mock->shouldReceive('findByState')->once()->andReturnSelf();
            $mock->shouldReceive('getOptions')->once()->andReturn(['delivery' => [1,2,3]]);
        });

        $this->getJson(route('api.theme.pickups.index', ['zip' => 12345]))
            ->assertOk()
            ->assertJsonFragment(['home_delivery' => [1,2,3],]);
    }

    #[Test]
    public function it_returns_no_results_when_no_results_exception_is_thrown_during_zip_geocoding(): void
    {
        $this->mock(Geocoder::class, function (MockInterface $mock) {
            $mock->shouldReceive('fromZipcode')->once()->with('12345')
                ->andThrow(NoGeocodeResultsException::class);
        });

        $this->getJson(route('api.theme.pickups.index', ['zip' => '12345']))
            ->assertStatus(422)
            ->assertExactJson(['The address you entered could not be found.']);
    }
}