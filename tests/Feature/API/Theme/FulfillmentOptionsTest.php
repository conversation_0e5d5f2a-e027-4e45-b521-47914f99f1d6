<?php

namespace Tests\Feature\API\Theme;

use App\Contracts\Geocoder;
use App\Models\Pickup;
use App\Models\PickupState;
use App\Models\PickupZip;
use App\Models\Setting;
use App\Services\Geocoding\GeocodedAddress;
use Mo<PERSON>y\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class FulfillmentOptionsTest extends TenantTestCase
{
    #[Test]
    public function it_can_fetch_locations_by_zipcode(): void
    {
        Setting::updateOrCreate(['key' => 'show_closest_zone'], ['value' => 1]);

        $pickups = Pickup::factory(2)->create();
        $deliveries = Pickup::factory(2)->delivery()->create(['title' => 'Delivery']);
        $deliveries->each(fn(Pickup $delivery) => $delivery->zips()->save(new PickupZip(['zip' => '12345'])));
        $deliveries->each(fn(Pickup $delivery) => $delivery->states()->save(new PickupState(['state' => 'TE'])));

        $this->mock(Geocoder::class, function (MockInterface $mock) {
            $mock->shouldReceive('fromZipcode')
                ->once()
                ->with('12345')
                ->andReturn(new GeocodedAddress(
                    lat: 123.45,
                    lng: 67.89,
                    city: 'Test',
                    state: 'TE',
                    postalCode: '12345',
                    country: 'USA',
                    accuracy: 1
                ));
        });

        $response = $this->getJson(route('api.theme.fulfillment-options.index', ['zip' => '12345']))
            ->assertOk()
            ->json();

        $this->assertArrayHasKey('closest', $response);
        $this->assertArrayHasKey('results', $response);
        $this->assertArrayHasKey('delivery', $response['results']);

        $this->assertArrayHasKey('address', $response);

        $this->assertCount(2, $response['results']['delivery']);
    }

    #[Test]
    public function it_can_fetch_all_delivery_locations_by_zipcode_when_configured(): void
    {
        Setting::updateOrCreate(['key' => 'show_closest_zone'], ['value' => 0]);

        $pickups = Pickup::factory(2)->create();
        $deliveries = Pickup::factory(2)->delivery()->create(['title' => 'Delivery']);
        $deliveries->each(fn(Pickup $delivery) => $delivery->zips()->save(new PickupZip(['zip' => '12345'])));
        $deliveries->each(fn(Pickup $delivery) => $delivery->states()->save(new PickupState(['state' => 'TE'])));

        $this->mock(Geocoder::class, function (MockInterface $mock) {
            $mock->shouldReceive('fromZipcode')
                ->once()
                ->with('12345')
                ->andReturn(new GeocodedAddress(
                    lat: 123.45,
                    lng: 67.89,
                    city: 'Test',
                    state: 'TE',
                    postalCode: '12345',
                    country: 'USA',
                    accuracy: 1
                ));
        });

        $response = $this->getJson(route('api.theme.fulfillment-options.index', ['zip' => '12345']))
            ->assertOk()
            ->json();

        $this->assertArrayHasKey('results', $response);
        $this->assertArrayHasKey('delivery', $response['results']);
        $this->assertCount(2, $response['results']['delivery']);
    }
}
