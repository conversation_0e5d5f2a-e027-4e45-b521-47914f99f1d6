<?php

namespace Tests\Feature\API\V1\Controllers;

use App\Models\ApiKey;
use App\Models\Product;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class ProductInventoryControllerTest extends TenantTestCase
{
    #[Test]
    public function it_validates_correctly_when_updating_the_product_inventory(): void
    {
        $apiKey = ApiKey::factory()->create([
            'scope' => [
                'inventory:update' => true
            ]
        ])->generateKey();

        $product = Product::factory()->create();

        $this->actingAsAdmin()
            ->putJson(route('products.inventory.update',
                [
                    'product' => $product,
                    'qty_on_hand' => 'qwerty',
                    'qty_at_processor' => 'qwerty',
                    'qty_off_site' => 'qwerty'
                ]), [
                'Authorization' => 'Bearer ' . $apiKey,
                'Accept' => 'application/json'
            ])
            ->assertUnprocessable()
            ->assertJsonFragment(['qty_on_hand' => ['The qty on hand field must be an integer.']])
            ->assertJsonFragment(['qty_at_processor' => ['The qty at processor field must be an integer.']])
            ->assertJsonFragment(['qty_off_site' => ['The qty off site field must be an integer.']]);
    }

    #[Test]
    public function it_updates_the_product_inventory(): void
    {
        $apiKey = ApiKey::factory()->create([
            'scope' => [
                'inventory:update' => true
            ]
        ])->generateKey();

        $product = Product::factory()->create();

        $this->actingAsAdmin()
            ->putJson(route('products.inventory.update',
                [
                    'product' => $product,
                    'qty_on_hand' => 50,
                    'qty_at_processor' => 10,
                    'qty_off_site' => 20
                ]), [
                'Authorization' => 'Bearer ' . $apiKey,
                'Accept' => 'application/json'
            ])
            ->assertOk();

        $this->assertDatabaseHas('products', [
            'id' => $product->id,
            'inventory' => 50,
            'processor_inventory' => 10,
            'other_inventory' => 20,
        ]);
    }

    #[Test]
    public function it_updates_the_product_inventory_using_sku(): void
    {
        $apiKey = ApiKey::factory()->create([
            'scope' => [
                'inventory:update' => true
            ]
        ])->generateKey();

        $product = Product::factory()->create(['sku' => 'productSku']);

        $this->actingAsAdmin()
            ->putJson(route('products.inventory.update',
                [
                    'product' => $product->sku,
                    'qty_on_hand' => 50,
                    'qty_at_processor' => 10,
                    'qty_off_site' => 20
                ]), [
                'Authorization' => 'Bearer ' . $apiKey,
                'Accept' => 'application/json'
            ])
            ->assertOk();

        $this->assertDatabaseHas('products', [
            'id' => $product->id,
            'sku' => 'productSku',
            'inventory' => 50,
            'processor_inventory' => 10,
            'other_inventory' => 20,
        ]);
    }
}