<?php

namespace Tests\Feature\API;

use App\Models\Coupon;
use Carbon\Carbon;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class CouponTest extends TenantTestCase
{
    #[Test]
    public function unauthenticated_users_cannot_fetch_coupons(): void
    {
        $this->getJson(route('api.coupons.index'))
            ->assertUnauthorized();
    }

    #[Test]
    public function non_admin_users_cannot_fetch_coupons(): void
    {
        $this->actingAsApiCustomer()
            ->getJson(route('api.coupons.index'))
            ->assertForbidden();
    }

    #[Test]
    public function admins_can_fetch_coupons(): void
    {
        $coupons = Coupon::factory()->times(2)->create();

        $response = $this->actingAsApiAdmin()
            ->getJson(route('api.coupons.index'))
            ->assertOk();

        foreach ($coupons as $coupon) {
            $response->assertJsonFragment(['id' => $coupon->id]);
        }
    }

    #[Test]
    public function unauthenticated_users_cannot_create_coupons(): void
    {
        $this->postJson(route('api.coupons.store'))
            ->assertUnauthorized();
    }

    #[Test]
    public function non_admin_users_cannot_create_coupons(): void
    {
        $this->actingAsApiCustomer()
            ->postJson(route('api.coupons.store'))
            ->assertForbidden();
    }

    #[Test]
    public function it_validates_the_coupon_creation_request(): void
    {
        $this->actingAsApiAdmin()
            ->postJson(route('api.coupons.store'))
            ->assertUnprocessable()
            ->assertInvalid([
                'discount_type' => 'The discount type field is required.',
                'application_type' => 'The application type field is required.',
                'code' => 'The code field is required.',
            ]);

        $coupon = Coupon::factory()->create(['code' => 'abc-123-def-456-ghi-789']);

        $this->postJson(route('api.coupons.store'), [
            'code' => $coupon->code
        ])
            ->assertUnprocessable()
            ->assertInvalid([
                'code' => [
                    'The code has already been taken.',
                    'The code field must not be greater than 15 characters.'
                ],
            ]);

        $this->postJson(route('api.coupons.store'), [
            'discount_type' => 'fixed'
        ])
            ->assertUnprocessable()
            ->assertInvalid([
                'discount_amount_formatted' => 'The discount amount field is required.'
            ]);

        $this->postJson(route('api.coupons.store'), [
            'discount_type' => 'fixed',
            'discount_amount_formatted' => 'a'
        ])
            ->assertUnprocessable()
            ->assertInvalid([
                'discount_amount_formatted' => [
                    'The discount amount field must be a number.'
                ]
            ]);

        $this->postJson(route('api.coupons.store'), [
            'discount_type' => 'fixed',
            'discount_amount_formatted' => -1.235
        ])
            ->assertUnprocessable()
            ->assertInvalid([
                'discount_amount_formatted' => 'The discount amount field must be at least 0.'
            ]);

        $this->postJson(route('api.coupons.store'), [
            'discount_type' => 'percentage'
        ])
            ->assertUnprocessable()
            ->assertInvalid([
                'discount_percentage' => 'The discount percentage field is required.'
            ]);

        $this->postJson(route('api.coupons.store'), [
            'discount_type' => 'percentage',
            'discount_percentage' => 'a'
        ])
            ->assertUnprocessable()
            ->assertInvalid([
                'discount_percentage' => [
                    'The discount percentage field must be an integer.'
                ]
            ]);

        $this->postJson(route('api.coupons.store'), [
            'discount_type' => 'percentage',
            'discount_percentage' => -1.235
        ])
            ->assertUnprocessable()
            ->assertInvalid([
                'discount_percentage' => 'The discount percentage field must be at least 1.'
            ]);

        $this->postJson(route('api.coupons.store'), [
            'discount_type' => 'percentage',
            'discount_percentage' => 100.1
        ])
            ->assertUnprocessable()
            ->assertInvalid([
                'discount_percentage' => 'The discount percentage field must be less than 100.'
            ]);

        $this->postJson(route('api.coupons.store'), [
            'min_order' => true
        ])
            ->assertUnprocessable()
            ->assertInvalid([
                'min_order_amount' => 'The minimum order amount field is required.'
            ]);

        $this->postJson(route('api.coupons.store'), [
            'min_order' => true,
            'min_order_amount' => 'a'
        ])
            ->assertUnprocessable()
            ->assertInvalid([
                'min_order_amount' => [
                    'The minimum order amount field must be a number.'
                ]
            ]);

        $this->postJson(route('api.coupons.store'), [
            'min_order' => true,
            'min_order_amount' => -1.235
        ])
            ->assertUnprocessable()
            ->assertInvalid([
                'min_order_amount' => 'The minimum order amount field must be at least 0.'
            ]);

        $this->postJson(route('api.coupons.store'), [
            'limit_usage' => true
        ])
            ->assertUnprocessable()
            ->assertInvalid([
                'max_uses' => 'The maximum usage limit field is required.'
            ]);

        $this->postJson(route('api.coupons.store'), [
            'limit_usage' => true,
            'max_uses' => 'a'
        ])
            ->assertUnprocessable()
            ->assertInvalid([
                'max_uses' => [
                    'The maximum usage limit field must be an integer.'
                ]
            ]);

        $this->postJson(route('api.coupons.store'), [
            'limit_usage' => true,
            'max_uses' => -1.235
        ])
            ->assertUnprocessable()
            ->assertInvalid([
                'max_uses' => 'The maximum usage limit field must be at least 1.'
            ]);

        $this->postJson(route('api.coupons.store'), [
            'expires' => true
        ])
            ->assertUnprocessable()
            ->assertInvalid([
                'expires_at' => 'The expires at field is required.'
            ]);

        $this->postJson(route('api.coupons.store'), [
            'expires' => true,
            'expires_at' => 'a'
        ])
            ->assertUnprocessable()
            ->assertInvalid([
                'expires_at' => [
                    'The expires at field must be a valid date.',
                    'The expires at field must match the format Y-m-d.'
                ]
            ]);

        $this->postJson(route('api.coupons.store'), [
            'settings' => 'a'
        ])
            ->assertUnprocessable()
            ->assertInvalid([
                'settings' => 'The settings field must be an array.'
            ]);

        $this->postJson(route('api.coupons.store'), [
            'expires' => false,
            'expires_at' => ''
        ])
            ->assertUnprocessable()
            ->assertValid('expires_at');
    }

    #[Test]
    public function it_creates_a_fixed_value_coupon(): void
    {
        $this->actingAsApiAdmin()
            ->postJson(route('api.coupons.store'), [
                'discount_type' => 'fixed',
                'application_type' => 'order',
                'code' => 'abc-123-456',
                'discount_amount_formatted' => 10.50,
                'min_order' => true,
                'min_order_amount' => 2,
                'limit_usage' => true,
                'max_uses' => 3,
                'expires' => true,
                'expires_at' => '2022-09-28',
                'settings' => ['foo' => 'bar'],
                'once_per_customer' => true,
            ])
            ->assertCreated()
            ->assertJsonFragment([
                'discount_type' => 'fixed',
                'application_type' => 'order',
                'code' => 'abc-123-456',
                'discount_amount' => 1050,
                'min_order' => true,
                'min_order_amount' => 200,
                'limit_usage' => true,
                'max_uses' => 3,
                'expires' => true,
                'settings' => ['foo' => 'bar'],
                'once_per_customer' => true,
            ]);

        $this->assertDatabaseHas(Coupon::class, [
            'discount_type' => 'fixed',
            'application_type' => 'order',
            'code' => 'abc-123-456',
            'discount_amount' => 1050,
            'min_order' => true,
            'min_order_amount' => 200,
            'limit_usage' => true,
            'max_uses' => 3,
            'expires' => true,
            'expires_at' => Carbon::parse('2022-09-28'),
            'settings' => json_encode(['foo' => 'bar']),
            'once_per_customer' => true,
        ]);
    }

    #[Test]
    public function it_creates_a_percentage_value_coupon(): void
    {
        $this->actingAsApiAdmin()
            ->postJson(route('api.coupons.store'), [
                'discount_type' => 'percentage',
                'application_type' => 'order',
                'code' => 'abc-123-456',
                'discount_percentage' => 11,
                'min_order' => true,
                'min_order_amount' => 2,
                'limit_usage' => true,
                'max_uses' => 3,
                'expires' => true,
                'expires_at' => '2022-09-28',
                'settings' => ['foo' => 'bar'],
                'once_per_customer' => true,
            ])
            ->assertCreated()
            ->assertJsonFragment([
                'discount_type' => 'percentage',
                'application_type' => 'order',
                'code' => 'abc-123-456',
                'discount_percentage' => 11,
                'min_order' => true,
                'min_order_amount' => 200,
                'limit_usage' => true,
                'max_uses' => 3,
                'expires' => true,
                'settings' => ['foo' => 'bar'],
                'once_per_customer' => true,
            ]);

        $this->assertDatabaseHas(Coupon::class, [
            'discount_type' => 'percentage',
            'application_type' => 'order',
            'code' => 'abc-123-456',
            'discount_percentage' => 11,
            'min_order' => true,
            'min_order_amount' => 200,
            'limit_usage' => true,
            'max_uses' => 3,
            'expires' => true,
            'expires_at' => Carbon::parse('2022-09-28'),
            'settings' => json_encode(['foo' => 'bar']),
            'once_per_customer' => true,
        ]);
    }

    #[Test]
    public function unauthenticated_users_cannot_update_coupons(): void
    {
        $coupon = Coupon::factory()->create();

        $this->putJson(route('api.coupons.update', compact('coupon')))
            ->assertUnauthorized();
    }

    #[Test]
    public function non_admin_users_cannot_update_coupons(): void
    {
        $coupon = Coupon::factory()->create();

        $this->actingAsApiCustomer()
            ->putJson(route('api.coupons.update', compact('coupon')))
            ->assertForbidden();
    }

    #[Test]
    public function admin_users_cannot_update_invalid_coupons(): void
    {
        $this->actingAsApiAdmin()
            ->putJson(route('api.coupons.update', ['coupon' => 123456789]))
            ->assertNotFound();
    }

    #[Test]
    public function it_validates_the_coupon_update_request(): void
    {
        $coupon = Coupon::factory()->create();

        $this->actingAsApiAdmin()
            ->putJson(route('api.coupons.update', compact('coupon')))
            ->assertUnprocessable()
            ->assertInvalid([
                'discount_type' => 'The discount type field is required.',
                'application_type' => 'The application type field is required.',
                'code' => 'The code field is required.',
            ]);

        $other_coupon = Coupon::factory()->create(['code' => 'abc-123-def-456-ghi-789']);

        $this->putJson(route('api.coupons.update', compact('coupon')), [
            'code' => $other_coupon->code
        ])
            ->assertUnprocessable()
            ->assertInvalid([
                'code' => [
                    'The code has already been taken.',
                    'The code field must not be greater than 15 characters.'
                ],
            ]);

        $this->putJson(route('api.coupons.update', compact('coupon')), [
            'discount_type' => 'fixed'
        ])
            ->assertUnprocessable()
            ->assertInvalid([
                'discount_amount_formatted' => 'The discount amount field is required.'
            ]);

        $this->putJson(route('api.coupons.update', compact('coupon')), [
            'discount_type' => 'fixed',
            'discount_amount_formatted' => 'a'
        ])
            ->assertUnprocessable()
            ->assertInvalid([
                'discount_amount_formatted' => [
                    'The discount amount field must be a number.'
                ]
            ]);

        $this->putJson(route('api.coupons.update', compact('coupon')), [
            'discount_type' => 'fixed',
            'discount_amount_formatted' => -1.235
        ])
            ->assertUnprocessable()
            ->assertInvalid([
                'discount_amount_formatted' => 'The discount amount field must be at least 0.'
            ]);

        $this->putJson(route('api.coupons.update', compact('coupon')), [
            'discount_type' => 'percentage'
        ])
            ->assertUnprocessable()
            ->assertInvalid([
                'discount_percentage' => 'The discount percentage field is required.'
            ]);

        $this->putJson(route('api.coupons.update', compact('coupon')), [
            'discount_type' => 'percentage',
            'discount_percentage' => 'a'
        ])
            ->assertUnprocessable()
            ->assertInvalid([
                'discount_percentage' => [
                    'The discount percentage field must be an integer.'
                ]
            ]);

        $this->putJson(route('api.coupons.update', compact('coupon')), [
            'discount_type' => 'percentage',
            'discount_percentage' => -1.235
        ])
            ->assertUnprocessable()
            ->assertInvalid([
                'discount_percentage' => 'The discount percentage field must be at least 1.'
            ]);

        $this->putJson(route('api.coupons.update', compact('coupon')), [
            'discount_type' => 'percentage',
            'discount_percentage' => 100.1
        ])
            ->assertUnprocessable()
            ->assertInvalid([
                'discount_percentage' => 'The discount percentage field must be less than 100.'
            ]);

        $this->putJson(route('api.coupons.update', compact('coupon')), [
            'min_order' => true
        ])
            ->assertUnprocessable()
            ->assertInvalid([
                'min_order_amount_formatted' => 'The minimum order amount field is required.'
            ]);

        $this->putJson(route('api.coupons.update', compact('coupon')), [
            'min_order' => true,
            'min_order_amount_formatted' => 'a'
        ])
            ->assertUnprocessable()
            ->assertInvalid([
                'min_order_amount_formatted' => [
                    'The minimum order amount field must be a number.'
                ]
            ]);

        $this->putJson(route('api.coupons.update', compact('coupon')), [
            'min_order' => true,
            'min_order_amount_formatted' => -1.235
        ])
            ->assertUnprocessable()
            ->assertInvalid([
                'min_order_amount_formatted' => 'The minimum order amount field must be at least 0.'
            ]);

        $this->putJson(route('api.coupons.update', compact('coupon')), [
            'limit_usage' => true
        ])
            ->assertUnprocessable()
            ->assertInvalid([
                'max_uses' => 'The maximum usage limit field is required.'
            ]);

        $this->putJson(route('api.coupons.update', compact('coupon')), [
            'limit_usage' => true,
            'max_uses' => 'a'
        ])
            ->assertUnprocessable()
            ->assertInvalid([
                'max_uses' => [
                    'The maximum usage limit field must be an integer.'
                ]
            ]);

        $this->putJson(route('api.coupons.update', compact('coupon')), [
            'limit_usage' => true,
            'max_uses' => -1.235
        ])
            ->assertUnprocessable()
            ->assertInvalid([
                'max_uses' => 'The maximum usage limit field must be at least 1.'
            ]);

        $this->putJson(route('api.coupons.update', compact('coupon')), [
            'expires' => true
        ])
            ->assertUnprocessable()
            ->assertInvalid([
                'expires_at_formatted' => 'The expires at field is required.'
            ]);

        $this->putJson(route('api.coupons.update', compact('coupon')), [
            'expires' => true,
            'expires_at_formatted' => 'a'
        ])
            ->assertUnprocessable()
            ->assertInvalid([
                'expires_at_formatted' => [
                    'The expires at field must be a valid date.',
                    'The expires at field must match the format Y-m-d.'
                ]
            ]);

        $this->putJson(route('api.coupons.update', compact('coupon')), [
            'settings' => 'a'
        ])
            ->assertUnprocessable()
            ->assertInvalid([
                'settings' => 'The settings field must be an array.'
            ]);

        $this->putJson(route('api.coupons.update', compact('coupon')), [
            'expires' => false,
            'expires_at' => ''
        ])
            ->assertUnprocessable()
            ->assertValid('expires_at');

        $this->putJson(route('api.coupons.update', compact('coupon')), [
            'discount_type' => 'fixed',
            'discount_percentage' => 0,
        ])
            ->assertUnprocessable()
            ->assertValid('discount_percentage');
    }

    #[Test]
    public function it_can_update_a_coupon(): void
    {
        $coupon = Coupon::factory()->create();

        $this->actingAsApiAdmin()
            ->putJson(route('api.coupons.update', compact('coupon')), [
                'discount_type' => 'percentage',
                'application_type' => 'order',
                'code' => 'abc-123-456',
                'discount_percentage' => 11,
                'min_order' => true,
                'min_order_amount_formatted' => 2,
                'limit_usage' => true,
                'max_uses' => 3,
                'expires' => true,
                'expires_at_formatted' => '2022-09-28',
                'settings' => ['foo' => 'bar'],
                'once_per_customer' => true,
            ])
            ->assertOk()
            ->assertJsonFragment([
                'id' => $coupon->id,
                'discount_type' => 'percentage',
                'application_type' => 'order',
                'code' => 'abc-123-456',
                'discount_percentage' => 11,
                'min_order' => true,
                'min_order_amount' => 200,
                'limit_usage' => true,
                'max_uses' => 3,
                'expires' => true,
                'settings' => ['foo' => 'bar'],
                'once_per_customer' => true,
            ]);

        $this->assertDatabaseHas(Coupon::class, [
            'id' => $coupon->id,
            'discount_type' => 'percentage',
            'application_type' => 'order',
            'code' => 'abc-123-456',
            'discount_percentage' => 11,
            'min_order' => true,
            'min_order_amount' => 200,
            'limit_usage' => true,
            'max_uses' => 3,
            'expires' => true,
            'expires_at' => Carbon::parse('2022-09-28'),
            'settings' => json_encode(['foo' => 'bar']),
            'once_per_customer' => true,
        ]);
    }

    #[Test]
    public function unauthenticated_users_cannot_destroy_coupons(): void
    {
        $coupon = Coupon::factory()->create();

        $this->deleteJson(route('api.coupons.destroy', compact('coupon')))
            ->assertUnauthorized();
    }

    #[Test]
    public function non_admin_users_cannot_destroy_coupons(): void
    {
        $coupon = Coupon::factory()->create();

        $this->actingAsApiCustomer()
            ->deleteJson(route('api.coupons.destroy', compact('coupon')))
            ->assertForbidden();
    }

    #[Test]
    public function admin_users_cannot_destroy_invalid_coupons(): void
    {
        $this->actingAsApiAdmin()
            ->deleteJson(route('api.coupons.destroy', [123456789]))
            ->assertNotFound();
    }

    #[Test]
    public function admin_users_can_destroy_coupons(): void
    {
        $coupon = Coupon::factory()->create();

        $this->actingAsApiAdmin()
            ->deleteJson(route('api.coupons.destroy', compact('coupon')))
            ->assertNoContent();

        $this->assertDatabaseMissing(Coupon::class, ['id' => $coupon->id]);
    }
}
