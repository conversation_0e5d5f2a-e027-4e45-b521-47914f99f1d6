<?php

namespace Tests\Feature\API;

use App\Models\Detour;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class DetourTest extends TenantTestCase
{
    #[Test]
    public function unauthenticated_users_cannot_fetch_detours(): void
    {
        $this->getJson(route('api.detours.index'))
            ->assertUnauthorized();
    }

    #[Test]
    public function non_admin_users_cannot_fetch_detours(): void
    {
        $this->actingAsApiCustomer()
            ->getJson(route('api.detours.index'))
            ->assertForbidden();
    }

    #[Test]
    public function admins_can_fetch_detours(): void
    {
        $detours = Detour::factory()->times(2)->create();

        $response = $this->actingAsApiAdmin()
            ->getJson(route('api.detours.index'))
            ->assertOk();

        foreach ($detours as $detour) {
            $response->assertJsonFragment(['id' => $detour->id]);
        }
    }

    #[Test]
    public function unauthenticated_users_cannot_create_detours(): void
    {
        $this->postJson(route('api.detours.store'))
            ->assertUnauthorized();
    }

    #[Test]
    public function non_admin_users_cannot_create_detours(): void
    {
        $this->actingAsApiCustomer()
            ->postJson(route('api.detours.store'))
            ->assertForbidden();
    }

    #[Test]
    public function it_validates_the_detour_creation_request(): void
    {
        $this->actingAsApiAdmin()
            ->postJson(route('api.detours.store'))
            ->assertUnprocessable()
            ->assertInvalid([
                'from_url' => 'The origin url field is required.',
                'to_url' => 'The destination url field is required.',
                'status_code' => 'The status code field is required.',
            ]);

        $detour = Detour::factory()->create(['from_url' => 'http://example.test']);

        $this->postJson(route('api.detours.store'), [
            'from_url' => $detour->from_url
        ])
            ->assertUnprocessable()
            ->assertInvalid([
                'from_url' => 'The origin url field has already been taken.',
            ]);

        $this->postJson(route('api.detours.store'), [
            'to_url' => $detour->from_url
        ])
            ->assertUnprocessable()
            ->assertInvalid([
                'to_url' => 'The destination url field cannot be the same as an existing origin url.',
            ]);

        $this->postJson(route('api.detours.store'), [
            'from_url' => 'http://example.test/url',
            'to_url' => 'http://example.test/url'
        ])
            ->assertUnprocessable()
            ->assertInvalid([
                'to_url' => 'The origin url field must be different from the destination url.',
            ]);

        $this->postJson(route('api.detours.store'), [
            'status_code' => 'abc'
        ])
            ->assertUnprocessable()
            ->assertInvalid([
                'status_code' => [
                    'The status code field must be an integer.',
                    'The status code field must be at least 301.'
                ],
            ]);

        $this->postJson(route('api.detours.store'), [
            'status_code' => 303
        ])
            ->assertUnprocessable()
            ->assertInvalid([
                'status_code' => 'The status code field must not be greater than 302.'
            ]);
    }

    #[Test]
    public function it_creates_a_detour(): void
    {
        $this->actingAsApiAdmin()
            ->postJson(route('api.detours.store'), [
                'from_url' => 'http://example.test/url-one',
                'to_url' => 'http://example.test/url-two',
                'status_code' => 301
            ])
            ->assertCreated()
            ->assertJsonFragment([
                'from_url' => 'http://example.test/url-one',
                'to_url' => 'http://example.test/url-two',
                'status_code' => 301
            ]);

        $this->assertDatabaseHas(Detour::class, [
            'from_url' => 'http://example.test/url-one',
            'to_url' => 'http://example.test/url-two',
            'status_code' => 301
        ]);
    }

    #[Test]
    public function unauthenticated_users_cannot_update_detours(): void
    {
        $detour = Detour::factory()->create();

        $this->putJson(route('api.detours.update', compact('detour')))
            ->assertUnauthorized();
    }

    #[Test]
    public function non_admin_users_cannot_update_detours(): void
    {
        $detour = Detour::factory()->create();

        $this->actingAsApiCustomer()
            ->putJson(route('api.detours.update', compact('detour')))
            ->assertForbidden();
    }

    #[Test]
    public function admin_users_cannot_update_invalid_detours(): void
    {
        $this->actingAsApiAdmin()
            ->putJson(route('api.detours.update', [123456789]))
            ->assertNotFound();
    }

    #[Test]
    public function it_validates_the_detour_update_request(): void
    {
        $detour = Detour::factory()->create();

        $this->actingAsApiAdmin()
            ->putJson(route('api.detours.update', compact('detour')))
            ->assertUnprocessable()
            ->assertInvalid([
                'from_url' => 'The origin url field is required.',
                'to_url' => 'The destination url field is required.',
                'status_code' => 'The status code field is required.',
            ]);

        $other_detour = Detour::factory()->create();

        $this->putJson(route('api.detours.update', compact('detour')), [
            'from_url' => $other_detour->from_url
        ])
            ->assertUnprocessable()
            ->assertInvalid([
                'from_url' => 'The origin url field has already been taken.',
            ]);

        $this->putJson(route('api.detours.update', compact('detour')), [
            'to_url' => $other_detour->from_url
        ])
            ->assertUnprocessable()
            ->assertInvalid([
                'to_url' => 'The destination url field cannot be the same as an existing origin url.',
            ]);

        $this->putJson(route('api.detours.update', compact('detour')), [
            'from_url' => 'http://example.test/url',
            'to_url' => 'http://example.test/url'
        ])
            ->assertUnprocessable()
            ->assertInvalid([
                'to_url' => 'The origin url field must be different from the destination url.',
            ]);

        $this->putJson(route('api.detours.update', compact('detour')), [
            'status_code' => 'abc'
        ])
            ->assertUnprocessable()
            ->assertInvalid([
                'status_code' => [
                    'The status code field must be an integer.',
                    'The status code field must be at least 301.'
                ],
            ]);

        $this->putJson(route('api.detours.update', compact('detour')), [
            'status_code' => 303
        ])
            ->assertUnprocessable()
            ->assertInvalid([
                'status_code' => 'The status code field must not be greater than 302.'
            ]);
    }

    #[Test]
    public function it_can_update_a_detour(): void
    {
        $detour = Detour::factory()->create();

        $this->actingAsApiAdmin()
            ->putJson(route('api.detours.update', compact('detour')), [
                'from_url' => 'http://example.test/url-one',
                'to_url' => 'http://example.test/url-two',
                'status_code' => 301
            ])
            ->assertOk()
            ->assertJsonFragment([
                'from_url' => 'http://example.test/url-one',
                'to_url' => 'http://example.test/url-two',
                'status_code' => 301
            ]);

        $this->assertDatabaseHas(Detour::class, [
            'id' => $detour->id,
            'from_url' => 'http://example.test/url-one',
            'to_url' => 'http://example.test/url-two',
            'status_code' => 301
        ]);
    }

    #[Test]
    public function unauthenticated_users_cannot_destroy_detours(): void
    {
        $detour = Detour::factory()->create();

        $this->deleteJson(route('api.detours.destroy', compact('detour')))
            ->assertUnauthorized();
    }

    #[Test]
    public function non_admin_users_cannot_destroy_detours(): void
    {
        $detour = Detour::factory()->create();

        $this->actingAsApiCustomer()
            ->deleteJson(route('api.detours.destroy', compact('detour')))
            ->assertForbidden();
    }

    #[Test]
    public function admin_users_cannot_destroy_invalid_detours(): void
    {
        $this->actingAsApiAdmin()
            ->deleteJson(route('api.detours.destroy', [123456789]))
            ->assertNotFound();
    }

    #[Test]
    public function admin_users_can_destroy_detours(): void
    {
        $detour = Detour::factory()->create();

        $this->actingAsApiAdmin()
            ->deleteJson(route('api.detours.destroy', compact('detour')))
            ->assertNoContent();

        $this->assertDatabaseMissing(Detour::class, ['id' => $detour->id]);
    }
}