<?php

namespace Tests\Feature\API\V2;

use App\Support\Enums\Channel;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class SalesChannelTest extends TenantTestCase
{
    #[Test]
    public function unauthorised_requests_cannot_fetch_sales_channels(): void
    {
        $this->getJson(route('api.v2.sales-channels.index'))
            ->assertUnauthorized();
    }

    #[Test]
    public function it_successfully_returns_all_sales_channels(): void
    {
        $this->actingAsSanctumUser()
            ->getJson(route('api.v2.sales-channels.index'))
            ->assertOk()
            ->assertJsonCount(9, 'data')
            ->assertJsonFragment(['id' => 9, 'title' => Channel::get(9)])
            ->assertJsonFragment(['id' => 3, 'title' => Channel::get(3)]);
    }
}
