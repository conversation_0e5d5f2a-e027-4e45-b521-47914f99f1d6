<?php

namespace Tests\Feature\API\V2;

use App\Models\RecurringOrder;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class SubscriptionTest extends TenantTestCase
{
    #[Test]
    public function unauthorized_requests_cannot_fetch_subscription_list(): void
    {
        $this->getJson(route('api.v2.subscriptions.index'))
            ->assertUnauthorized();
    }

    #[Test]
    public function it_successfully_returns_subscription_list(): void
    {
        $current_count = RecurringOrder::count();

        RecurringOrder::factory(2)->create();

        $this->actingAsSanctumUser()
            ->getJson(route('api.v2.subscriptions.index'))
            ->assertOk()
            ->assertJsonCount($current_count + 2,'data');
    }

    #[Test]
    public function it_successfully_returns_a_specific_subscription(): void
    {
        $subscription = RecurringOrder::factory()->create();

        $this->actingAsSanctumUser()
            ->getJson(route('api.v2.subscriptions.show', compact('subscription')))
            ->assertOk()
            ->assertJsonStructure([
                'data' => [
                    'id',
                    'discount_percentage',
                ],
            ])
            ->assertJsonFragment([
                'id' => $subscription->id,
                'reorder_frequency' => $subscription->reorder_frequency,
            ]);
    }
}
