<?php

namespace Tests\Feature\Livewire\Admin;

use App\Livewire\Admin\BundleStoreTemplate;
use App\Models\Product;
use Livewire\Livewire;
use PHPUnit\Framework\Attributes\Test;
use Tests\LivewireTenantTestCase;

class BundleStoreTemplateTest extends LivewireTenantTestCase
{
    #[Test]
    public function it_renders_successfully()
    {
        $product_id = Product::factory()->create()->id;

        Livewire::test(BundleStoreTemplate::class, compact('product_id'))
            ->assertSee('Bundle Contents')
            ->assertSee('Heading')
            ->assertSee('Subheading')
            ->assertSee('Video Embed')
            ->assertSee('Farming Practices')
            ->assertSee('Related Products')
            ->assertSee('Related Recipes');
    }

    #[Test]
    public function it_validates_data_before_saving()
    {
        $product = Product::factory()->create();

        Livewire::test(BundleStoreTemplate::class, ['product_id' => $product->id])
            ->set('heading', null)
            ->set('subheading', null)
            ->set('video_embed', null)
            ->set('farming_practices', 'invalid_practice')
            ->set('related_products', null)
            ->set('related_recipes', null)
            ->set('contents_description', 2222)
            ->call('save')
            ->assertHasErrors([
                'farming_practices' => 'in',
                'contents_description' => 'string',
            ]);
    }

    #[Test]
    public function it_saves_valid_data()
    {
        $product = Product::factory()->create(['settings' => [
            'store_template' => 'bundle',
            'standard_template' => ['heading' => 'Standard heading'],
        ]]);

        Livewire::test(BundleStoreTemplate::class, ['product_id' => $product->id])
            ->set('contents_description', 'New description')
            ->set('heading', 'New heading')
            ->set('subheading', 'New subheading')
            ->set('video_embed', 'New embed')
            ->set('related_products', 'New products')
            ->set('related_recipes', 'New recipes')
            ->call('save');

        $product->refresh();

        $this->assertDatabaseHas('products', [
            'id' => $product->id,
            'settings->bundle_template->contents_description' => 'New description',
            'settings->bundle_template->heading' => 'New heading',
            'settings->bundle_template->subheading' => 'New subheading',
            'settings->bundle_template->video_embed' => 'New embed',
            'settings->bundle_template->related_products' => 'New products',
            'settings->bundle_template->related_recipes' => 'New recipes',
            'settings->bundle_template->farming_practices' => 'mixed',
            'settings->standard_template->heading' => 'Standard heading',
        ]);
    }

    #[Test]
    public function it_mounts_with_correct_data()
    {
        $product = Product::factory()->create([
            'settings' => [
                'bundle_template' => [
                    'contents_description' => 'Description',
                    'heading' => 'Heading',
                    'subheading' => 'Subheading',
                    'video_embed' => 'Embed',
                    'farming_practices' => 'beef',
                    'related_products' => 'Products',
                    'related_recipes' => 'Recipes',
                ],
            ],
        ]);

        Livewire::test(BundleStoreTemplate::class, ['product_id' => $product->id])
            ->assertSet('contents_description', 'Description')
            ->assertSet('heading', 'Heading')
            ->assertSet('subheading', 'Subheading')
            ->assertSet('video_embed', 'Embed')
            ->assertSet('farming_practices', 'beef')
            ->assertSet('related_products', 'Products')
            ->assertSet('related_recipes', 'Recipes');
    }
}
