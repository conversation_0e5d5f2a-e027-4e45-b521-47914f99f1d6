<?php

namespace Tests\Feature\Livewire\Admin\Pages\Widgets;

use App\Livewire\Admin\Pages\Widgets\Cta;
use App\Models\Page;
use Livewire\Livewire;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class CtaTest extends TenantTestCase
{
    #[Test]
    public function it_renders_successfully(): void
    {
        Livewire::test(Cta::class, [
            'page_id' => 1,
            'widget' => [
                'id' => 'abc-def',
                'type' => 'cta',
                'settings' => [
                    'name' => 'CTA Widget Name',
                    'html_id' => 'test-cta',
                    'max_width' => 'none',
                    'padding' => [
                        'top' => 'sm',
                        'bottom' => 'sm'
                    ],
                    'heading' => [
                        'image' => 'https://example.com/heading.jpg',
                        'max_width' => 'none',
                    ],
                    'button_size' => 'md',
                    'ctas' => [
                        [
                            'label' => 'Call to Action',
                            'url' => 'https://example.com/cta',
                            'style' => 'brand',
                        ],
                        [
                            'label' => 'Call to Action 2',
                            'url' => 'https://example.com/cta_2',
                            'style' => 'action',
                        ]
                    ]
                ]
            ]
        ])
            ->assertStatus(200)
            ->assertSet('name', 'CTA Widget Name')
            ->assertSet('html_id', 'test-cta')
            ->assertSet('padding_top', 'sm')
            ->assertSet('padding_bottom', 'sm')
            ->assertSet('max_width', 'none')
            ->assertSet('heading_image', 'https://example.com/heading.jpg')
            ->assertSet('heading_max_width', 'none')
            ->assertSet('button_size', 'md')
            ->assertSet('ctas', [
                [
                    'label' => 'Call to Action',
                    'url' => 'https://example.com/cta',
                    'style' => 'brand',
                ],
                [
                    'label' => 'Call to Action 2',
                    'url' => 'https://example.com/cta_2',
                    'style' => 'action',
                ]
            ]);
    }

    #[Test]
    public function it_can_update_the_widget_settings(): void
    {
        $widget = [
            'id' => 'abc-def',
            'type' => 'cta',
            'settings' => [
                'name' => 'CTA Widget Name',
                'html_id' => 'test-cta',
                'max_width' => 'none',
                'padding' => [
                    'top' => 'sm',
                    'bottom' => 'sm'
                ],
                'heading' => [
                    'image' => 'https://example.com/heading.jpg',
                    'max_width' => 'none',
                ],
                'button_size' => 'md',
                'ctas' => [
                    [
                        'label' => 'Call to Action',
                        'url' => 'https://example.com/cta',
                        'style' => 'brand',
                    ],
                    [
                        'label' => 'Call to Action 2',
                        'url' => 'https://example.com/cta_2',
                        'style' => 'action',
                    ]
                ]
            ]
        ];

        $page = Page::factory()->create([
            'settings' => [
                'type' => 'promo',
                'content' => [$widget]
            ]
        ]);

        Livewire::test(Cta::class, [
            'page_id' => $page->id,
            'widget' => $widget,
        ])
            ->assertStatus(200)
            ->set('name', 'CTA Widget Name 2')
            ->set('html_id', 'abc')
            ->set('max_width', 'lg')
            ->set('padding_top', 'lg')
            ->set('padding_bottom', 'lg')
            ->set('heading_image', 'https://example.com/new-heading.jpg')
            ->set('heading_max_width', 'lg')
            ->set('button_size', 'lg')
            ->set('ctas', [
                [
                    'label' => 'New Call to Action',
                    'url' => 'https://example.com/new_cta',
                    'style' => 'transparent',
                ],
                [
                    'label' => 'New Call to Action 2',
                    'url' => 'https://example.com/new_cta_2',
                    'style' => 'white',
                ]
            ])
            ->call('save')
            ->assertHasNoErrors();

        $this->assertDatabaseHas(Page::class, [
            'id' => $page->id,
            'settings' => json_encode([
                'type' => 'promo',
                'content' => [
                    [
                        'id' => 'abc-def',
                        'type' => 'cta',
                        'settings' => [
                            'name' => 'CTA Widget Name 2',
                            'html_id' => 'abc',
                            'max_width' => 'lg',
                            'padding' => [
                                'top' => 'lg',
                                'bottom' => 'lg'
                            ],
                            'heading' => [
                                'image' => 'https://example.com/new-heading.jpg',
                                'max_width' => 'lg',
                            ],
                            'button_size' => 'lg',
                            'ctas' => [
                                [
                                    'label' => 'New Call to Action',
                                    'url' => 'https://example.com/new_cta',
                                    'style' => 'transparent',
                                ],
                                [
                                    'label' => 'New Call to Action 2',
                                    'url' => 'https://example.com/new_cta_2',
                                    'style' => 'white',
                                ]
                            ]
                        ],
                    ]
                ]
            ])
        ]);
    }

    #[Test]
    public function it_dispatches_an_event_on_successful_save(): void
    {
        $widget = [
            'id' => 'abc-def',
            'type' => 'cta',
            'settings' => [
                'name' => 'CTA Widget Name',
                'html_id' => 'test-cta',
                'max_width' => 'none',
                'padding' => [
                    'top' => 'sm',
                    'bottom' => 'sm'
                ],
                'heading' => [
                    'image' => 'https://example.com/heading.jpg',
                    'max_width' => 'none',
                ],
                'button_size' => 'md',
                'ctas' => [
                    [
                        'label' => 'Call to Action',
                        'url' => 'https://example.com/cta',
                        'style' => 'brand',
                    ],
                    [
                        'label' => 'Call to Action 2',
                        'url' => 'https://example.com/cta_2',
                        'style' => 'action',
                    ]
                ]
            ]
        ];

        $page = Page::factory()->create([
            'settings' => [
                'type' => 'promo',
                'content' => [$widget]
            ]
        ]);

        Livewire::test(Cta::class, [
            'page_id' => $page->id,
            'widget' => $widget,
        ])
            ->assertStatus(200)
            ->call('save')
            ->assertHasNoErrors()
            ->assertDispatched('admin-notification-sent', notification: [
                'level' => 'success',
                'title' => 'Widget updated!',
                'message' => 'The widget settings have been successfully updated.',
            ]);
    }
}
