<?php

namespace Tests\Feature\Livewire\Admin;

use App\Livewire\Admin\OrderInfoBoxes;
use App\Models\Order;
use Livewire\Livewire;
use PHPUnit\Framework\Attributes\Test;
use Tests\LivewireTenantTestCase;

class OrderInfoBoxesTest extends LivewireTenantTestCase
{
    #[Test]
    public function it_can_render(): void
    {
        $order = Order::factory()->create(['paid' => false]);

        Livewire::test(OrderInfoBoxes::class, compact('order'))
            ->assertStatus(200)
            ->assertViewIs('livewire.order-info-boxes');
    }

    #[Test]
    public function it_refreshes_when_the_order_updated_event_is_fired(): void
    {
        $order = Order::factory()->create(['paid' => false]);

        $component = Livewire::test(OrderInfoBoxes::class, compact('order'))
            ->assertStatus(200)
            ->assertViewIs('livewire.order-info-boxes')
            ->assertSee('Not Paid');

        $order->update(['paid' => true]);

        $component->dispatch('order-updated', order_id: $order->id)
            ->assertSee('Paid')
            ->assertDontSee('Not Paid');
    }
}
