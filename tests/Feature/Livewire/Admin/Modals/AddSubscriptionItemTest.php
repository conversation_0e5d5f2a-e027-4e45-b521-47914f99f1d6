<?php

namespace Tests\Feature\Livewire\Admin\Modals;

use App\Livewire\Admin\Modals\AddSubscriptionItem;
use App\Models\Product;
use App\Models\RecurringOrder;
use App\Models\RecurringOrderItem;
use Livewire\Livewire;
use PHPUnit\Framework\Attributes\Test;
use Tests\LivewireTenantTestCase;

class AddSubscriptionItemTest extends LivewireTenantTestCase
{
    #[Test]
    public function the_component_can_render(): void
    {
        $subscription = RecurringOrder::factory()->create();

        Livewire::test(AddSubscriptionItem::class)
            ->dispatch('open-modal-add-subscription-item', $subscription->id)
            ->assertStatus(200);
    }

    #[Test]
    function product_id_is_validated_upon_submission(): void
    {
        $subscription = RecurringOrder::factory()->create();

        Livewire::test(AddSubscriptionItem::class)
            ->dispatch('open-modal-add-subscription-item', $subscription->id)
            ->set('product_id', null)
            ->call('submit')
            ->assertHasErrors(['product_id' => 'required'])
            ->set('product_id', '9999999999')
            ->call('submit')
            ->assertHasErrors(['product_id' => 'exists']);;
    }

    #[Test]
    function quantity_is_validated_upon_submission(): void
    {
        $subscription = RecurringOrder::factory()->create();

        Livewire::test(AddSubscriptionItem::class)
            ->dispatch('open-modal-add-subscription-item', $subscription->id)
            ->set('qty', 0)
            ->call('submit')
            ->assertHasErrors(['qty' => 'min:1']);
    }

    #[Test]
    function type_is_validated_upon_submission(): void
    {
        $subscription = RecurringOrder::factory()->create();

        Livewire::test(AddSubscriptionItem::class)
            ->dispatch('open-modal-add-subscription-item', $subscription->id)
            ->set('type', '')
            ->call('submit')
            ->assertHasErrors(['type' => 'required'])
            ->set('type', 'something')
            ->call('submit')
            ->assertHasErrors(['type' => 'in']);
    }

    #[Test]
    function it_can_add_an_item_to_a_subscription(): void
    {
        $subscription = RecurringOrder::factory()->create();

        $product = Product::factory()->create();

        Livewire::test(AddSubscriptionItem::class)
            ->dispatch('open-modal-add-subscription-item', $subscription->id)
            ->set('product_id', $product->id)
            ->set('qty', 2)
            ->set('type', 'addon')
            ->call('submit')
            ->assertHasNoErrors()
            
            ->assertDispatched('subscriptionUpdated');

        $this->assertDatabaseHas(RecurringOrderItem::class, [
            'order_id' => $subscription->id,
            'product_id' => $product->id,
            'qty' => 2,
            'type' => 'addon'
        ]);
    }

    #[Test]
    function it_can_close(): void
    {
        $subscription = RecurringOrder::factory()->create();

        Livewire::test(AddSubscriptionItem::class)
            ->dispatch('open-modal-add-subscription-item', $subscription->id)
            ->call('close');

        $this->assertDatabaseMissing(RecurringOrderItem::class, [
            'order_id' => $subscription->id,
        ]);
    }
}
