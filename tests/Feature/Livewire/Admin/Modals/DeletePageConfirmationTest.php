<?php

namespace Tests\Feature\Livewire\Admin\Modals;

use App\Livewire\Admin\Modals\DeletePageConfirmation;
use App\Models\Page;
use Illuminate\Support\Carbon;
use Livewire\Livewire;
use PHPUnit\Framework\Attributes\Test;
use Tests\LivewireTenantTestCase;

class DeletePageConfirmationTest extends LivewireTenantTestCase
{
    #[Test]
    public function the_component_can_render(): void
    {
        $page = Page::factory()->create();

        Livewire::test(DeletePageConfirmation::class)
            ->dispatch('open-modal-delete-page-confirmation', $page->id)
            ->assertStatus(200);
    }

    #[Test]
    function a_name_is_required_to_submit(): void
    {
        $page = Page::factory()->create();

        Livewire::test(DeletePageConfirmation::class)
            ->dispatch('open-modal-delete-page-confirmation', $page->id)
            ->set('name', '')
            ->call('submit')
            ->assertHasErrors(['name' => 'required']);
    }

    #[Test]
    function a_matching_is_required_to_submit(): void
    {
        $page = Page::factory()->create();

        Livewire::test(DeletePageConfirmation::class)
            ->dispatch('open-modal-delete-page-confirmation', $page->id)
            ->set('name', 'zzzzz')
            ->call('submit')
            ->assertHasErrors('name');
    }

    #[Test]
    function it_can_delete_a_page(): void
    {
        Carbon::setTestNow($now = now());

        $page = Page::factory()->create();

        Livewire::test(DeletePageConfirmation::class)
            ->dispatch('open-modal-delete-page-confirmation', $page->id)
            ->set('name', $page->title)
            ->call('submit')
            ->assertHasNoErrors()
            ->assertRedirect(route('admin.pages.index'))
            ->assertSessionHas('flash_notification', [
                'message' => 'Page was successfully deleted!',
                'level' => 'info'
            ]);

        $this->assertDatabaseHas(Page::class, [
            'id' => $page->id,
            'deleted_at' => $now
        ]);

        Carbon::setTestNow();
    }

    #[Test]
    function it_can_close(): void
    {
        $page = Page::factory()->create();

        Livewire::test(DeletePageConfirmation::class)
            ->dispatch('open-modal-delete-page-confirmation', $page->id)
            ->set('name', $page->title)
            ->call('close');

        $this->assertDatabaseHas(Page::class, [
            'id' => $page->id,
            'deleted_at' => null
        ]);
    }
}
