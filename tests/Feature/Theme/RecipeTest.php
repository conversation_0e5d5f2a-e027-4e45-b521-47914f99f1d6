<?php

namespace Tests\Feature\Theme;

use App\Models\Pickup;
use App\Models\Recipe;
use App\Models\User;
use Illuminate\Pagination\LengthAwarePaginator;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class RecipeTest extends TenantTestCase
{
    #[Test]
    public function it_shows_a_list_of_posts(): void
    {
        Recipe::factory(2)->create(['published' => true]);
        Recipe::factory()->create(['published' => false]);

        $this->get(route('recipes.index'))
            ->assertOk()
            ->assertViewIs('theme::recipes.index')
            ->assertViewHas('recipes', function (LengthAwarePaginator $arg) {
                return $arg->count() === 2;
            });
    }

    #[Test]
    public function a_visitor_can_see_a_published_recipe(): void
    {
        $recipe = Recipe::factory()->create(['published' => true]);

        $this->get(route('recipes.show', compact('recipe')))
            ->assertOk()
            ->assertSee($recipe->title);
    }

    #[Test]
    public function a_visitor_cannot_see_a_unpublished_recipe(): void
    {
        $recipe = Recipe::factory()->create(['published' => false]);

        $this->get(route('recipes.show', compact('recipe')))
            ->assertRedirect();
    }

    #[Test]
    public function a_visitor_cannot_see_an_invalid_recipe(): void
    {
        $this->get(route('recipes.show', ['recipe' => 'zzzzzzzz']))
            ->assertRedirect();
    }

    #[Test]
    public function a_customer_cannot_see_a_unpublished_post(): void
    {
        $recipe = Recipe::factory()->create(['published' => false]);

        $this->actingAsCustomer()
            ->get(route('recipes.show', compact('recipe')))
            ->assertRedirect();
    }

    #[Test]
    public function an_admin_can_see_a_unpublished_post(): void
    {
        $recipe = Recipe::factory()->create(['published' => false]);

        $this->actingAs(User::factory()->admin()->create(['pickup_point' => Pickup::factory()]))
            ->get(route('recipes.show', compact('recipe')))
            ->assertOk()
            ->assertSee($recipe->ittle);
    }

    #[Test]
    public function an_editor_can_see_a_unpublished_post(): void
    {
        $recipe = Recipe::factory()->create(['published' => false]);

        $this->actingAs(User::factory()->editor()->create(['pickup_point' => Pickup::factory()]))
            ->get(route('recipes.show', compact('recipe')))
            ->assertOk()
            ->assertSee($recipe->ittle);
    }

    #[Test]
    public function an_owner_can_see_a_unpublished_post(): void
    {
        $recipe = Recipe::factory()->create(['published' => false]);

        $this->actingAs(User::factory()->owner()->create(['pickup_point' => Pickup::factory()]))
            ->get(route('recipes.show', compact('recipe')))
            ->assertOk()
            ->assertSee($recipe->ittle);
    }
}