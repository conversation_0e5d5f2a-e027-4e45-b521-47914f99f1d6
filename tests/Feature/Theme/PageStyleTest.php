<?php

namespace Tests\Feature\Theme;

use App\Models\Page;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class PageStyleTest extends TenantTestCase
{
    #[Test]
    public function it_cannot_load_page_styles_for_an_unknown_page(): void
    {
        $this->get(route('theme.pages.styles.show', [12312312312]))
            ->assertRedirect('/');
    }

    #[Test]
    public function it_can_load_page_styles_for_page(): void
    {
        $page = Page::factory()->create();

        $this->get(route('theme.pages.styles.show', compact('page')))
            ->assertOk();
    }
}
