<?php

namespace Tests\Feature\Theme\Account;

use App\Actions\Subscription\SyncSubscriptionDatetimes;
use App\Events\Subscription\SubscriptionWasExpedited;
use App\Models\Date;
use App\Models\Order;
use App\Models\Pickup;
use App\Models\RecurringOrder;
use App\Models\Schedule;
use Carbon\Carbon;
use Illuminate\Support\Facades\Event;
use Mockery;
use Mockery\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class RecurringOrderTest extends TenantTestCase
{
    #[Test]
    public function a_guest_cannot_see_recurring_order_account_settings(): void
    {
        $this->get(route('customers.recurring.edit'))
            ->assertRedirect(route('login'));
    }

    #[Test]
    public function a_user_without_a_recurring_order_blueprint_cannot_see_recurring_order_account_settings(): void
    {
        $this->actingAsCustomer()
            ->get(route('customers.recurring.edit'))
            ->assertRedirect(route('store.index'))
            ->assertSessionHas('flash_notification', [
                'message' => "Shop the store and select Subscribe & Save at checkout.",
                'level' => 'info'
            ]);
    }

    #[Test]
    public function test_a_user_cannot_update_their_recurring_order_frequency_with_invalid_data(): void
    {
        /** @var Pickup $pickup */
        $pickup = Pickup::factory()->create();

        $schedule = Schedule::factory()->create(['reorder_frequency' => ["14"]]);

        /** @var RecurringOrder $blueprint */
        $blueprint = RecurringOrder::factory()->create(['fulfillment_id' => $pickup->id, 'schedule_id' => $schedule->id]);

        /** @var Order $order */
        $order = Order::factory()->create([
            'is_recurring' => true,
            'blueprint_id' => $blueprint->id,
            'customer_id' => $blueprint->customer_id,
            'pickup_id' => $pickup->id
        ]);

        $this->actingAs($blueprint->customer)
            ->put(route('customers.recurring.frequency.update'), [
                'reorder_frequency' => 3
            ])
            ->assertSessionHasErrors(['reorder_frequency' => 'The selected reorder frequency is invalid.']);
    }

    #[Test]
    public function a_user_with_a_recurring_order_blueprint_can_see_recurring_order_account_settings(): void
    {
        $schedule = Schedule::factory()->hasDate()->create();

        /** @var Pickup $pickup */
        $pickup = Pickup::factory()->delivery()->for($schedule)->create();

        /** @var RecurringOrder $blueprint */
        $blueprint = RecurringOrder::factory()->create(['fulfillment_id' => $pickup->id]);


        Order::factory()->create([
            'is_recurring' => true,
            'blueprint_id' => $blueprint->id,
            'customer_id' => $blueprint->customer_id,
            'pickup_id' => $pickup->id
        ]);

        $this->actingAs($blueprint->customer)
            ->get(route('customers.recurring.edit'))
            ->assertOk()
            ->assertViewIs('theme::customers.recurring-orders');
    }

    #[Test]
    public function a_user_with_a_recurring_order_has_option_to_get_the_order_sooner(): void
    {
        $schedule = Schedule::factory()->hasDate()->create();

        $pickup = Pickup::factory()->delivery()->for($schedule)->create();

        /** @var RecurringOrder $blueprint */
        $blueprint = RecurringOrder::factory()->create([
            'fulfillment_id' => $pickup->id,
            'reorder_frequency' => 7
        ]);

        /** @var Order $order */
        $order = Order::factory()->create([
            'blueprint_id' => $blueprint->id,
            'customer_id' => $blueprint->customer_id,
            'pickup_id' => $pickup->id,
            'pickup_date' => today()->addDays(7)->format('Y-m-d'),
        ]);

        $soonest_order_window = $blueprint->fulfillment->activeOrderWindow();

        $this->actingAs($blueprint->customer)
            ->get(route('customers.recurring.edit'))
            ->assertOk()
            ->assertViewIs('theme::customers.recurring-orders')
            ->assertSee("{$soonest_order_window->deliveryDatetime()?->format('l, M jS')}");
    }

    #[Test]
    public function a_user_can_select_a_get_it_sooner_date(): void
    {
        Event::fake([SubscriptionWasExpedited::class]);

        $schedule = Schedule::factory()->create(['type_id' => Schedule::TYPE_REPEATING]);
        $date = Date::factory()->create(['schedule_id' => $schedule->id, 'pickup_date' => today()->addDays(5)->format('Y-m-d')]);
        Date::factory()->create(['schedule_id' => $schedule->id, 'pickup_date' => today()->addDays(7)->format('Y-m-d'),]);
        $pickup = Pickup::factory()->delivery()->for($schedule)->create();

        /** @var RecurringOrder $blueprint */
        $blueprint = RecurringOrder::factory()->create(['fulfillment_id' => $pickup->id, 'schedule_id' => $schedule->id]);

        /** @var Order $order */
        $order = Order::factory()->create([
            'is_recurring' => true,
            'blueprint_id' => $blueprint->id,
            'customer_id' => $blueprint->customer_id,
            'pickup_id' => $pickup->id,
            'deadline_date' => '2022-11-04',
            "pickup_date" => '2022-11-05'
        ]);

        $expected_blueprint = RecurringOrder::factory()->create([
            'fulfillment_id' => $pickup->id,
            'schedule_id' => $schedule->id,
            'ready_at' => $date->pickup_date->copy(),
        ]);

        $this->mock(SyncSubscriptionDatetimes::class, function (MockInterface $mock) use ($date, $blueprint, $expected_blueprint) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(
                    Mockery::on(fn (RecurringOrder $arg) => $arg->id === $blueprint->id),
                    Mockery::on(fn (Carbon $arg) => $arg->eq($date->pickup_date->copy()->addDays($blueprint->reorder_frequency)))
                )
                ->andReturn($expected_blueprint);
        });

        $this->actingAs($blueprint->customer)
            ->put(route('customers.recurring.get-it-sooner'), [
                'order_id' => $order->id
            ])
            ->assertRedirect()
            ->assertSessionHasNoErrors()
            ->assertSessionHas('flash_notification', [
                'message' => 'Your subscription has been updated.',
                'level' => 'info'
            ]);

        Event::assertDispatched(SubscriptionWasExpedited::class, function (SubscriptionWasExpedited $event) use ($blueprint, $expected_blueprint, $order, $date) {
            return $event->subscription->is($blueprint)
                && $event->old_delivery_date->format('Y-m-d H:i:s') === $order->pickup_date->format('Y-m-d H:i:s')
                && $event->new_delivery_date->format('Y-m-d H:i:s') === $expected_blueprint->ready_at->format('Y-m-d H:i:s');
        });

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'deadline_date' => $date->order_end_date->format('Y-m-d'),
            'pickup_date' => $date->pickup_date->format('Y-m-d'),
        ]);
    }
}
