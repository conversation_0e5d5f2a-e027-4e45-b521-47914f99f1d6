<?php

namespace Tests\Feature\Theme;

use App\Models\GiftCertificate;
use App\Models\User;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class RedeemedGiftCardTest extends TenantTestCase
{
    #[Test]
    public function an_unauthenticated_user_cannot_redeem_gift_certificates(): void
    {
        $this->postJson(route('api.theme.redeemed-gift-certificates.store'))
            ->assertUnauthorized();
    }


    #[Test]
    public function it_validates_the_redeem_gift_certificate_request(): void
    {
        $this->actingAsCustomer()
            ->postJson(route('api.theme.redeemed-gift-certificates.store'))
            ->assertInvalid(['code' => 'The code field is required.']);

        $this->postJson(route('api.theme.redeemed-gift-certificates.store'), [
            'code' => 123123123
        ])
            ->assertInvalid(['code' => 'The selected code is invalid.']);

        $gift_certificate = GiftCertificate::factory()->create(['active' => false]);
        $this->postJson(route('api.theme.redeemed-gift-certificates.store'), [
            'code' => $gift_certificate->code
        ])
            ->assertInvalid(['code' => 'The selected code is invalid.']);

        $gift_certificate = GiftCertificate::factory()->create(['redeemed' => now(), 'qty' => 0]);
        $this->postJson(route('api.theme.redeemed-gift-certificates.store'), [
            'code' => $gift_certificate->code
        ])
            ->assertStatus(409)
            ->assertJsonFragment(['The gift certificate has already been redeemed.']);
    }

    #[Test]
    public function it_stores_the_gift_certificate(): void
    {
        $gift_certificate = GiftCertificate::factory()->create();

        $this->actingAs($user = User::factory()->create())
            ->postJson(route('api.theme.redeemed-gift-certificates.store'), [
                'code' => $gift_certificate->code
            ])
            ->assertOk()
            ->assertJsonFragment([
                'The gift certificate in the amount of &#36;' . money($gift_certificate->amount) . ' has been applied.'
            ]);

        $this->assertDatabaseHas(User::class, [
            'id' => $user->id,
            'credit' => $gift_certificate->amount
        ]);
    }
}