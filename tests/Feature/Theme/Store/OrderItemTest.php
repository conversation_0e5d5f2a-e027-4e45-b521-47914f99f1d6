<?php

namespace Tests\Feature\Theme\Store;

use App\Events\Cart\CartCreated;
use App\Events\Order\ItemWasAddedToOrder;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use App\Models\Setting;
use App\Models\User;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Event;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class OrderItemTest extends TenantTestCase
{
    #[Test]
    public function a_guest_cannot_add_an_item_to_an_order(): void
    {
        $order = Order::factory()->create();

        $this->post(route('order.item.store', ['order' => $order->id]))
            ->assertRedirect(route('login'));
    }

    #[Test]
    public function a_user_cannot_add_an_item_to_an_unknown_order(): void
    {
        $user = User::factory()->create();

        $this->actingAs($user)
            ->get(route('store.index'))
            ->assertOk();

        $this->post(route('order.item.store', ['order' => 'abc']))
            ->assertRedirect(route('store.index'));
    }

    #[Test]
    public function a_user_cannot_add_an_item_to_another_customers_order(): void
    {
        $user = User::factory()->create();
        $order = Order::factory()->create();

        $this->actingAs($user)
            ->get(route('store.index'))
            ->assertOk();

        $this->post(route('order.item.store', ['order' => $order->id]))
            ->assertRedirect(route('store.index'));
    }

    #[Test]
    public function it_validates_required_fields_when_adding_item_to_an_order(): void
    {
        $user = User::factory()->create();
        $order = Order::factory()->create(['customer_id' => $user->id]);
        $user->order_id = $order->save();

        $this->actingAs($user)
            ->get(route('store.index'))
            ->assertOk();

        $this->post(route('order.item.store', ['order' => $order]))
            ->assertRedirect(route('store.index'))
            ->assertSessionHasErrors([
                'product_id' => 'The product id field is required.',
                'quantity' => 'The quantity field is required.',
            ]);
    }

    #[Test]
    public function a_user_cannot_add_an_unknown_item_to_an_order(): void
    {
        $user = User::factory()->create();
        $order = Order::factory()->create(['customer_id' => $user->id]);
        $user->order_id = $order->save();

        $this->actingAs($user)
            ->get(route('store.index'))
            ->assertOk();

        $this->post(route('order.item.store', ['order' => $order]), [
            'product_id' => 'abc',
            'quantity' => 1
        ])
            ->assertRedirect(route('store.index'))
            ->assertSessionHasErrors(['product_id' => 'The selected product id is invalid.']);
    }

    #[Test]
    public function a_user_cannot_add_a_zero_quantity_item_to_an_order(): void
    {
        $user = User::factory()->create();
        $order = Order::factory()->create(['customer_id' => $user->id]);
        $user->order_id = $order->save();

        $product = Product::factory()->create();

        $this->actingAs($user)
            ->get(route('store.index'))
            ->assertOk();

        $this->post(route('order.item.store', ['order' => $order]), [
            'product_id' => $product->id,
            'quantity' => 0
        ])
            ->assertRedirect(route('store.index'))
            ->assertSessionHasErrors(['quantity' => 'The quantity field must be at least 1.']);
    }

    #[Test]
    public function a_user_can_add_an_item_to_order_before_order_has_been_confirmed(): void
    {
        Event::fake([CartCreated::class]);

        $user = User::factory()->create();
        $order = Order::factory()->create(['customer_id' => $user->id]);
        $user->order_id = $order->save();

        $product = Product::factory()->create(['inventory' => 10]);

        $this->actingAs($user)
            ->get(route('store.index'))
            ->assertOk();

        $this->post(route('order.item.store', ['order' => $order]), [
            'product_id' => $product->id,
            'quantity' => 1
        ])
            ->assertRedirect(route('store.index'));

        // Check that order item is created.
        $this->assertDatabaseHas('order_items', [
            'order_id' => $order->id,
            'product_id' => $product->id,
            'qty' => 1,
            'title' => $product->title,
            'subtotal' => $product->getPrice()
        ]);

        // Check that product inventory has not changed.
        $this->assertDatabaseHas('products', [
            'id' => $product->id,
            'inventory' => 10
        ]);

        // Because this is the first item added, check that the cart created event is fired.
        Event::assertDispatched(CartCreated::class);

        // Ensure no event is recorded. This should only happen when adding to an order.
        $this->assertDatabaseMissing('events', [
            'user_id' => $user->id,
            'model_id' => $order->id,
            'model_type' => \App\Models\Order::class,
            'event_id' => 'order_item_added',
        ]);
    }

    #[Test]
    public function a_user_can_add_item_to_order_after_order_has_been_confirmed(): void
    {
        Carbon::setTestNow(now());

        Event::fake([CartCreated::class]);

        $user = User::factory()->create();
        $order = Order::factory()->create(['customer_id' => $user->id, 'confirmed' => true]);
        $user->order_id = $order->save();

        $product = Product::factory()->create();

        $this->actingAs($user)
            ->get(route('store.index'))
            ->assertOk();

        $this->post(route('order.item.store', ['order' => $order]), [
            'product_id' => $product->id,
            'quantity' => 1
        ])
            ->assertRedirect(route('store.index'));

        // Check that order item is created.
        $this->assertDatabaseHas('order_items', [
            'order_id' => $order->id,
            'product_id' => $product->id,
            'qty' => 1,
            'title' => $product->title,
            'subtotal' => $product->getPrice()
        ]);

        // Check that inventory been reduced by the quantity added.
        $this->assertDatabaseHas('products', [
            'id' => $product->id,
            'inventory' => $product->inventory - 1
        ]);

        // Because this is the first item added, check that the cart created event is fired.
        Event::assertDispatched(CartCreated::class);

        // Ensure no event is recorded. This should only happen when adding to an order.
        $this->assertDatabaseHas(\App\Models\Event::class, [
            'user_id' => $user->id,
            'model_id' => $order->id,
            'model_type' => \App\Models\Order::class,
            'event_id' => 'order_item_added',
            'created_at' => now()
        ]);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_updates_order_subtotals_when_adding_an_item_to_the_cart(): void
    {
        Event::fake([CartCreated::class]);

        $user = User::factory()->create();
        $order = Order::factory()->create(['customer_id' => $user->id]);
        $user->order_id = $order->save();

        $product = Product::factory()->create(['inventory' => 10]);

        $this->actingAs($user)
            ->get(route('store.index'))
            ->assertOk();

        $this->post(route('order.item.store', ['order' => $order]), [
            'product_id' => $product->id,
            'quantity' => 1
        ])
            ->assertRedirect(route('store.index'));

        $this->assertDatabaseHas('order_items', [
            'order_id' => $order->id,
            'product_id' => $product->id,
            'qty' => 1,
            'title' => $product->title,
            'subtotal' => $product->getPrice()
        ]);

        // Check that product inventory has not changed.
        $this->assertDatabaseHas('products', [
            'id' => $product->id,
            'inventory' => 10
        ]);

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'original_subtotal' => $product->getPrice(),
            'subtotal' => $product->getPrice(),
            'original_total' => $product->getPrice(),
        ]);
    }

    #[Test]
    public function it_retuns_a_json_response_when_requesting_json(): void
    {
        Carbon::setTestNow($now = now());

        Event::fake([CartCreated::class]);

        $user = User::factory()->create();
        $order = Order::factory()->create(['customer_id' => $user->id, 'confirmed' => true]);
        $user->order_id = $order->save();

        $product = Product::factory()->create();

        $this->actingAs($user)
            ->get(route('store.index'))
            ->assertOk();

        $this->postJson(route('order.item.store', ['order' => $order]), [
            'product_id' => $product->id,
            'quantity' => 1
        ])
            ->assertOk()
            ->assertJsonStructure([
                'items',
                'itemsCount',
                'orderId',
                'orderTotal',
                'orderSubtotal',
                'productId',
                'productName',
                'productPrice',
                'productSavings',
                'productQty'
            ]);

        Carbon::setTestNow();
    }

    #[Test]
    public function the_expected_events_fire_when_the_first_item_is_added_to_an_order(): void
    {
        Event::fake([CartCreated::class, ItemWasAddedToOrder::class]);

        $user = User::factory()->create();
        $order = Order::factory()->create(['customer_id' => $user->id]);
        $user->order_id = $order->save();

        $product = Product::factory()->create();

        $this->actingAs($user)
            ->get(route('store.index'))
            ->assertOk();

        $this->post(route('order.item.store', ['order' => $order]), [
            'product_id' => $product->id,
            'quantity' => 1
        ])
            ->assertRedirect(route('store.index'));

        // Check that order item is created.
        $this->assertDatabaseHas('order_items', [
            'order_id' => $order->id,
            'product_id' => $product->id,
            'qty' => 1,
            'title' => $product->title,
            'subtotal' => $product->getPrice()
        ]);

        Event::assertDispatched(CartCreated::class);
        Event::assertDispatched(ItemWasAddedToOrder::class);
    }

    #[Test]
    public function the_expected_events_fire_when_the_second_item_is_added_to_an_order(): void
    {
        Event::fake([CartCreated::class, ItemWasAddedToOrder::class]);

        $user = User::factory()->create();
        $order = Order::factory()->create(['customer_id' => $user->id]);
        $user->order_id = $order->save();

        OrderItem::factory()->create(['order_id' => $order->id]);

        $product = Product::factory()->create();

        $this->actingAs($user)
            ->get(route('store.index'))
            ->assertOk();

        $this->post(route('order.item.store', ['order' => $order]), [
            'product_id' => $product->id,
            'quantity' => 1
        ])
            ->assertRedirect(route('store.index'));

        // Check that order item is created.
        $this->assertDatabaseHas('order_items', [
            'order_id' => $order->id,
            'product_id' => $product->id,
            'qty' => 1,
            'title' => $product->title,
            'subtotal' => $product->getPrice()
        ]);

        Event::assertNotDispatched(CartCreated::class);
        Event::assertDispatched(ItemWasAddedToOrder::class);
    }

    #[Test]
    public function a_product_cannot_be_added_to_a_confirmed_order_when_the_cart_does_not_meet_product_order_minimum(): void
    {
        $user = User::factory()->create();
        $order = Order::factory()->create(['confirmed' => true, 'customer_id' => $user->id, 'subtotal' => 499]);
        $user->order_id = $order->save();

        $product = Product::factory()->create(['settings' => ['order_minimum' => 500]]);

        $this->actingAs($user)
            ->get(route('store.index'))
            ->assertOk();

        $this->post(route('order.item.store', ['order' => $order]), [
            'product_id' => $product->id,
            'quantity' => 1
        ])
            ->assertStatus(409);

        // Check that order item is created.
        $this->assertDatabaseMissing('order_items', [
            'order_id' => $order->id,
            'product_id' => $product->id,
        ]);
    }

    protected function setUp(): void
    {
        parent::setUp();
        Setting::updateOrCreate(['key' => 'cart_service'], ['value' => 'order']);
    }
}
