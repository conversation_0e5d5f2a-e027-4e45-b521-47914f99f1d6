<?php

namespace Tests\Feature\Theme\Store;

use App\Events\Product\ProductWasViewed;
use App\Models\Product;
use App\Models\User;
use App\Services\StoreService;
use Illuminate\Support\Facades\Event;
use Mo<PERSON>y\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class ProductTest extends TenantTestCase
{
    #[Test]
    public function it_does_not_fire_events_when_user_visits_product_page_and_is_not_authenticated(): void
    {
        Event::fake([ProductWasViewed::class]);

        $product = Product::factory()->create();

        $this->get(route('store.show', ['product' => $product->slug]))
            ->assertOk();

        Event::assertNotDispatched(ProductWasViewed::class);
    }

    #[Test]
    public function it_fires_expected_events_when_user_visits_product_page_and_is_authenticated(): void
    {
        Event::fake([ProductWasViewed::class]);

        $product = Product::factory()->create();
        $user = User::factory()->create();

        $this->actingAs($user)
            ->get(route('store.show', ['product' => $product->slug]))
            ->assertOk();

        Event::assertDispatched(
            ProductWasViewed::class,
            function (ProductWasViewed $event) use ($user, $product) {
                return $event->user->id === $user->id
                    && $event->product->id === $product->id;
            }
        );
    }

    #[Test]
    public function store_shows_products_callout_messages(): void
    {
        $product = Product::factory()->create(['sale' => true, 'settings' => ['sale_message' => 'Sales Tag']]);

        $user = User::factory()->create();

        $this->actingAs($user)
            ->get(route('store.show', ['product' => $product->slug]))
            ->assertOk()
            ->assertSee('Sales Tag');

        $product = Product::factory()->create([
            'sale' => true,
            'settings' => [
                'sale_message' => 'New Sales Tag',
                'standard_callout_message' => 'New Standard Tag'
            ]
        ]);

        $this->actingAs($user)
            ->get(route('store.show', ['product' => $product->slug]))
            ->assertOk()
            ->assertSee('New Sales Tag');

        $product = Product::factory()->create(['settings' => ['standard_callout_message' => 'Standard Tag']]);

        $this->actingAs($user)
            ->get(route('store.show', ['product' => $product->slug]))
            ->assertOk()
            ->assertSee('Standard Tag');
    }

    #[Test]
    public function it_shows_the_expected_template_page(): void
    {
        $cart = \App\Models\Cart::factory()->make();

        $this->mock(StoreService::class, function (MockInterface $mock) use ($cart) {
            $mock->shouldReceive('rating')->andReturn(4);
            $mock->shouldReceive('reviewCount')->andReturn(100);
            $mock->shouldReceive('order')->andReturnNull();
            $mock->shouldReceive('orderWithoutDeliveryMethodCheck')->andReturnNull();
            $mock->shouldReceive('cart')->andReturnNull();
            $mock->shouldReceive('cartOrCartStub')->andReturn($cart);
            $mock->shouldReceive('subscription')->andReturnNull();
            $mock->shouldReceive('deliveryMethod')->andReturnNull();
        });
        $product = Product::factory()->create(['sale' => true, 'settings' => []]);

        $this->get(route('store.show', ['product' => $product->slug]))
            ->assertOk()
            ->assertDontSee('See Inside Bundle');

        $product->setting(['store_template' => 'default']);
        $product->save();

        $this->get(route('store.show', ['product' => $product->slug]))
            ->assertOk()
            ->assertDontSee('See Inside Bundle');

        $product->setting(['store_template' => 'bundle']);
        $product->save();

        $this->get(route('store.show', ['product' => $product->slug]))
            ->assertOk()
            ->assertSee('100% Weird Free Guarantee');
    }
}
