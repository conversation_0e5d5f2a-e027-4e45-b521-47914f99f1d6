<?php

namespace Tests\Feature\Theme;

use App\Models\Pickup;
use App\Models\Post;
use App\Models\User;
use Illuminate\Pagination\LengthAwarePaginator;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class BlogPostTest extends TenantTestCase
{
    #[Test]
    public function it_shows_a_list_of_posts(): void
    {
        Post::factory(2)->create(['status' => 'published']);
        Post::factory()->create(['status' => 'draft']);

        $this->get(route('blog.index'))
            ->assertOk()
            ->assertViewIs('theme::blog.index')
            ->assertViewHas('posts', function (LengthAwarePaginator $arg) {
                return $arg->count() === 2;
            });
    }

    #[Test]
    public function a_visitor_can_see_a_published_post(): void
    {
        $post = Post::factory()->create(['status' => 'published']);

        $this->get(route('blog.show', compact('post')))
            ->assertOk()
            ->assertSee($post->ittle);
    }

    #[Test]
    public function a_visitor_cannot_see_a_unpublished_post(): void
    {
        $post = Post::factory()->create(['status' => 'draft']);

        $this->get(route('blog.show', compact('post')))
            ->assertRedirect();
    }

    #[Test]
    public function a_visitor_cannot_see_an_invalid_post(): void
    {
        $this->get(route('blog.show', ['post' => 'zzzzzzzz']))
            ->assertRedirect();
    }

    #[Test]
    public function a_customer_cannot_see_a_unpublished_post(): void
    {
        $post = Post::factory()->create(['status' => 'draft']);

        $this->actingAsCustomer()
            ->get(route('blog.show', compact('post')))
            ->assertRedirect();
    }

    #[Test]
    public function an_admin_can_see_a_unpublished_post(): void
    {
        $post = Post::factory()->create(['status' => 'draft']);

        $this->actingAs(User::factory()->admin()->create(['pickup_point' => Pickup::factory()]))
            ->get(route('blog.show', compact('post')))
            ->assertOk()
            ->assertSee($post->ittle);
    }

    #[Test]
    public function an_editor_can_see_a_unpublished_post(): void
    {
        $post = Post::factory()->create(['status' => 'draft']);

        $this->actingAs(User::factory()->editor()->create(['pickup_point' => Pickup::factory()]))
            ->get(route('blog.show', compact('post')))
            ->assertOk()
            ->assertSee($post->ittle);
    }

    #[Test]
    public function an_owner_can_see_a_unpublished_post(): void
    {
        $post = Post::factory()->create(['status' => 'draft']);

        $this->actingAs(User::factory()->owner()->create(['pickup_point' => Pickup::factory()]))
            ->get(route('blog.show', compact('post')))
            ->assertOk()
            ->assertSee($post->ittle);
    }
}