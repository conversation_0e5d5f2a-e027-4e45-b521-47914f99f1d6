<?php

namespace Tests\Feature\Admin;

use App\Models\Widget;
use Illuminate\Support\Carbon;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class FooterWidgetTest extends TenantTestCase
{
    #[Test]
    public function a_guest_cannot_create_a_footer_widget(): void
    {
        $this->post(route('admin.footer.widgets.store'))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function a_non_admin_cannot_create_a_footer_widget(): void
    {
        $this->actingAsCustomer()
            ->post(route('admin.footer.widgets.store'))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function it_validates_the_create_request(): void
    {
        $this->actingAsAdmin()
            ->post(route('admin.footer.widgets.store'))
            ->assertInvalid([
                'title' => 'The title field is required.',
                'template' => 'The template field is required.',
            ]);

        $this->post(route('admin.footer.widgets.store'), ['template' => 'RichText'])
            ->assertInvalid(['content' => 'The content field is required.']);

        $this->post(route('admin.footer.widgets.store'), ['template' => 'LinkList'])
            ->assertInvalid(['settings.links' => 'The settings.links field is required.']);

        $this->post(route('admin.footer.widgets.store'), [
            'template' => 'LinkList',
            'settings' => ['links' => []]
        ])
            ->assertInvalid(['settings.links' => 'The settings.links field is required.']);

        $this->post(route('admin.footer.widgets.store'), [
            'template' => 'LinkList',
            'settings' => ['links' => ['a']]
        ])
            ->assertInvalid([
                'settings.links.0.url' => 'The settings.links.0.url field is required.',
                'settings.links.0.title' => 'The settings.links.0.title field is required.'
            ]);

        $this->post(route('admin.footer.widgets.store'), [
            'template' => 'SocialNetworks',
            'settings' => ['links' => ['a']]
        ])
            ->assertInvalid([
                'settings.links.0.url' => 'The settings.links.0.url field is required.',
            ]);

        $this->post(route('admin.footer.widgets.store'), ['settings' => 'seomething'])
            ->assertInvalid(['settings' => 'The settings field must be an array.']);
    }

    #[Test]
    public function an_admin_can_create_rich_text_widget(): void
    {
        $settings = ['foo' => 'bar'];

        $this->actingAsAdmin()
            ->post(route('admin.footer.widgets.store'), [
                'title' => 'The rich text widget',
                'template' => 'RichText',
                'content' => 'My content',
                'settings' => $settings
            ])
            ->assertSessionHasNoErrors()
            ->assertExactJson(['id' => null]);

        $this->assertDatabaseHas(Widget::class, [
            'page_id' => 0,
            'container_id' => null,
            'enabled' => 0,
            'title' => 'The rich text widget',
            'description' => null,
            'visible' => 1,
            'template' => 'RichText',
            'content' => 'My content',
            'settings' => json_encode($settings),
            'sort' => 0
        ]);
    }

    #[Test]
    public function an_admin_can_create_HTML_widget(): void
    {
        $settings = ['foo' => 'bar'];

        $this->actingAsAdmin()
            ->post(route('admin.footer.widgets.store'), [
                'title' => 'The HTML widget',
                'template' => 'HTML',
                'content' => 'My HTML content',
                'settings' => $settings
            ])
            ->assertSessionHasNoErrors()
            ->assertExactJson(['id' => null]);

        $this->assertDatabaseHas(Widget::class, [
            'page_id' => 0,
            'container_id' => null,
            'enabled' => 0,
            'title' => 'The HTML widget',
            'description' => null,
            'visible' => 1,
            'template' => 'HTML',
            'settings' => json_encode($settings),
            'sort' => 0
        ]);

        // content is overridden with view
        $this->assertDatabaseMissing(Widget::class, [
            'content' => 'My HTML content',
        ]);
    }

    #[Test]
    public function an_admin_can_create_LinkList_widget(): void
    {
        $settings = ['links' => [
            ['url' => 'http://example.com', 'title' => 'My link']]
        ];

        $this->actingAsAdmin()
            ->post(route('admin.footer.widgets.store'), [
                'title' => 'The LinkList widget',
                'template' => 'LinkList',
                'content' => 'My LinkList content',
                'settings' => $settings
            ])
            ->assertSessionHasNoErrors()
            ->assertExactJson(['id' => null]);

        $this->assertDatabaseHas(Widget::class, [
            'page_id' => 0,
            'container_id' => null,
            'enabled' => 0,
            'title' => 'The LinkList widget',
            'description' => null,
            'visible' => 1,
            'template' => 'LinkList',
            'settings' => json_encode($settings),
            'sort' => 0
        ]);

        // content is overridden with view
        $this->assertDatabaseMissing(Widget::class, [
            'content' => 'My LinkList content',
        ]);
    }

    #[Test]
    public function an_admin_can_create_SocialNetworks_widget(): void
    {
        $settings = ['links' => [
            ['url' => 'http://example.com']]
        ];

        $this->actingAsAdmin()
            ->post(route('admin.footer.widgets.store'), [
                'title' => 'The SocialNetworks widget',
                'template' => 'SocialNetworks',
                'content' => 'My SocialNetworks content',
                'settings' => $settings
            ])
            ->assertSessionHasNoErrors()
            ->assertExactJson(['id' => null]);

        $this->assertDatabaseHas(Widget::class, [
            'page_id' => 0,
            'container_id' => null,
            'enabled' => 0,
            'title' => 'The SocialNetworks widget',
            'description' => null,
            'visible' => 1,
            'template' => 'SocialNetworks',
            'settings' => json_encode($settings),
            'sort' => 0
        ]);

        // content is overridden with view
        $this->assertDatabaseMissing(Widget::class, [
            'content' => 'My SocialNetworks content',
        ]);
    }

    #[Test]
    public function an_admin_can_create_Newsletter_widget(): void
    {
        $settings = ['foo' => 'bar'];

        $this->actingAsAdmin()
            ->post(route('admin.footer.widgets.store'), [
                'title' => 'The Newsletter widget',
                'template' => 'Newsletter',
                'content' => 'My Newsletter content',
                'settings' => $settings
            ])
            ->assertSessionHasNoErrors()
            ->assertExactJson(['id' => null]);

        $this->assertDatabaseHas(Widget::class, [
            'page_id' => 0,
            'container_id' => null,
            'enabled' => 0,
            'title' => 'The Newsletter widget',
            'description' => null,
            'visible' => 1,
            'template' => 'Newsletter',
            'settings' => json_encode($settings),
            'sort' => 0
        ]);

        // content is overridden with view
        $this->assertDatabaseMissing(Widget::class, [
            'content' => 'My Newsletter content',
        ]);
    }

    #[Test]
    public function an_admin_can_create_ContactDetails_widget(): void
    {
        $settings = ['foo' => 'bar'];

        $this->actingAsAdmin()
            ->post(route('admin.footer.widgets.store'), [
                'title' => 'The ContactDetails widget',
                'template' => 'ContactDetails',
                'content' => 'My ContactDetails content',
                'settings' => $settings
            ])
            ->assertSessionHasNoErrors()
            ->assertExactJson(['id' => null]);

        $this->assertDatabaseHas(Widget::class, [
            'page_id' => 0,
            'container_id' => null,
            'enabled' => 0,
            'title' => 'The ContactDetails widget',
            'description' => null,
            'visible' => 1,
            'template' => 'ContactDetails',
            'settings' => json_encode($settings),
            'sort' => 0
        ]);

        // content is overridden with view
        $this->assertDatabaseMissing(Widget::class, [
            'content' => 'My ContactDetails content',
        ]);
    }

    #[Test]
    public function an_admin_can_create_Divider_widget(): void
    {
        $settings = ['foo' => 'bar'];

        $this->actingAsAdmin()
            ->post(route('admin.footer.widgets.store'), [
                'title' => 'The Divider widget',
                'template' => 'Divider',
                'content' => 'My Divider content',
                'settings' => $settings
            ])
            ->assertSessionHasNoErrors()
            ->assertExactJson(['id' => null]);

        $this->assertDatabaseHas(Widget::class, [
            'page_id' => 0,
            'container_id' => null,
            'enabled' => 0,
            'title' => 'The Divider widget',
            'description' => null,
            'visible' => 1,
            'template' => 'Divider',
            'settings' => json_encode($settings),
            'sort' => 0
        ]);

        // content is overridden with view
        $this->assertDatabaseMissing(Widget::class, [
            'content' => 'My Divider content',
        ]);
    }

    #[Test]
    public function an_admin_can_create_Spacer_widget(): void
    {
        $settings = ['foo' => 'bar'];

        $this->actingAsAdmin()
            ->post(route('admin.footer.widgets.store'), [
                'title' => 'The Spacer widget',
                'template' => 'Spacer',
                'content' => 'My Spacer content',
                'settings' => $settings
            ])
            ->assertSessionHasNoErrors()
            ->assertExactJson(['id' => null]);

        $this->assertDatabaseHas(Widget::class, [
            'page_id' => 0,
            'container_id' => null,
            'enabled' => 0,
            'title' => 'The Spacer widget',
            'description' => null,
            'visible' => 1,
            'template' => 'Spacer',
            'settings' => json_encode($settings),
            'sort' => 0
        ]);

        // content is overridden with view
        $this->assertDatabaseMissing(Widget::class, [
            'content' => 'My Spacer content',
        ]);
    }

    #[Test]
    public function a_guest_cannot_update_a_footer_widget(): void
    {
        $widget = Widget::factory()->create(['page_id' => 0]);

        $this->put(route('admin.footer.widgets.update', compact('widget')))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function a_non_admin_cannot_update_a_footer_widget(): void
    {
        $widget = Widget::factory()->create(['page_id' => 0]);

        $this->actingAsCustomer()
            ->put(route('admin.footer.widgets.update', compact('widget')))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function an_admin_cannot_update_an_invalid_footer_widget(): void
    {
        $this->actingAsAdmin()
            ->put(route('admin.footer.widgets.update', ['something']))
            ->assertRedirect();
    }

    #[Test]
    public function it_validates_the_update_request(): void
    {
        $widget = Widget::factory()->create(['page_id' => 0]);

        $this->actingAsAdmin()
            ->put(route('admin.footer.widgets.update', compact('widget')))
            ->assertInvalid([
                'title' => 'The title field is required.',
                'template' => 'The template field is required.',
            ]);

        $this->put(route('admin.footer.widgets.update', compact('widget')), ['template' => 'RichText'])
            ->assertInvalid(['content' => 'The content field is required.']);

        $this->put(route('admin.footer.widgets.update', compact('widget')), ['template' => 'LinkList'])
            ->assertInvalid(['settings.links' => 'The settings.links field is required.']);

        $this->put(route('admin.footer.widgets.update', compact('widget')), [
            'template' => 'LinkList',
            'settings' => ['links' => []]
        ])
            ->assertInvalid(['settings.links' => 'The settings.links field is required.']);

        $this->put(route('admin.footer.widgets.update', compact('widget')), [
            'template' => 'LinkList',
            'settings' => ['links' => ['a']]
        ])
            ->assertInvalid([
                'settings.links.0.url' => 'The settings.links.0.url field is required.',
                'settings.links.0.title' => 'The settings.links.0.title field is required.'
            ]);

        $this->put(route('admin.footer.widgets.update', compact('widget')), [
            'template' => 'SocialNetworks',
            'settings' => ['links' => ['a']]
        ])
            ->assertInvalid([
                'settings.links.0.url' => 'The settings.links.0.url field is required.',
            ]);

        $this->put(route('admin.footer.widgets.update', compact('widget')), ['settings' => 'seomething'])
            ->assertInvalid(['settings' => 'The settings field must be an array.']);
    }

    #[Test]
    public function an_admin_can_update_rich_text_widget(): void
    {
        $widget = Widget::factory()->create(['page_id' => 0]);

        $settings = ['foo' => 'bar'];

        $this->actingAsAdmin()
            ->put(route('admin.footer.widgets.update', compact('widget')), [
                'title' => 'The rich text widget',
                'template' => 'RichText',
                'content' => 'My content',
                'settings' => $settings
            ])
            ->assertOk()
            ->assertSessionHasNoErrors()
            ->assertExactJson(['Widget updated']);

        $this->assertDatabaseHas(Widget::class, [
            'page_id' => 0,
            'container_id' => null,
            'enabled' => (int) $widget->enabled,
            'title' => 'The rich text widget',
            'description' => $widget->description,
            'visible' => 1,
            'template' => 'RichText',
            'content' => 'My content',
            'settings' => json_encode(array_merge((array) $widget->settings, $settings)),
            'sort' => $widget->sort
        ]);
    }

    #[Test]
    public function an_admin_can_update_HTML_widget(): void
    {
        $widget = Widget::factory()->create(['page_id' => 0]);

        $settings = ['foo' => 'bar'];

        $this->actingAsAdmin()
            ->put(route('admin.footer.widgets.update', compact('widget')), [
                'title' => 'The HTML widget',
                'template' => 'HTML',
                'content' => 'My HTML content',
                'settings' => $settings
            ])
            ->assertOk()
            ->assertSessionHasNoErrors()
            ->assertExactJson(['Widget updated']);

        $this->assertDatabaseHas(Widget::class, [
            'page_id' => 0,
            'container_id' => null,
            'enabled' => (int) $widget->enabled,
            'title' => 'The HTML widget',
            'description' => $widget->description,
            'visible' => 1,
            'template' => 'HTML',
            'settings' => json_encode(array_merge((array) $widget->settings, $settings)),
            'sort' => $widget->sort
        ]);

        $this->assertDatabaseHas(Widget::class, [
            'content' => 'My HTML content',
        ]);
    }

    #[Test]
    public function an_admin_can_update_LinkList_widget(): void
    {
        $widget = Widget::factory()->create(['page_id' => 0]);

        $settings = ['links' => [
            ['url' => 'http://example.com', 'title' => 'My link']]
        ];

        $this->actingAsAdmin()
            ->put(route('admin.footer.widgets.update', compact('widget')), [
                'title' => 'The LinkList widget',
                'template' => 'LinkList',
                'content' => 'My LinkList content',
                'settings' => $settings
            ])
            ->assertOk()
            ->assertSessionHasNoErrors()
            ->assertExactJson(['Widget updated']);

        $this->assertDatabaseHas(Widget::class, [
            'page_id' => 0,
            'container_id' => null,
            'enabled' => (int) $widget->enabled,
            'title' => 'The LinkList widget',
            'description' => $widget->description,
            'visible' => 1,
            'template' => 'LinkList',
            'settings' => json_encode(array_merge((array) $widget->settings, $settings)),
            'sort' => $widget->sort
        ]);

        // content is overridden with view
        $this->assertDatabaseMissing(Widget::class, [
            'content' => 'My LinkList content',
        ]);
    }

    #[Test]
    public function an_admin_can_update_SocialNetworks_widget(): void
    {
        $widget = Widget::factory()->create(['page_id' => 0]);

        $settings = ['links' => [
            ['url' => 'http://example.com']]
        ];

        $this->actingAsAdmin()
            ->put(route('admin.footer.widgets.update', compact('widget')), [
                'title' => 'The SocialNetworks widget',
                'template' => 'SocialNetworks',
                'content' => 'My SocialNetworks content',
                'settings' => $settings
            ])
            ->assertOk()
            ->assertSessionHasNoErrors()
            ->assertExactJson(['Widget updated']);

        $this->assertDatabaseHas(Widget::class, [
            'page_id' => 0,
            'container_id' => null,
            'enabled' => (int) $widget->enabled,
            'title' => 'The SocialNetworks widget',
            'description' => $widget->description,
            'visible' => 1,
            'template' => 'SocialNetworks',
            'settings' => json_encode(array_merge((array) $widget->settings, $settings)),
            'sort' => $widget->sort
        ]);

        // content is overridden with view
        $this->assertDatabaseMissing(Widget::class, [
            'content' => 'My SocialNetworks content',
        ]);
    }

    #[Test]
    public function an_admin_can_update_Newsletter_widget(): void
    {
        $widget = Widget::factory()->create(['page_id' => 0]);

        $settings = ['foo' => 'bar'];

        $this->actingAsAdmin()
            ->put(route('admin.footer.widgets.update', compact('widget')), [
                'title' => 'The Newsletter widget',
                'template' => 'Newsletter',
                'content' => 'My Newsletter content',
                'settings' => $settings
            ])
            ->assertOk()
            ->assertSessionHasNoErrors()
            ->assertExactJson(['Widget updated']);

        $this->assertDatabaseHas(Widget::class, [
            'page_id' => 0,
            'container_id' => null,
            'enabled' => (int) $widget->enabled,
            'title' => 'The Newsletter widget',
            'description' => $widget->description,
            'visible' => 1,
            'template' => 'Newsletter',
            'settings' => json_encode(array_merge((array) $widget->settings, $settings)),
            'sort' => $widget->sort
        ]);

        // content is overridden with view
        $this->assertDatabaseMissing(Widget::class, [
            'content' => 'My Newsletter content',
        ]);
    }

    #[Test]
    public function an_admin_can_update_ContactDetails_widget(): void
    {
        $widget = Widget::factory()->create(['page_id' => 0]);

        $settings = ['foo' => 'bar'];

        $this->actingAsAdmin()
            ->put(route('admin.footer.widgets.update', compact('widget')), [
                'title' => 'The ContactDetails widget',
                'template' => 'ContactDetails',
                'content' => 'My ContactDetails content',
                'settings' => $settings
            ])
            ->assertOk()
            ->assertSessionHasNoErrors()
            ->assertExactJson(['Widget updated']);

        $this->assertDatabaseHas(Widget::class, [
            'page_id' => 0,
            'container_id' => null,
            'enabled' => (int) $widget->enabled,
            'title' => 'The ContactDetails widget',
            'description' => $widget->description,
            'visible' => 1,
            'template' => 'ContactDetails',
            'settings' => json_encode(array_merge((array) $widget->settings, $settings)),
            'sort' => $widget->sort
        ]);

        // content is overridden with view
        $this->assertDatabaseMissing(Widget::class, [
            'content' => 'My ContactDetails content',
        ]);
    }

    #[Test]
    public function an_admin_can_update_Divider_widget(): void
    {
        $widget = Widget::factory()->create(['page_id' => 0]);

        $settings = ['foo' => 'bar'];

        $this->actingAsAdmin()
            ->put(route('admin.footer.widgets.update', compact('widget')), [
                'title' => 'The Divider widget',
                'template' => 'Divider',
                'content' => 'My Divider content',
                'settings' => $settings
            ])
            ->assertOk()
            ->assertSessionHasNoErrors()
            ->assertExactJson(['Widget updated']);

        $this->assertDatabaseHas(Widget::class, [
            'page_id' => 0,
            'container_id' => null,
            'enabled' => (int) $widget->enabled,
            'title' => 'The Divider widget',
            'description' => $widget->description,
            'visible' => 1,
            'template' => 'Divider',
            'settings' => json_encode(array_merge((array) $widget->settings, $settings)),
            'sort' => $widget->sort
        ]);

        // content is overridden with view
        $this->assertDatabaseMissing(Widget::class, [
            'content' => 'My Divider content',
        ]);
    }

    #[Test]
    public function an_admin_can_update_Spacer_widget(): void
    {
        $widget = Widget::factory()->create(['page_id' => 0]);

        $settings = ['foo' => 'bar'];

        $this->actingAsAdmin()
            ->put(route('admin.footer.widgets.update', compact('widget')), [
                'title' => 'The Spacer widget',
                'template' => 'Spacer',
                'content' => 'My Spacer content',
                'settings' => $settings
            ])
            ->assertOk()
            ->assertSessionHasNoErrors()
            ->assertExactJson(['Widget updated']);

        $this->assertDatabaseHas(Widget::class, [
            'page_id' => 0,
            'container_id' => null,
            'enabled' => (int) $widget->enabled,
            'title' => 'The Spacer widget',
            'description' => $widget->description,
            'visible' => 1,
            'template' => 'Spacer',
            'settings' => json_encode(array_merge((array) $widget->settings, $settings)),
            'sort' => $widget->sort
        ]);

        // content is overridden with view
        $this->assertDatabaseMissing(Widget::class, [
            'content' => 'My Spacer content',
        ]);
    }

    #[Test]
    public function a_guest_cannot_destroy_a_footer_widget(): void
    {
        $widget = Widget::factory()->create(['page_id' => 0]);

        $this->delete(route('admin.footer.widgets.destroy', compact('widget')))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function a_non_admin_cannot_destroy_a_footer_widget(): void
    {
        $widget = Widget::factory()->create(['page_id' => 0]);

        $this->actingAsCustomer()
            ->delete(route('admin.footer.widgets.destroy', compact('widget')))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function an_admin_cannot_destroy_an_invalid_footer_widget(): void
    {
        $this->actingAsAdmin()
            ->delete(route('admin.footer.widgets.destroy', ['something']))
            ->assertRedirect();
    }

    #[Test]
    public function an_admin_can_destroy_a_footer_widget(): void
    {
        Carbon::setTestNow(now());

        $widget = Widget::factory()->create(['page_id' => 0, 'deleted_at' => null]);

        $this->actingAsAdmin()
            ->delete(route('admin.footer.widgets.destroy', compact('widget')))
            ->assertOk()
            ->assertSessionHasNoErrors()
            ->assertExactJson(['Widget deleted']);

        $this->assertDatabaseHas(Widget::class, [
            'id' => $widget->id,
            'deleted_at' => now()
        ]);

        Carbon::setTestNow();
    }
}