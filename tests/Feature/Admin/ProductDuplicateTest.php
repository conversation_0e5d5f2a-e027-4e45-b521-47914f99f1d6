<?php

namespace Tests\Feature\Admin;

use App\Models\Product;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class ProductDuplicateTest extends TenantTestCase
{
    #[Test]
    public function guests_cannot_duplicate_products(): void
    {
        $product = Product::factory()->create();

        $this->post(route('admin.products.duplicate', ['product' => $product->id]), [
                'title' => 'Duplicate Product',
            ])
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function non_admins_cannot_duplicate_products(): void
    {
        $product = Product::factory()->create();

        $this->actingAsCustomer()
            ->post(route('admin.products.duplicate', ['product' => $product->id]), [
                'title' => 'Duplicate Product',
            ])
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function it_validates_the_duplicate_request(): void
    {
        $product = Product::factory()->create();

        $this->actingAsAdmin()
            ->post(route('admin.products.duplicate', ['product' => $product->id]), [
                'title' => '',
            ])
            ->assertSessionHasErrors(['title']);
    }

    #[Test]
    public function it_duplicates_a_product(): void
    {
        $product = Product::factory()->create();

        $response = $this->actingAsAdmin()
            ->post(route('admin.products.duplicate', ['product' => $product->id]), [
                'title' => 'Duplicate Product',
            ]);

        $duplicate = Product::latest('id')->first();

        $this->assertNotEquals($product->id, $duplicate->id);

        $response->assertRedirect(route('admin.products.edit', [
            'product' => $duplicate->id,
            'tab' => 'description',
        ]));
    }
}
