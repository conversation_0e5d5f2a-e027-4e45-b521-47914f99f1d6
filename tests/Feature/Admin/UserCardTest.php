<?php

namespace Tests\Feature\Admin;

use App\Actions\Billing\AddSourceToCustomer;
use App\Actions\Billing\RemoveCard;
use App\Models\Card;
use App\Models\User;
use Illuminate\Support\Carbon;
use Mockery\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class UserCardTest extends TenantTestCase
{
    #[Test]
    public function an_unauthenticated_user_cannot_add_a_new_card(): void
    {
        $user = User::factory()->create();

        $this->post(route('admin.users.cards.store', [$user]))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function an_non_admin_cannot_add_a_new_card(): void
    {
        $user = User::factory()->create();

        $this->actingAsCustomer()
            ->post(route('admin.users.cards.store', [$user]))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function it_validates_the_add_new_card_request_for_stripe(): void
    {
        $user = User::factory()->create();

        $this->actingAsAdmin()
            ->post(route('admin.users.cards.store', [$user]))
            ->assertRedirect()
            ->assertInvalid([
                'token' => 'The token field is required.',
                'cardholder_name' => 'The cardholder name field is required.'
            ]);
    }

    #[Test]
    public function an_admin_can_add_a_card_to_a_user_for_stripe(): void
    {
        $user = User::factory()->create();

        $request = [
            'token' => 'tok_123',
            'cardholder_name' => 'First Last'
        ];

        $this->mock(AddSourceToCustomer::class, function (MockInterface $mock) use ($user, $request) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(
                    \Mockery::on(fn(User $arg) => $arg->id === $user->id),
                    'tok_123',
                    $request,
                )
                ->andReturn(Card::factory()->make());
        });

        $this->actingAsAdmin()
            ->post(route('admin.users.cards.store', [$user]), $request)
            ->assertRedirect()
            ->assertSessionDoesntHaveErrors()
            ->assertSessionHas('flash_notification', [
                'level' => 'info',
                'message' => 'The card has been added to the customer!'
            ]);
    }

    #[Test]
    public function an_unauthenticated_user_cannot_destroy_a_card(): void
    {
        $card = Card::factory()->create();

        $this->delete(route('admin.users.cards.destroy', [$card->user, $card]))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function an_admin_cannot_destroy_an_invalid_card(): void
    {
        $card = Card::factory()->create();

        $this->actingAsAdmin()
            ->delete(route('admin.users.cards.destroy', [$card->user, 123123123123]))
            ->assertRedirect('/');
    }

    #[Test]
    public function an_admin_cannot_destroy_a_card_for_an_invalid_user(): void
    {
        $card = Card::factory()->create();

        $this->actingAsAdmin()
            ->delete(route('admin.users.cards.destroy', [123123123123, $card]))
            ->assertRedirect('/');
    }

    #[Test]
    public function an_admin_cannot_destroy_a_card_for_non_matching_user(): void
    {
        $user = User::factory()->create(['customer_id' => 'abc_123']);
        $card = Card::factory()->create();

        $this->actingAsAdmin()
            ->delete(route('admin.users.cards.destroy', [$user, $card]))
            ->assertConflict();
    }

    #[Test]
    public function an_admin_can_destroy_a_card(): void
    {
        Carbon::setTestNow(now());

        $user = User::factory()->create(['customer_id' => 'abc_123']);

        $card = Card::factory()->create([
            'user_id' => $user->id,
            'source_id' => 'src_123',
            'default' => true,
        ]);

        $this->mock(RemoveCard::class, function (MockInterface $mock) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(
                    \Mockery::on(fn(string $arg) => $arg === 'abc_123'),
                    \Mockery::on(fn(string $arg) => $arg === 'src_123'),
                )
                ->andReturnUndefined();
        });

        $this->actingAsAdmin()
            ->delete(route('admin.users.cards.destroy', [$card->user, $card]))
            ->assertRedirect()
            ->assertSessionDoesntHaveErrors()
            ->assertSessionHas('flash_notification', [
                'level' => 'info',
                'message' => 'The card has been deleted!'
            ]);

        Carbon::setTestNow();
    }
}
