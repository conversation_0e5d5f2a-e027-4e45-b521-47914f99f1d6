<?php

namespace Tests\Feature\Admin;

use App\Models\Location;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class LocationTest extends TenantTestCase
{
    #[Test]
    public function a_non_admin_cannot_view_the_various_locations_list(): void
    {
        $this->actingAsCustomer()
            ->get(route('admin.locations.index', ['locations' => 'market']))
            ->assertRedirect(route('admin.login'));

        $this->get(route('admin.locations.index', ['locations' => 'restaurant']))
            ->assertRedirect(route('admin.login'));

        $this->get(route('admin.locations.index', ['locations' => 'retail']))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function an_admin_can_view_the_various_locations_list(): void
    {
        $this->actingAsAdmin()
            ->get(route('admin.locations.index', ['locations' => 'market']))
            ->assertOk()
            ->assertViewIs('locations.market.index');

        $this->get(route('admin.locations.index', ['locations' => 'restaurant']))
            ->assertOk()
            ->assertViewIs('locations.restaurant.index');

        $this->get(route('admin.locations.index', ['locations' => 'retail']))
            ->assertOk()
            ->assertViewIs('locations.retail.index');

        $this->get(route('admin.locations.index'))
            ->assertOk()
            ->assertViewIs('locations.market.index');
    }

    #[Test]
    public function an_admin_cannot_view_invalid_locations(): void
    {
        $this->actingAsAdmin()
            ->get(route('admin.locations.index', ['locations' => 'something']))
            ->assertNotFound();
    }

    #[Test]
    public function it_validates_when_creating_location(): void
    {
        $location = [
            'title' => 'Mt Everest',
            'street' => '123 Road',
            'city' => 'No Lands',
            'state' => 'Of Mind',
            'zip' => '123456798900',
            'type' => 'market',
        ];

        $this->actingAsAdmin()
            ->post(route('admin.locations.store', []))
            ->assertInvalid([
                'title' => 'A location name is required',
                'street' => 'Street is required',
                'city' => 'City is required',
                'state' => 'State is required',
                'type' => 'Location type is required',
            ]);

        $this->post(route('admin.locations.store', $location))
            ->assertInvalid([
                'zip' => 'The zip field must not be greater than 10 characters.',
            ]);
    }

    #[Test]
    public function it_can_create_new_location(): void
    {
        $location = [
            'title' => 'Mt Everest',
            'street' => '123 Road',
            'city' => 'No Lands',
            'state' => 'IL',
            'zip' => '12349',
            'type' => 'market',
        ];

        $this->actingAsAdmin()
            ->post(route('admin.locations.store', $location))
            ->assertSessionHasNoErrors();


        $this->assertDatabaseHas(Location::class, [
            'title' => 'Mt Everest',
            'street' => '123 Road',
            'city' => 'No Lands',
            'state' => 'IL',
            'zip' => '12349',
            'type' => 'market',
        ]);
    }

    #[Test]
    public function it_validates_when_updating_location(): void
    {
        $location = Location::factory()->create(['state' => 'MA']);

        $this->actingAsAdmin()
            ->put(route('admin.locations.update', compact('location')), ['title' => 'Mt Everest'])
            ->assertSessionHasNoErrors();

        $this->assertDatabaseHas(Location::class, [
            'title' => 'Mt Everest',
            'street' => $location->street,
            'city' => $location->city,
            'state' => $location->state,
            'zip' => $location->zip,
            'type' => $location->type,
        ]);
    }
}
