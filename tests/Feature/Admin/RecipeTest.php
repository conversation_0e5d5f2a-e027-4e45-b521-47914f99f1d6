<?php

namespace Tests\Feature\Admin;

use App\Models\Recipe;
use App\Models\User;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Carbon;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class RecipeTest extends TenantTestCase
{
    #[Test]
    public function it_requires_an_authenticated_user_to_view_recipes(): void
    {
        $this->get(route('admin.recipes.index'))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function only_an_admin_can_view_recipes(): void
    {
        $this->actingAsCustomer()
            ->get(route('admin.recipes.index'))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function it_shows_a_list_of_recipes(): void
    {
        Recipe::factory()->times(2)->create();

        $this->actingAsAdmin()
            ->get(route('admin.recipes.index'))
            ->assertOk()
            ->assertViewIs('recipes.index')
            ->assertViewHas('recipes', function (LengthAwarePaginator $arg) {
                return $arg->count() === 2;
            });
    }

    #[Test]
    public function it_can_filter_recipes_by_title(): void
    {
        Recipe::factory()->create(['title' => 'abcde']);
        $expected = Recipe::factory()->create(['title' => 'vwxyz']);

        $this->actingAsAdmin()
            ->get(route('admin.recipes.index', ['recipes' => 'wxy']))
            ->assertOk()
            ->assertViewHas('recipes', function (LengthAwarePaginator $arg) use ($expected) {
                return $arg->contains(fn (Recipe $recipe) => $recipe->id === $expected->id);
            });
    }

    #[Test]
    public function it_can_filter_recipes_by_author(): void
    {
        $user_one = User::factory()->create();
        Recipe::factory()->create(['user_id' => $user_one->id]);

        $user_two = User::factory()->create();
        $expected = Recipe::factory()->create(['user_id' => $user_two->id]);

        $this->actingAsAdmin()
            ->get(route('admin.recipes.index', ['user_id' => $user_two->id]))
            ->assertOk()
            ->assertViewHas('recipes', function (LengthAwarePaginator $arg) use ($expected) {
                return $arg->contains(fn (Recipe $recipe) => $recipe->id === $expected->id);
            });
    }

    #[Test]
    public function it_can_filter_recipes_by_published_date(): void
    {
        $now = now();
        Recipe::factory()->create(['published_at' => $now->copy()->subDays(5)]);

        /** @var Recipe $expected */
        $expected = Recipe::factory()->create(['published_at' => $now->copy()->addDays(5)]);

        $date_string = "{$expected->published_at->copy()->subDay()->format('Y-m-d')} - {$expected->published_at->copy()->addDay()->format('Y-m-d')}";

        $this->actingAsAdmin()
            ->get(route('admin.recipes.index', ['published_date' => $date_string]))
            ->assertOk()
            ->assertViewHas('recipes', function (LengthAwarePaginator $arg) use ($expected) {
                return $arg->contains(fn (Recipe $recipe) => $recipe->id === $expected->id);
            });
    }

    #[Test]
    public function it_can_sort_recipes_by_attributes(): void
    {
        /** @var Recipe $recipe_one  */
        $recipe_one = Recipe::factory()->create([
            'title' => 'abc',
            'published_at' => now()->subDays(5)
        ]);

        /** @var Recipe $recipe_two */
        $recipe_two = Recipe::factory()->create([
            'title' => 'xyz',
            'published_at' => now()->addDays(5)
        ]);

        $this->actingAsAdmin()
            ->get(route('admin.recipes.index', ['orderBy' => 'title', 'sort' => 'asc']))
            ->assertOk()
            ->assertViewHas('recipes', function (LengthAwarePaginator $arg) use ($recipe_one, $recipe_two) {
                $recipeIds = $arg->map(function (Recipe $recipe) {
                    return $recipe->id;
                });

                return $recipeIds->search($recipe_one->id) < $recipeIds->search($recipe_two->id);
            });

        $this->get(route('admin.recipes.index', ['orderBy' => 'title', 'sort' => 'desc']))
            ->assertOk()
            ->assertViewHas('recipes', function (LengthAwarePaginator $arg) use ($recipe_one, $recipe_two) {
                $recipeIds = $arg->map(function (Recipe $recipe) {
                    return $recipe->id;
                });

                return $recipeIds->search($recipe_two->id) < $recipeIds->search($recipe_one->id);
            });

        $this->actingAsAdmin()
            ->get(route('admin.recipes.index', ['orderBy' => 'published_at', 'sort' => 'asc']))
            ->assertOk()
            ->assertViewHas('recipes', function (LengthAwarePaginator $arg) use ($recipe_one, $recipe_two) {
                $recipeIds = $arg->map(function (Recipe $recipe) {
                    return $recipe->id;
                });

                return $recipeIds->search($recipe_one->id) < $recipeIds->search($recipe_two->id);
            });

        $this->get(route('admin.recipes.index', ['orderBy' => 'published_at', 'sort' => 'desc']))
            ->assertOk()
            ->assertViewHas('recipes', function (LengthAwarePaginator $arg) use ($recipe_one, $recipe_two) {
                $recipeIds = $arg->map(function (Recipe $recipe) {
                    return $recipe->id;
                });

                return $recipeIds->search($recipe_two->id) < $recipeIds->search($recipe_one->id);
            });
    }

    #[Test]
    public function it_sorts_by_asc_when_using_invalid_sort_attribute(): void
    {
        /** @var Recipe $recipe_one  */
        $recipe_one = Recipe::factory()->create([
            'title' => 'abc',
            'published_at' => now()->subDays(5)
        ]);

        /** @var Recipe $recipe_two */
        $recipe_two = Recipe::factory()->create([
            'title' => 'xyz',
            'published_at' => now()->addDays(5)
        ]);

        $this->actingAsAdmin()
            ->get(route('admin.recipes.index', ['orderBy' => 'title', 'sort' => 'abc']))
            ->assertOk()
            ->assertViewHas('recipes', function (LengthAwarePaginator $arg) use ($recipe_one, $recipe_two) {
                $recipeIds = $arg->map(fn(Recipe $recipe) => $recipe->id);

                return $recipeIds->search($recipe_one->id) < $recipeIds->search($recipe_two->id);
            });
    }

    #[Test]
    public function a_non_admin_cannot_create_a_recipe(): void
    {
        $this->actingAsCustomer()
            ->post(route('admin.recipes.store'))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function it_validates_the_recipe_creation_request(): void
    {
        $this->actingAsAdmin()
            ->post(route('admin.recipes.store'))
            ->assertInvalid(['title' => 'The title field is required.']);

        $this->post(route('admin.recipes.store'), ['user_id' => 'a'])
            ->assertInvalid(['user_id' => 'The user id field must be an integer.']);

        $this->post(route('admin.recipes.store'), ['user_id' => 123123123])
            ->assertInvalid(['user_id' => 'The selected user id is invalid.']);
    }

    #[Test]
    public function it_creates_an_authored_recipe(): void
    {
        Carbon::setTestNow(now());

        $admin = User::factory()->admin()->create();

        $response = $this->actingAs($admin)
            ->post(route('admin.recipes.store'), [
                'title' => 'my test recipe',
                'user_id' => $admin->id
            ])
            ->assertSessionHasNoErrors();

        $expected_params = [
            'title' => 'my test recipe',
            'published' => false,
            'published_at' => now()->format('Y-m-d H:i:s'),
            'slug' => 'my-test-recipe',
            'user_id' => $admin->id
        ];

        $this->assertDatabaseHas(Recipe::class, $expected_params);

        $recipe = Recipe::where($expected_params)->first();

        $response->assertRedirect(route('admin.recipes.edit', [$recipe->slug]));

        Carbon::setTestNow();
    }

    #[Test]
    public function it_creates_an_anonymous_recipe(): void
    {
        Carbon::setTestNow(now());

        $response = $this->actingAsAdmin()
            ->post(route('admin.recipes.store'), [
                'title' => 'my test recipe',
                'user_id' => ''
            ])
            ->assertSessionHasNoErrors();

        $expected_params = [
            'title' => 'my test recipe',
            'published' => false,
            'published_at' => now()->format('Y-m-d H:i:s'),
            'slug' => 'my-test-recipe',
            'user_id' => null
        ];

        $this->assertDatabaseHas(Recipe::class, $expected_params);

        $recipe = Recipe::where($expected_params)->first();

        $response->assertRedirect(route('admin.recipes.edit',[$recipe->slug]));

        Carbon::setTestNow();
    }
}