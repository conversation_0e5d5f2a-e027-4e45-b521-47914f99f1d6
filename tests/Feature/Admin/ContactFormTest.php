<?php

namespace Tests\Feature\Admin;

use App\Events\ContactFormSubmitted;
use Illuminate\Support\Facades\Event;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class ContactFormTest extends TenantTestCase
{
    #[Test]
    public function the_contact_form_page_can_be_viewed(): void
    {
        $this->get('/contact')
            ->assertOk()
            ->assertSeeText('Send Message');
    }

    public function it_fires_event_on_successful_contact_form_submission()
    {
        Event::fake();

        $this->get('/contact')
            ->assertOk();

        $this->post('/contact', [
            'full_name' => '<PERSON>',
            'topic' => 'Hello World',
            'email' => '<EMAIL>',
            'body' => 'Test message'
        ])
            ->assertSessionDoesntHaveErrors()
            ->assertRedirect('/contact')
            ->assertSessionHas(['flash_notification' => [
                'message' => 'Thank you! Your message has been sent.',
                'level' => 'info'
            ]]);

        Event::assertDispatched(
            ContactFormSubmitted::class,
            function (ContactFormSubmitted $event) {
                return $event->body === 'Test message' &&
                    $event->full_name === '<PERSON>' &&
                    $event->email === '<EMAIL>' &&
                    $event->topic === 'Hello World';
            }
        );
    }
}
