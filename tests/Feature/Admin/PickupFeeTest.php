<?php

namespace Tests\Feature\Admin;

use App\Models\Pickup;
use App\Models\PickupFee;
use Illuminate\Support\Str;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class PickupFeeTest extends TenantTestCase
{
    #[Test]
    public function a_non_admin_cannot_view_fees_associated_to_a_pickup_location(): void
    {
        $pickup = Pickup::factory()->create();

        $this->actingAsCustomer()
            ->get(route('admin.pickups.fees.index', compact('pickup')))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function an_admin_cannot_view_fees_associated_to_an_unknown_pickup_location(): void
    {
        $this->actingAsAdmin()
            ->get(route('admin.pickups.index'))
            ->assertOk();

        $this->get(route('admin.pickups.fees.index', ['pickup' => 'abc']))
            ->assertRedirect(route('admin.pickups.index'));
    }

    #[Test]
    public function an_admin_can_view_fees_associated_to_a_pickup_location(): void
    {
        $pickup = Pickup::factory()->create();

        $fees = PickupFee::factory()->count(2)->create(['pickup_id' => $pickup->id]);
        $other_fees = PickupFee::factory()->count(2)->create();

        $response = $this->actingAsAdmin()
            ->get(route('admin.pickups.fees.index', compact('pickup')))
            ->assertOk()
            ->assertJsonCount(2);

        $fees->each(function (PickupFee $fee) use ($response) {
            $response->assertJsonFragment(['id' => $fee->id]);
        });

        $other_fees->each(function (PickupFee $fee) use ($response) {
            $response->assertJsonMissing(['id' => $fee->id]);
        });
    }

    #[Test]
    public function a_non_admin_cannot_create_a_pickup_location_fee(): void
    {
        $pickup = Pickup::factory()->create();

        $this->actingAsCustomer()
            ->post(route('admin.pickups.fees.store', compact('pickup')))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function an_admin_cannot_create_a_fee_associated_to_an_unknown_pickup_location(): void
    {
        $this->actingAsAdmin()
            ->get(route('admin.pickups.index'))
            ->assertOk();

        $this->post(route('admin.pickups.fees.store', ['pickup' => 'abc']))
            ->assertRedirect(route('admin.pickups.index'));
    }

    #[Test]
    public function it_validates_the_title_when_creating_a_pickup_fee(): void
    {
        $pickup = Pickup::factory()->create();

        $this->actingAsAdmin()
            ->post(route('admin.pickups.fees.store', compact('pickup')))
            ->assertSessionHasErrors(['title' => 'The title field is required.']);

        $this->actingAsAdmin()
            ->post(route('admin.pickups.fees.store', compact('pickup')), ['title' => ''])
            ->assertSessionHasErrors(['title' => 'The title field is required.']);

        $this->actingAsAdmin()
            ->post(route('admin.pickups.fees.store', compact('pickup')), [
                'title' => Str::random(256)
            ])
            ->assertSessionHasErrors(['title' => 'The title field must not be greater than 255 characters.']);
    }

    #[Test]
    public function it_validates_the_amount_when_creating_a_pickup_fee(): void
    {
        $pickup = Pickup::factory()->create();

        $this->actingAsAdmin()
            ->post(route('admin.pickups.fees.store', compact('pickup')))
            ->assertSessionHasErrors(['amount' => 'The amount field is required.']);

        $this->actingAsAdmin()
            ->post(route('admin.pickups.fees.store', compact('pickup')), ['amount' => ''])
            ->assertSessionHasErrors(['amount' => 'The amount field is required.']);

        $this->actingAsAdmin()
            ->post(route('admin.pickups.fees.store', compact('pickup')), ['amount' => 'abc'])
            ->assertSessionHasErrors(['amount' => 'The amount field must be a number.']);

        $this->actingAsAdmin()
            ->post(route('admin.pickups.fees.store', compact('pickup')), ['amount' => -1])
            ->assertSessionHasErrors(['amount' => 'The amount field must be at least 0.']);
    }

    #[Test]
    public function it_validates_the_note_when_creating_a_pickup_fee(): void
    {
        $pickup = Pickup::factory()->create();

        $this->actingAsAdmin()
            ->post(route('admin.pickups.fees.store', compact('pickup')), ['note' => -1])
            ->assertSessionHasErrors(['note' => 'The note field must be a string.']);
    }

    #[Test]
    public function it_validates_the_taxable_when_creating_a_pickup_fee(): void
    {
        $pickup = Pickup::factory()->create();

        $this->actingAsAdmin()
            ->post(route('admin.pickups.fees.store', compact('pickup')), ['taxable' => 'abc'])
            ->assertSessionHasErrors(['taxable' => 'The taxable field must be true or false.']);
    }

    #[Test]
    public function it_validates_the_apply_limit_when_creating_a_pickup_fee(): void
    {
        $pickup = Pickup::factory()->create();

        $this->actingAsAdmin()
            ->post(route('admin.pickups.fees.store', compact('pickup')), ['apply_limit' => 'abc'])
            ->assertSessionHasErrors(['apply_limit' => 'The apply limit field must be true or false.']);
    }

    #[Test]
    public function it_can_create_a_pickup_location_fee(): void
    {
        $pickup = Pickup::factory()->create();

        $this->actingAsAdmin()
            ->post(route('admin.pickups.fees.store', compact('pickup')), [
                'title' => 'new fee title',
                'amount' => 123,
                'note' => 'new fee notes',
                'taxable' => true,
                'apply_limit' => true,
            ])
            ->assertCreated();

        $this->assertDatabaseHas('pickup_fees', [
            'pickup_id' => $pickup->id,
            'title' => 'new fee title',
            'amount' => 12300, // 123 * 100
            'note' => 'new fee notes',
            'taxable' => true,
            'apply_limit' => true,
        ]);
    }

    #[Test]
    public function it_ignores_unknown_properties_when_creating_a_fee(): void
    {
        $pickup = Pickup::factory()->create();

        $currentPickupFeeCount = PickupFee::count();

        $this->actingAsAdmin()
            ->post(route('admin.pickups.fees.store', compact('pickup')), [
                'title' => 'new fee title',
                'amount' => 123,
                'note' => 'new fee notes',
                'taxable' => true,
                'apply_limit' => true,
                'some' => 'other'
            ])
            ->assertCreated();

        $this->assertDatabaseCount(PickupFee::class, $currentPickupFeeCount + 1);
    }

    #[Test]
    public function a_non_admin_cannot_update_a_pickup_location_fee(): void
    {
        $fee = PickupFee::factory()->create();

        $this->actingAsCustomer()
            ->put(route('admin.pickups.fees.update', ['pickup' => $fee->pickup_id, 'fee' => $fee]))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function an_admin_cannot_update_a_known_fee_associated_to_an_unknown_pickup_location(): void
    {
        $fee = PickupFee::factory()->create();

        $this->actingAsAdmin()
            ->get(route('admin.pickups.index'))
            ->assertOk();

        $this->put(route('admin.pickups.fees.update', ['pickup' => 'abc', 'fee' => $fee]))
            ->assertRedirect(route('admin.pickups.index'));
    }

    #[Test]
    public function an_admin_cannot_update_an_unknown_fee_associated_to_a_known_pickup_location(): void
    {
        $pickup = Pickup::factory()->create();

        $this->actingAsAdmin()
            ->get(route('admin.pickups.index'))
            ->assertOk();

        $this->put(route('admin.pickups.fees.update', ['pickup' => $pickup, 'fee' => 'abc']))
            ->assertRedirect(route('admin.pickups.index'));
    }

    #[Test]
    public function an_admin_cannot_update_an_unknown_fee_associated_to_an_unknown_pickup_location(): void
    {
        $this->actingAsAdmin()
            ->get(route('admin.pickups.index'))
            ->assertOk();

        $this->put(route('admin.pickups.fees.update', ['pickup' => 'abc', 'fee' => 'abc']))
            ->assertRedirect(route('admin.pickups.index'));
    }

    #[Test]
    public function an_admin_cannot_update_a_known_fee_associated_to_a_known_pickup_location_when_pickup_ids_dont_match(): void
    {
        $pickup = Pickup::factory()->create();
        $fee = PickupFee::factory()->create();

        $this->actingAsAdmin()
            ->get(route('admin.pickups.index'))
            ->assertOk();

        $this->put(route('admin.pickups.fees.update', compact('pickup', 'fee')))
            ->assertNotFound();
    }

    #[Test]
    public function it_validates_the_title_when_updating_a_pickup_fee(): void
    {
         $fee = PickupFee::factory()->create();

        $this->actingAsAdmin()
            ->put(route('admin.pickups.fees.update', ['pickup' => $fee->pickup_id, 'fee' => $fee]))
            ->assertSessionHasErrors(['title' => 'The title field is required.']);

        $this->actingAsAdmin()
            ->put(route('admin.pickups.fees.update', ['pickup' => $fee->pickup_id, 'fee' => $fee]), ['title' => ''])
            ->assertSessionHasErrors(['title' => 'The title field is required.']);

        $this->actingAsAdmin()
            ->put(route('admin.pickups.fees.update', ['pickup' => $fee->pickup_id, 'fee' => $fee]), [
                'title' => Str::random(256)
            ])
            ->assertSessionHasErrors(['title' => 'The title field must not be greater than 255 characters.']);
    }

    #[Test]
    public function it_validates_the_amount_when_updating_a_pickup_fee(): void
    {
         $fee = PickupFee::factory()->create();

        $this->actingAsAdmin()
            ->put(route('admin.pickups.fees.update', ['pickup' => $fee->pickup_id, 'fee' => $fee]))
            ->assertSessionHasErrors(['amount' => 'The amount field is required.']);

        $this->actingAsAdmin()
            ->put(route('admin.pickups.fees.update', ['pickup' => $fee->pickup_id, 'fee' => $fee]), ['amount' => ''])
            ->assertSessionHasErrors(['amount' => 'The amount field is required.']);

        $this->actingAsAdmin()
            ->put(route('admin.pickups.fees.update', ['pickup' => $fee->pickup_id, 'fee' => $fee]), ['amount' => 'abc'])
            ->assertSessionHasErrors(['amount' => 'The amount field must be a number.']);

        $this->actingAsAdmin()
            ->put(route('admin.pickups.fees.update', ['pickup' => $fee->pickup_id, 'fee' => $fee]), ['amount' => -1])
            ->assertSessionHasErrors(['amount' => 'The amount field must be at least 0.']);
    }

    #[Test]
    public function it_validates_the_note_when_updating_a_pickup_fee(): void
    {
         $fee = PickupFee::factory()->create();

        $this->actingAsAdmin()
            ->put(route('admin.pickups.fees.update', ['pickup' => $fee->pickup_id, 'fee' => $fee]), ['note' => -1])
            ->assertSessionHasErrors(['note' => 'The note field must be a string.']);
    }

    #[Test]
    public function it_validates_the_taxable_when_updating_a_pickup_fee(): void
    {
         $fee = PickupFee::factory()->create();

        $this->actingAsAdmin()
            ->put(route('admin.pickups.fees.update', ['pickup' => $fee->pickup_id, 'fee' => $fee]), ['taxable' => 'abc'])
            ->assertSessionHasErrors(['taxable' => 'The taxable field must be true or false.']);
    }

    #[Test]
    public function it_validates_the_apply_limit_when_updating_a_pickup_fee(): void
    {
         $fee = PickupFee::factory()->create();

        $this->actingAsAdmin()
            ->put(route('admin.pickups.fees.update', ['pickup' => $fee->pickup_id, 'fee' => $fee]), ['apply_limit' => 'abc'])
            ->assertSessionHasErrors(['apply_limit' => 'The apply limit field must be true or false.']);
    }

    #[Test]
    public function it_can_update_a_pickup_location_fee(): void
    {
        $fee = PickupFee::factory()->create([
            'title' => 'old fee title',
            'amount' => 321,
            'note' => 'old fee notes',
            'taxable' => false,
            'apply_limit' => false,
        ]);

        $this->actingAsAdmin()
            ->put(route('admin.pickups.fees.update', ['pickup' => $fee->pickup_id, 'fee' => $fee]), [
                'title' => 'new fee title',
                'amount' => 123,
                'note' => 'new fee notes',
                'taxable' => true,
                'apply_limit' => true,
            ])
            ->assertOk();

        $this->assertDatabaseHas('pickup_fees', [
            'id' => $fee->id,
            'pickup_id' => $fee->pickup_id,
            'title' => 'new fee title',
            'amount' => 12300, // 123 * 100
            'note' => 'new fee notes',
            'taxable' => true,
            'apply_limit' => true,
        ]);
    }

    #[Test]
    public function it_ignores_unknown_properties_when_updating_a_fee(): void
    {
        $fee = PickupFee::factory()->create();

        $this->assertDatabaseCount(PickupFee::class, 1);

        $this->actingAsAdmin()
            ->put(route('admin.pickups.fees.update', ['pickup' => $fee->pickup_id, 'fee' => $fee]), [
                'title' => 'new fee title',
                'amount' => 123,
                'note' => 'new fee notes',
                'taxable' => true,
                'apply_limit' => true,
                'some' => 'other'
            ])
            ->assertOk();

        $this->assertDatabaseCount(PickupFee::class, 1);
    }

    #[Test]
    public function a_non_admin_cannot_destroy_a_pickup_location_fee(): void
    {
        $fee = PickupFee::factory()->create();

        $this->actingAsCustomer()
            ->delete(route('admin.pickups.fees.destroy', ['pickup' => $fee->pickup_id, 'fee' => $fee]))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function an_admin_cannot_destroy_a_known_fee_associated_to_an_unknown_pickup_location(): void
    {
        $fee = PickupFee::factory()->create();

        $this->actingAsAdmin()
            ->get(route('admin.pickups.index'))
            ->assertOk();

        $this->delete(route('admin.pickups.fees.destroy', ['pickup' => 'abc', 'fee' => $fee]))
            ->assertRedirect(route('admin.pickups.index'));
    }

    #[Test]
    public function an_admin_cannot_destroy_an_unknown_fee_associated_to_a_known_pickup_location(): void
    {
        $pickup = Pickup::factory()->create();

        $this->actingAsAdmin()
            ->get(route('admin.pickups.index'))
            ->assertOk();

        $this->delete(route('admin.pickups.fees.destroy', ['pickup' => $pickup, 'fee' => 'abc']))
            ->assertRedirect(route('admin.pickups.index'));
    }

    #[Test]
    public function an_admin_cannot_destroy_an_unknown_fee_associated_to_an_unknown_pickup_location(): void
    {
        $this->actingAsAdmin()
            ->get(route('admin.pickups.index'))
            ->assertOk();

        $this->delete(route('admin.pickups.fees.destroy', ['pickup' => 'abc', 'fee' => 'abc']))
            ->assertRedirect(route('admin.pickups.index'));
    }

    #[Test]
    public function an_admin_cannot_destroy_a_known_fee_associated_to_a_known_pickup_location_when_pickup_ids_dont_match(): void
    {
        $pickup = Pickup::factory()->create();
        $fee = PickupFee::factory()->create();

        $this->actingAsAdmin()
            ->get(route('admin.pickups.index'))
            ->assertOk();

        $this->delete(route('admin.pickups.fees.destroy', compact('pickup', 'fee')))
            ->assertNotFound();
    }

    #[Test]
    public function it_can_destroy_a_pickup_location_fee(): void
    {
        $fee = PickupFee::factory()->create();

        $this->actingAsAdmin()
            ->delete(route('admin.pickups.fees.destroy', ['pickup' => $fee->pickup_id, 'fee' => $fee]))
            ->assertOk();

        $this->assertDatabaseMissing('pickup_fees', ['id' => $fee->id]);
    }
}