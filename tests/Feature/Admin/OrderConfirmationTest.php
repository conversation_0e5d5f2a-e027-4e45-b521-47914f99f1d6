<?php

namespace Tests\Feature\Admin;

use App\Actions\Order\Confirm;
use App\Events\Order\OrderWasConfirmed;
use App\Models\Order;
use App\Models\OrderFee;
use App\Models\OrderItem;
use App\Models\Payment;
use App\Models\Pickup;
use App\Models\PickupFee;
use App\Models\Product;
use App\Models\Schedule;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Event;
use Mockery;
use Mockery\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class OrderConfirmationTest extends TenantTestCase
{
    #[Test]
    public function it_requires_an_authenticated_user_to_confirm_an_order(): void
    {
        $order = Order::factory()->create();

        $this->post(route('admin.orders.confirm.store', compact('order')))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function it_requires_an_admin_to_confirm_an_order(): void
    {
        $order = Order::factory()->create();

        $this->actingAsCustomer()
            ->post(route('admin.orders.confirm.store', compact('order')))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function it_cannot_confirm_an_unknown_order(): void
    {
        $this->actingAsAdmin()
            ->get(route('dashboard'));

        $this->post(route('admin.orders.confirm.store', ['order' => 0]))
            ->assertRedirect(route('dashboard'));
    }

    #[Test]
    public function it_validates_the_request(): void
    {
        $order = Order::factory()->create(['confirmed' => false]);

        $this->actingAsAdmin()
            ->get(route('dashboard'));

        $this->post(route('admin.orders.confirm.store', compact('order')))
            ->assertRedirect(route('dashboard'))
            ->assertSessionHasErrors([
                'customer_first_name' => 'The customer first name field is required.',
                'customer_last_name' => 'The customer last name field is required.',
                'customer_phone' => 'The customer phone field is required.',
                'customer_email' => 'The customer email field is required.',
                'payment_id' => 'The payment id field is required.'
            ]);

        $this->post(route('admin.orders.confirm.store', compact('order')), [
            'customer_first_name' => '',
            'customer_last_name' => '',
            'customer_phone' => '',
            'customer_email' => '',
            'payment_id' => ''
        ])
            ->assertRedirect(route('dashboard'))
            ->assertSessionHasErrors([
                'customer_first_name' => 'The customer first name field is required.',
                'customer_last_name' => 'The customer last name field is required.',
                'customer_phone' => 'The customer phone field is required.',
                'customer_email' => 'The customer email field is required.',
                'payment_id' => 'The payment id field is required.',
                'type_id' => 'The type id field is required.'
            ]);

        $this->post(route('admin.orders.confirm.store', compact('order')), [
            'customer_email' => 'invalid email'
        ])
            ->assertRedirect(route('dashboard'))
            ->assertSessionHasErrors([
                'customer_email' => 'The customer email field must be a valid email address.',
            ]);

        $this->post(route('admin.orders.confirm.store', compact('order')), [
            'payment_id' => 0
        ])
            ->assertRedirect(route('dashboard'))
            ->assertSessionHasErrors([
                'payment_id' => 'The selected payment id is invalid.',
            ]);

        $this->post(route('admin.orders.confirm.store', compact('order')), [
            'type_id' => 0
        ])
            ->assertRedirect(route('dashboard'))
            ->assertSessionHasErrors([
                'type_id' => 'The selected type id is invalid.',
            ]);
    }

    #[Test]
    public function it_does_not_confirm_an_already_confirmed_order(): void
    {
        $payment = Payment::factory()->create();
        $order = Order::factory()->create(['confirmed' => true]);

        $this->mock(Confirm::class, function (MockInterface $mock) {
            return $mock->shouldNotReceive('handle');
        });

        $this->actingAsAdmin()
            ->get(route('dashboard'));

        $this->post(route('admin.orders.confirm.store', compact('order')), [
            'customer_first_name' => 'first',
            'customer_last_name' => 'last',
            'customer_phone' => '************',
            'customer_email' => '<EMAIL>',
            'payment_id' => $payment->id,
            'type_id' => 1
        ])
            ->assertRedirect(route('dashboard'))
            ->assertSessionDoesntHaveErrors()
            ->assertSessionHas('flash_notification', [
                'message' => 'The order has already been confirmed.',
                'level' => 'error',
            ]);
    }

    #[Test]
    public function it_does_not_confirm_an_order_without_a_pickup_location(): void
    {
        $payment = Payment::factory()->create();
        $order = Order::factory()->create(['confirmed' => false, 'pickup_id' => 0]);

        $this->mock(Confirm::class, function (MockInterface $mock) {
            return $mock->shouldNotReceive('handle');
        });

        $this->actingAsAdmin()
            ->get(route('dashboard'));

        $this->post(route('admin.orders.confirm.store', compact('order')), [
            'customer_first_name' => 'first',
            'customer_last_name' => 'last',
            'customer_phone' => '************',
            'customer_email' => '<EMAIL>',
            'payment_id' => $payment->id,
            'type_id' => 1
        ])
            ->assertRedirect(route('dashboard'))
            ->assertSessionDoesntHaveErrors()
            ->assertSessionHas('flash_notification', [
                'message' => 'The order can not be confirmed, it must have an active location assigned to it.',
                'level' => 'error',
            ]);
    }

    #[Test]
    public function it_does_not_confirm_an_order_with_excluded_products(): void
    {
        $payment = Payment::factory()->create();
        $order = Order::factory()->create(['confirmed' => false]);
        $product = Product::factory()->create();
        $order->pickup->products()->attach($product->id);
        $order_item = OrderItem::factory()->create(['order_id' => $order->id, 'product_id' => $product->id]);

        $this->mock(Confirm::class, function (MockInterface $mock) {
            return $mock->shouldNotReceive('handle');
        });

        $this->actingAsAdmin()
            ->get(route('dashboard'));

        $this->post(route('admin.orders.confirm.store', compact('order')), [
            'customer_first_name' => 'first',
            'customer_last_name' => 'last',
            'customer_phone' => '************',
            'customer_email' => '<EMAIL>',
            'payment_id' => $payment->id,
            'type_id' => 1
        ])
            ->assertRedirect(route('dashboard'))
            ->assertSessionDoesntHaveErrors()
            ->assertSessionHas('flash_notification', [
                'message' => 'These products are not available at On Farm Pickup: ' . $product->title,
                'level' => 'error',
            ]);

    }

    #[Test]
    public function it_can_confirm_an_order_that_has_not_met_the_pickup_order_minimum_when_confirm_below_minimum_is_off(): void
    {
        $payment = Payment::factory()->create();
        $pickup = Pickup::factory()->create(['min_customer_orders' => 1000000]);
        $order = Order::factory()->create(['confirmed' => false, 'pickup_id' => $pickup->id]);

        $this->mock(Confirm::class, function (MockInterface $mock) {
            return $mock->shouldNotReceive('handle');
        });

        $this->actingAsAdmin()
            ->get(route('dashboard'));

        $this->post(route('admin.orders.confirm.store', compact('order')), [
            'customer_first_name' => 'first',
            'customer_last_name' => 'last',
            'customer_phone' => '************',
            'customer_email' => '<EMAIL>',
            'payment_id' => $payment->id,
            'type_id' => 1,
        ])
            ->assertRedirect(route('dashboard'))
            ->assertSessionDoesntHaveErrors()
            ->assertSessionHas('flash_notification', [
                'message' => 'The order subtotal must be at least $1,000,000.00 before it can be confirmed.',
                'level' => 'error',
            ]);
    }

    #[Test]
    public function it_can_confirm_an_order_that_has_not_met_the_pickup_order_minimum_when_confirm_below_minimum_is_on(): void
    {
        $payment = Payment::factory()->create();
        $pickup = Pickup::factory()->create(['min_customer_orders' => 1000000]);
        $order = Order::factory()->create(['confirmed' => false, 'pickup_id' => $pickup->id]);

        $this->mock(Confirm::class, function (MockInterface $mock) use ($order) {
            return $mock->shouldReceive('handle')
                ->once()
                ->with(Mockery::on(function ($arg) use ($order) {
                    return $arg instanceof Order && $arg->id === $order->id;
                }), Mockery::any())
                ->andReturn($order);
        });

        $this->actingAsAdmin()
            ->get(route('dashboard'));

        $this->post(route('admin.orders.confirm.store', compact('order')), [
            'customer_first_name' => 'first',
            'customer_last_name' => 'last',
            'customer_phone' => '************',
            'customer_email' => '<EMAIL>',
            'payment_id' => $payment->id,
            'type_id' => 1,
            'confirm_below_minimum' => 'on'
        ])
            ->assertRedirect(route('dashboard'))
            ->assertSessionDoesntHaveErrors()
            ->assertSessionHas('flash_notification', [
                'message' => 'The order has been successfully confirmed.',
                'level' => 'info',
            ]);
    }

    #[Test]
    public function it_can_confirm_an_order(): void
    {
        Event::fake([OrderWasConfirmed::class]);

        $payment = Payment::factory()->create();

        $order = Order::factory()->create(['confirmed' => false]);

        $request_params = [
            'customer_first_name' => 'first',
            'customer_last_name' => 'last',
            'customer_phone' => '************',
            'customer_email' => '<EMAIL>',
            'payment_id' => $payment->id,
            'type_id' => 1
        ];

        $this->mock(Confirm::class, function (MockInterface $mock) use ($order, $request_params) {
            return $mock->shouldReceive('handle')
                ->once()
                ->with(
                    Mockery::on(function ($arg) use ($order) {
                        return $arg instanceof Order && $arg->id === $order->id;
                    }),
                    $request_params
                )
                ->andReturn($order);
        });

        $this->actingAsAdmin()
            ->get(route('dashboard'));

        $this->post(route('admin.orders.confirm.store', compact('order')), $request_params)
            ->assertRedirect(route('dashboard'))
            ->assertSessionDoesntHaveErrors()
            ->assertSessionHas('flash_notification', [
                'message' => 'The order has been successfully confirmed.',
                'level' => 'info',
            ]);

        Event::assertNotDispatched(OrderWasConfirmed::class);
    }

    #[Test]
    public function it_fires_order_confirmed_event_when_send_notification_is_in_request(): void
    {
        Event::fake([OrderWasConfirmed::class]);

        $payment = Payment::factory()->create();

        $order = Order::factory()->create(['confirmed' => false]);

        $request_params = [
            'customer_first_name' => 'first',
            'customer_last_name' => 'last',
            'customer_phone' => '************',
            'customer_email' => '<EMAIL>',
            'payment_id' => $payment->id,
            'type_id' => 1,
            'send_confirmation_email' => 'on'
        ];

        $this->mock(Confirm::class, function (MockInterface $mock) use ($order, $request_params) {
            return $mock->shouldReceive('handle')
                ->once()
                ->with(
                    Mockery::on(function ($arg) use ($order) {
                        return $arg instanceof Order && $arg->id === $order->id;
                    }),
                    $request_params
                )
                ->andReturn($order);
        });

        $this->actingAsAdmin()
            ->get(route('dashboard'));

        $this->post(route('admin.orders.confirm.store', compact('order')), $request_params)
            ->assertRedirect(route('dashboard'))
            ->assertSessionDoesntHaveErrors()
            ->assertSessionHas('flash_notification', [
                'message' => 'The order has been successfully confirmed.',
                'level' => 'info',
            ]);

        Event::assertDispatched(OrderWasConfirmed::class, function (OrderWasConfirmed $event) use ($order) {
            return $event->order->id === $order->id;
        });
    }

    #[Test]
    public function it_updates_customer_when_save_for_later_is_in_request(): void
    {
        Event::fake([OrderWasConfirmed::class]);

        $payment = Payment::factory()->create();

        $order = Order::factory()->create(['confirmed' => false]);

        $request_params = [
            'customer_first_name' => 'first',
            'customer_last_name' => 'last',
            'customer_phone' => '************',
            'customer_email' => '<EMAIL>',
            'payment_id' => $payment->id,
            'type_id' => 1,
            'save_for_later' => 'on'
        ];

        $this->mock(Confirm::class, function (MockInterface $mock) use ($order, $request_params) {
            return $mock->shouldReceive('handle')
                ->once()
                ->with(
                    Mockery::on(function ($arg) use ($order) {
                        return $arg instanceof Order && $arg->id === $order->id;
                    }),
                    $request_params
                )
                ->andReturn($order);
        });

        $customer = $order->customer;

        $this->actingAsAdmin()
            ->get(route('dashboard'));

        $this->post(route('admin.orders.confirm.store', compact('order')), $request_params)
            ->assertRedirect(route('dashboard'))
            ->assertSessionDoesntHaveErrors()
            ->assertSessionHas('flash_notification', [
                'message' => 'The order has been successfully confirmed.',
                'level' => 'info',
            ]);

        $this->assertDatabaseHas(User::class, [
            'id' => $order->customer_id,
            'first_name' => 'first',
            'last_name' => 'last',
            'phone' => '************',
            'street' => $customer->street,
            'street_2' => $customer->street_2,
            'city' => $customer->city,
            'state' => $customer->state,
            'zip' => $customer->zip,
            'country' => $customer->country,
        ]);
    }

    #[Test]
    public function it_updates_optional_customer_attributes_when_save_for_later_is_in_request(): void
    {
        Event::fake([OrderWasConfirmed::class]);

        $payment = Payment::factory()->create();

        $order = Order::factory()->create(['confirmed' => false]);

        $request_params = [
            'customer_first_name' => 'first',
            'customer_last_name' => 'last',
            'customer_phone' => '************',
            'customer_email' => '<EMAIL>',
            'shipping_street' => '123 Fake St',
            'shipping_street_2' => 'Unit 1',
            'shipping_city' => 'Test city',
            'shipping_state' => 'ST',
            'shipping_zip' => '12345',
            'country' => 'CA',
            'payment_id' => $payment->id,
            'type_id' => 1,
            'save_for_later' => 'on'
        ];

        $this->mock(Confirm::class, function (MockInterface $mock) use ($order, $request_params) {
            return $mock->shouldReceive('handle')
                ->once()
                ->with(
                    Mockery::on(function ($arg) use ($order) {
                        return $arg instanceof Order && $arg->id === $order->id;
                    }),
                    $request_params
                )
                ->andReturn($order);
        });

        $this->actingAsAdmin()
            ->get(route('dashboard'));

        $this->post(route('admin.orders.confirm.store', compact('order')), $request_params)
            ->assertRedirect(route('dashboard'))
            ->assertSessionDoesntHaveErrors()
            ->assertSessionHas('flash_notification', [
                'message' => 'The order has been successfully confirmed.',
                'level' => 'info',
            ]);

        $this->assertDatabaseHas(User::class, [
            'id' => $order->customer_id,
            'first_name' => 'first',
            'last_name' => 'last',
            'phone' => '************',
            'street' => '123 Fake St',
            'street_2' => 'Unit 1',
            'city' => 'Test city',
            'state' => 'ST',
            'zip' => '12345',
            'country' => 'CA',
        ]);
    }

    #[Test]
    public function it_retains_the_original_pickup_date_and_deadline_date_from_order(): void
    {
        Event::fake([OrderWasConfirmed::class]);

        $payment = Payment::factory()->create();

        $order = Order::factory()->create(['confirmed' => false]);

        $request_params = [
            'customer_first_name' => 'first',
            'customer_last_name' => 'last',
            'customer_phone' => '************',
            'customer_email' => '<EMAIL>',
            'payment_id' => $payment->id,
            'type_id' => 1,
        ];

        $original_deadline_date = $order->deadline_date;
        $original_pickup_date = $order->pickup_date;

        $this->mock(Confirm::class, function (MockInterface $mock) use ($order, $request_params) {
            $order->deadline_date = today()->subDays(2);
            $order->pickup_date = today()->subDay();

            return $mock->shouldReceive('handle')
                ->once()
                ->with(
                    Mockery::on(function ($arg) use ($order) {
                        return $arg instanceof Order && $arg->id === $order->id;
                    }),
                    $request_params
                )
                ->andReturn($order);
        });

        $this->actingAsAdmin()
            ->get(route('dashboard'));

        $this->post(route('admin.orders.confirm.store', compact('order')), $request_params)
            ->assertRedirect(route('dashboard'))
            ->assertSessionDoesntHaveErrors()
            ->assertSessionHas('flash_notification', [
                'message' => 'The order has been successfully confirmed.',
                'level' => 'info',
            ]);

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'deadline_date' => $original_deadline_date,
            'pickup_date' => $original_pickup_date,
        ]);
    }

    #[Test]
    public function it_sets_base_order_attributes_when_confirming_the_order(): void
    {
        Carbon::setTestNow(now());

        Event::fake([OrderWasConfirmed::class]);

        $payment = Payment::factory()->create();

        $schedule = Schedule::factory()->create();
        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id, 'delivery_rate' => 1000, 'settings' => ['delivery_fee_type' => 2]]);
        $blueprint = RecurringOrder::factory()->create(['fulfillment_id' => $pickup->id, 'schedule_id' => $schedule->id]);

        $order = Order::factory()->create([
            'confirmed' => false, 'pickup_id' => $pickup->id,
            'blueprint_id' => $blueprint->id,
            'original_pickup_date' => null,
            'schedule_id' => null,
            'deadline_date' => null,
            'pickup_date' => null,
        ]);

        $request_params = [
            'customer_first_name' => 'first',
            'customer_last_name' => 'last',
            'customer_phone' => '************',
            'customer_email' => '<EMAIL>',
            'payment_id' => $payment->id,
            'type_id' => 1,
        ];

        $this->actingAsAdmin()
            ->get(route('dashboard'));

        $this->post(route('admin.orders.confirm.store', compact('order')), $request_params)
            ->assertRedirect(route('dashboard'))
            ->assertSessionDoesntHaveErrors()
            ->assertSessionHas('flash_notification', [
                'message' => 'The order has been successfully confirmed.',
                'level' => 'info',
            ]);

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'customer_first_name' => 'first',
            'customer_last_name' => 'last',
            'customer_phone' => '************',
            'customer_email' => '<EMAIL>',
            'payment_id' => $payment->id,
            'type_id' => 1,
            'original_pickup_date' => null,
            'schedule_id' => null,
            'deadline_date' => null,
            'pickup_date' => null,
            'delivery_rate' => 100000,
            'delivery_fee_type' => 2,
            'accounting_id' => $order->customer->accounting_id,
            'confirmed' => true,
            'confirmed_date' => today()->format('Y-m-d'),
            'created_year' => today()->year,
            'created_month' => today()->month,
            'created_day' => today()->day
        ]);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_sets_optional_order_attributes_when_confirming_the_order(): void
    {
        Event::fake([OrderWasConfirmed::class]);

        $payment = Payment::factory()->create();

        $pickup = Pickup::factory()->create(['schedule_id' => Schedule::factory()]);

        $customer = User::factory()->create(['email_alt' => '<EMAIL>']);
        $order = Order::factory()->create(['customer_id' => $customer->id, 'confirmed' => false, 'pickup_id' => $pickup->id]);

        $request_params = [
            'customer_first_name' => 'first',
            'customer_last_name' => 'last',
            'customer_phone' => '************',
            'customer_email' => '<EMAIL>',
            'payment_id' => $payment->id,
            'type_id' => 1,
            'shipping_street' => '123 Fake St',
            'shipping_street_2' => 'Unit 1',
            'shipping_city' => 'Test city',
            'shipping_state' => 'ST',
            'shipping_zip' => '12345',
            'country' => 'CA',
        ];

        $this->actingAsAdmin()
            ->get(route('dashboard'));

        $this->post(route('admin.orders.confirm.store', compact('order')), $request_params)
            ->assertRedirect(route('dashboard'))
            ->assertSessionDoesntHaveErrors()
            ->assertSessionHas('flash_notification', [
                'message' => 'The order has been successfully confirmed.',
                'level' => 'info',
            ]);

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'shipping_street' => '123 Fake St',
            'shipping_street_2' => 'Unit 1',
            'shipping_city' => 'Test city',
            'shipping_state' => 'ST',
            'shipping_zip' => '12345',
            'customer_email_alt' => '<EMAIL>'
        ]);
    }

    #[Test]
    public function it_applies_pickup_fees_when_confirming_an_order(): void
    {
        Event::fake([OrderWasConfirmed::class]);

        $payment = Payment::factory()->create();

        $pickup = Pickup::factory()->create(['schedule_id' => Schedule::factory()]);

        $fees = PickupFee::factory()->count(2)->create(['pickup_id' => $pickup->id]);
        $order = Order::factory()->create(['confirmed' => false, 'pickup_id' => $pickup->id]);

        $this->actingAsAdmin()
            ->get(route('dashboard'));

        $this->post(route('admin.orders.confirm.store', compact('order')), [
            'customer_first_name' => 'first',
            'customer_last_name' => 'last',
            'customer_phone' => '************',
            'customer_email' => '<EMAIL>',
            'payment_id' => $payment->id,
            'type_id' => 1,
        ])
            ->assertRedirect(route('dashboard'))
            ->assertSessionDoesntHaveErrors();

        $fees->each(function (PickupFee $fee) use ($order) {
            $this->assertDatabaseHas(OrderFee::class, [
                'order_id' => $order->id,
                'title' => $fee->title,
                'qty' => 1,
                'amount' => $fee->amount,
                'taxable' => $fee->taxable,
                'apply_limit' => $fee->apply_limit,
                'threshold' => $fee->threshold,
                'cap' => $fee->cap,
                'subtotal' => $fee->amount,
            ]);
        });
    }

    #[Test]
    public function it_does_not_apply_pickup_fees_when_confirming_an_order_for_a_fee_exempt_customer(): void
    {
        Event::fake([OrderWasConfirmed::class]);

        $payment = Payment::factory()->create();

        $pickup = Pickup::factory()->create(['schedule_id' => Schedule::factory()]);
        $fees = PickupFee::factory()->count(2)->create(['pickup_id' => $pickup->id]);

        $customer = User::factory()->create(['email_alt' => '<EMAIL>', 'exempt_from_fees' => true]);
        $order = Order::factory()->create(['customer_id' => $customer->id, 'confirmed' => false, 'pickup_id' => $pickup->id]);

        $this->actingAsAdmin()
            ->get(route('dashboard'));

        $this->post(route('admin.orders.confirm.store', compact('order')), [
            'customer_first_name' => 'first',
            'customer_last_name' => 'last',
            'customer_phone' => '************',
            'customer_email' => '<EMAIL>',
            'payment_id' => $payment->id,
            'type_id' => 1,
        ])
            ->assertRedirect(route('dashboard'))
            ->assertSessionDoesntHaveErrors();

        $fees->each(function (PickupFee $fee) use ($order) {
            $this->assertDatabaseMissing(OrderFee::class, [
                'order_id' => $order->id,
                'title' => $fee->title,
                'qty' => 1,
                'amount' => $fee->amount,
                'taxable' => $fee->taxable,
                'apply_limit' => $fee->apply_limit,
                'threshold' => $fee->threshold,
                'cap' => $fee->cap,
                'subtotal' => $fee->amount,
            ]);
        });
    }

    #[Test]
    public function it_removes_existing_fees_when_confirming_an_order_for_a_fee_exempt_customer(): void
    {
        Event::fake([OrderWasConfirmed::class]);

        $payment = Payment::factory()->create();

        $pickup = Pickup::factory()->create(['schedule_id' => Schedule::factory()]);
        $fees = PickupFee::factory()->count(2)->create(['pickup_id' => $pickup->id]);

        $order = Order::factory()->create(['confirmed' => false, 'pickup_id' => $pickup->id]);
        $order_fee = OrderFee::factory()->create(['order_id' => $order->id]);

        $this->actingAsAdmin()
            ->get(route('dashboard'));

        $this->post(route('admin.orders.confirm.store', compact('order')), [
            'customer_first_name' => 'first',
            'customer_last_name' => 'last',
            'customer_phone' => '************',
            'customer_email' => '<EMAIL>',
            'payment_id' => $payment->id,
            'type_id' => 1,
        ])
            ->assertRedirect(route('dashboard'))
            ->assertSessionDoesntHaveErrors();

        $this->assertDatabaseMissing(OrderFee::class, [
            'id' => $order_fee->id,
        ]);
    }

    #[Test]
    public function it_returns_error_if_individual_product_doesnt_have_availability(): void
    {
        Event::fake([OrderWasConfirmed::class]);

        $payment = Payment::factory()->create();
        $pickup = Pickup::factory()->create(['schedule_id' => Schedule::factory()]);
        $order = Order::factory()->create(['confirmed' => false, 'pickup_id' => $pickup->id]);
        $product = Product::factory()->create(['track_inventory' => 'yes', 'inventory' => 1]);
        OrderItem::factory()->create(['order_id' => $order->id, 'product_id' => $product->id, 'qty' => 2]);

        $this->actingAsAdmin()
            ->get(route('dashboard'));

        $this->post(route('admin.orders.confirm.store', compact('order')), [
            'customer_first_name' => 'first',
            'customer_last_name' => 'last',
            'customer_phone' => '************',
            'customer_email' => '<EMAIL>',
            'payment_id' => $payment->id,
            'type_id' => 1,
        ])
            ->assertRedirect(route('dashboard'))
            ->assertSessionHas('flash_notification', [
                'message' => "There is not enough {$product['title']} left in stock.",
                'level' => 'error'
            ]);
    }

    #[Test]
    public function it_returns_an_error_if_bundle_product_doesnt_have_availability(): void
    {
        Event::fake([OrderWasConfirmed::class]);

        $payment = Payment::factory()->create();
        $pickup = Pickup::factory()->create(['schedule_id' => Schedule::factory()]);
        $order = Order::factory()->create(['confirmed' => false, 'pickup_id' => $pickup->id]);
        $product_one = Product::factory()->create(['track_inventory' => 'yes', 'inventory' => 3]);
        $product_two = Product::factory()->create(['track_inventory' => 'yes', 'inventory' => 4]);
        $bundle_product = Product::factory()->create(['is_bundle' => true, 'track_inventory' => 'bundle']);
        $bundle_product->bundle()->attach([
            $product_one->id => ['qty' => 2],
            $product_two->id => ['qty' => 2],
        ]);
        $item = OrderItem::factory()->create(['order_id' => $order->id, 'product_id' => $bundle_product->id, 'qty' => 2]);

        $this->actingAsAdmin()
            ->get(route('dashboard'));

        $this->post(route('admin.orders.confirm.store', compact('order')), [
            'customer_first_name' => 'first',
            'customer_last_name' => 'last',
            'customer_phone' => '************',
            'customer_email' => '<EMAIL>',
            'payment_id' => $payment->id,
            'type_id' => 1,
        ])
            ->assertRedirect(route('dashboard'))
            ->assertSessionHas('flash_notification', [
                'message' => "There is not enough {$bundle_product['title']} left in stock.",
                'level' => 'error'
            ]);
    }

    #[Test]
    public function it_deducts_inventory_when_confirming_an_order(): void
    {
        Event::fake([OrderWasConfirmed::class]);

        $payment = Payment::factory()->create();
        $pickup = Pickup::factory()->create(['schedule_id' => Schedule::factory()]);
        $order = Order::factory()->create(['confirmed' => false, 'pickup_id' => $pickup->id]);
        $product = Product::factory()->create(['track_inventory' => 'yes', 'inventory' => 10]);
        OrderItem::factory()->create(['order_id' => $order->id, 'product_id' => $product->id, 'qty' => 2]);

        $this->actingAsAdmin()
            ->get(route('dashboard'));

        $this->post(route('admin.orders.confirm.store', compact('order')), [
            'customer_first_name' => 'first',
            'customer_last_name' => 'last',
            'customer_phone' => '************',
            'customer_email' => '<EMAIL>',
            'payment_id' => $payment->id,
            'type_id' => 1,
        ])
            ->assertRedirect(route('dashboard'))
            ->assertSessionDoesntHaveErrors();

        $this->assertDatabaseHas(Product::class, [
            'id' => $product->id,
            'inventory' => 8,
        ]);
    }

    #[Test]
    public function it_updates_the_customer_when_confirming_an_order(): void
    {
        Carbon::setTestNow(now());

        Event::fake([OrderWasConfirmed::class]);

        $payment = Payment::factory()->create();
        $user = User::factory()->create(['order_count' => 1]);
        $order = Order::factory()->create(['confirmed' => false, 'customer_id' => $user->id]);

        $this->actingAsAdmin()
            ->get(route('dashboard'));

        $this->post(route('admin.orders.confirm.store', compact('order')), [
            'customer_first_name' => 'first',
            'customer_last_name' => 'last',
            'customer_phone' => '************',
            'customer_email' => '<EMAIL>',
            'payment_id' => $payment->id,
            'type_id' => 1,
        ])
            ->assertRedirect(route('dashboard'))
            ->assertSessionDoesntHaveErrors();

        $this->assertDatabaseHas(User::class, [
            'id' => $user->id,
            'order_count' => 2,
            'last_purchase' => now()
        ]);

        Carbon::setTestNow();

    }

    #[Test]
    public function it_updates_order_totals_when_confirming_an_order(): void
    {
        Event::fake([OrderWasConfirmed::class]);

        $payment = Payment::factory()->create();
        $user = User::factory()->create(['order_count' => 1]);
        $order = Order::factory()->create(['confirmed' => false, 'customer_id' => $user->id, 'subtotal' => 0]);
        OrderItem::factory()->create(['order_id' => $order->id, 'qty' => 2]);

        $this->actingAsAdmin()
            ->get(route('dashboard'));

        $this->post(route('admin.orders.confirm.store', compact('order')), [
            'customer_first_name' => 'first',
            'customer_last_name' => 'last',
            'customer_phone' => '************',
            'customer_email' => '<EMAIL>',
            'payment_id' => $payment->id,
            'type_id' => 1,
        ])
            ->assertRedirect(route('dashboard'))
            ->assertSessionDoesntHaveErrors();

        $this->assertDatabaseMissing(Order::class, [
            'id' => $order->id,
            'subtotal' => 0,
        ]);
    }
}
