<?php

namespace Feature\Reports;

use App\Exports\AggregateSaleByOrderExport;
use App\Models\BundleProduct;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\PackingGroup;
use App\Models\Pickup;
use App\Models\Product;
use App\Models\RecurringOrder;
use App\Models\RecurringOrderItem;
use App\Models\Schedule;
use App\Models\User;
use App\Support\Enums\OrderStatus;
use Carbon\Carbon;
use Maatwebsite\Excel\Facades\Excel;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class HarvestReportAggregateSaleByOrderExportTest extends TenantTestCase
{
    private function createTestDataForOrder(array $orderAttributes = []): array
    {
        $packingGroup = PackingGroup::factory()->create();
        $schedule = Schedule::factory()->create();
        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);
        $customer = User::factory()->create();
        $product = Product::factory()->create(['inventory_type' => $packingGroup->id]);

        $order = Order::factory()->create(array_merge([
            'customer_id' => $customer->id,
            'pickup_id' => $pickup->id,
            'schedule_id' => $schedule->id,
            'confirmed' => true,
            'canceled' => false,
        ], $orderAttributes));

        $orderItem = OrderItem::factory()->create([
            'order_id' => $order->id,
            'product_id' => $product->id
        ]);

        return compact('packingGroup', 'schedule', 'pickup', 'customer', 'product', 'order', 'orderItem');
    }
    #[Test]
    public function a_subscribed_admin_can_export_the_aggregate_sale_by_order_report_from_harvest_report(): void
    {
        $this->actingAsAdmin()
            ->get(route('admin.reports.harvest', ['asbo-export' => true]))
            ->assertOk()
            ->assertHeader('content-type', 'text/plain; charset=UTF-8')
            ->assertHeader('content-disposition', 'attachment; filename=aggregate_sale_by_order_report.csv');
    }

    #[Test]
    public function the_aggregate_sale_by_order_export_can_be_filtered_by_date_range(): void
    {
        $this->actingAsAdmin()
            ->get(route('admin.reports.harvest', [
                'asbo-export' => true,
                'confirmed_date' => [
                    'start' => '2025-01-01',
                    'end' => '2025-01-31'
                ]
            ]))
            ->assertOk()
            ->assertHeader('content-type', 'text/plain; charset=UTF-8');
    }

    #[Test]
    public function the_regular_harvest_export_still_works(): void
    {
        $this->actingAsAdmin()
            ->get(route('admin.reports.harvest', ['export' => true]))
            ->assertOk()
            ->assertHeader('content-type', 'text/csv; charset=UTF-8')
            ->assertHeader('content-disposition', 'attachment; filename="bundle_sales_report.csv"');
    }

    #[Test]
    public function the_export_contains_billable_order_items_with_correct_data(): void
    {
        Excel::fake();

        $packingGroup = PackingGroup::factory()->create(['title' => 'Test Packing Group']);
        $schedule = Schedule::factory()->create(['title' => 'Test Schedule']);
        $pickup = Pickup::factory()->create([
            'title' => 'Test Pickup Location',
            'schedule_id' => $schedule->id
        ]);
        $customer = User::factory()->create([
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'phone' => '555-1234'
        ]);
        $product = Product::factory()->create([
            'title' => 'Test Product',
            'sku' => 'TEST-SKU-001',
            'barcode' => 'TEST-BARCODE-001',
            'unit_of_issue' => 'package',
            'inventory_type' => $packingGroup->id,
            'accounting_class' => 'TEST-CLASS',
            'custom_sort' => 100,
            'is_bundle' => false
        ]);

        $order = Order::factory()->create([
            'customer_id' => $customer->id,
            'pickup_id' => $pickup->id,
            'schedule_id' => $schedule->id,
            'confirmed' => true,
            'canceled' => false,
            'status_id' => OrderStatus::confirmed(),
            'confirmed_date' => now(),
            'pickup_date' => now()->addDays(3),
            'payment_date' => now(),
            'customer_first_name' => $customer->first_name,
            'customer_last_name' => $customer->last_name,
            'customer_email' => $customer->email,
            'customer_phone' => $customer->phone,
            'blueprint_id' => null // One-time order
        ]);

        $orderItem = OrderItem::factory()->create([
            'order_id' => $order->id,
            'product_id' => $product->id,
            'title' => $product->title,
            'unit_price' => 1500,
            'store_price' => 1800,
            'qty' => 2,
            'fulfilled_qty' => 2,
            'weight' => 1.5,
            'subtotal' => 3000
        ]);

        $this->actingAsAdmin()
            ->get(route('admin.reports.harvest', ['asbo-export' => true]));

        Excel::assertDownloaded('aggregate_sale_by_order_report.csv',
            function (AggregateSaleByOrderExport $export) use ($order, $orderItem, $product, $pickup, $schedule, $customer) {
            $collection = $export->collection();

            // Should contain at least one billable item
            $billableItem = $collection->firstWhere('billable', 'Y');
            $this->assertNotNull($billableItem, 'Export should contain billable items');

            // Verify data
            $this->assertEquals($order->id, $billableItem->order_id);
            $this->assertEquals('One Time', $billableItem->order_type);
            $this->assertEquals($orderItem->id, $billableItem->order_item_id);
            $this->assertEquals('Standard', $billableItem->product_type);
            $this->assertEquals('Y', $billableItem->billable);
            $this->assertEquals($product->barcode, $billableItem->product_barcode);
            $this->assertEquals($product->id, $billableItem->product_id);
            $this->assertEquals($product->sku, $billableItem->sku);
            $this->assertEquals($product->title, $billableItem->title);
            $this->assertEquals($product->unit_of_issue, $billableItem->unit_of_issue);
            $this->assertEquals(2, $billableItem->quantity);
            $this->assertEquals($pickup->title, $billableItem->location_name);
            $this->assertEquals($pickup->id, $billableItem->location_id);
            $this->assertEquals($schedule->title, $billableItem->schedule_name);
            $this->assertEquals($schedule->id, $billableItem->schedule_id);
            $this->assertEquals($customer->id, $billableItem->customer_id);
            $this->assertEquals($customer->first_name, $billableItem->customer_first_name);
            $this->assertEquals($customer->last_name, $billableItem->customer_last_name);
            $this->assertEquals($customer->email, $billableItem->customer_email);
            $this->assertEquals($customer->phone, $billableItem->customer_phone);

            return true;
        });
    }

    #[Test]
    public function the_export_contains_non_billable_bundle_items_with_correct_data(): void
    {
        Excel::fake();

        $packingGroup = PackingGroup::factory()->create(['title' => 'Bundle Packing Group']);
        $schedule = Schedule::factory()->create(['title' => 'Bundle Schedule']);
        $pickup = Pickup::factory()->create([
            'title' => 'Bundle Pickup Location',
            'schedule_id' => $schedule->id
        ]);
        $customer = User::factory()->create([
            'first_name' => 'Jane',
            'last_name' => 'Smith',
            'email' => '<EMAIL>'
        ]);

        // Create bundle product
        $bundleProduct = Product::factory()->create([
            'title' => 'Test Bundle',
            'sku' => 'BUNDLE-001',
            'barcode' => 'BUNDLE-BARCODE-001',
            'is_bundle' => true,
            'inventory_type' => $packingGroup->id
        ]);

        // Create individual product that's part of the bundle
        $individualProduct = Product::factory()->create([
            'title' => 'Individual Product',
            'sku' => 'INDIVIDUAL-001',
            'barcode' => 'INDIVIDUAL-BARCODE-001',
            'unit_of_issue' => 'each',
            'inventory_type' => $packingGroup->id,
            'accounting_class' => 'INDIVIDUAL-CLASS',
            'custom_sort' => 200,
            'is_bundle' => false
        ]);

        // Create bundle relationship
        BundleProduct::factory()->create([
            'bundle_id' => $bundleProduct->id,
            'product_id' => $individualProduct->id,
            'qty' => 3
        ]);

        $recurringOrder = RecurringOrder::factory()->create([
            'customer_id' => $customer->id,
            'fulfillment_id' => $pickup->id,
            'schedule_id' => $schedule->id
        ]);

        $order = Order::factory()->create([
            'customer_id' => $customer->id,
            'pickup_id' => $pickup->id,
            'schedule_id' => $schedule->id,
            'confirmed' => true,
            'canceled' => false,
            'status_id' => OrderStatus::confirmed(),
            'blueprint_id' => $recurringOrder->id
        ]);

        $bundleOrderItem = OrderItem::factory()->create([
            'order_id' => $order->id,
            'product_id' => $bundleProduct->id,
            'title' => $bundleProduct->title,
            'qty' => 1,
            'fulfilled_qty' => 1
        ]);

        $this->actingAsAdmin()
            ->get(route('admin.reports.harvest', ['asbo-export' => true]));

        Excel::assertDownloaded('aggregate_sale_by_order_report.csv', function (AggregateSaleByOrderExport $export) use ($order, $bundleOrderItem, $bundleProduct, $individualProduct) {
            $collection = $export->collection();

            // Should contain billable bundle item
            $billableItem = $collection->firstWhere(function ($item) use ($bundleOrderItem) {
                return $item->order_item_id == $bundleOrderItem->id && $item->billable === 'Y';
            });
            $this->assertNotNull($billableItem, 'Export should contain billable bundle item');
            $this->assertEquals('Bundle', $billableItem->product_type);
            $this->assertEquals('Subscription', $billableItem->order_type);

            // Should contain non-billable individual items from the bundle
            $nonBillableItem = $collection->firstWhere(function ($item) use ($bundleOrderItem, $individualProduct) {
                return $item->order_item_id == $bundleOrderItem->id
                    && $item->billable === 'N'
                    && $item->product_id == $individualProduct->id;
            });
            $this->assertNotNull($nonBillableItem, 'Export should contain non-billable bundle component items');

            // Verify non-billable item data
            $this->assertEquals('N', $nonBillableItem->billable);
            $this->assertEquals($individualProduct->barcode, $nonBillableItem->product_barcode);
            $this->assertEquals($individualProduct->id, $nonBillableItem->product_id);
            $this->assertEquals($individualProduct->sku, $nonBillableItem->sku);
            $this->assertEquals($individualProduct->title, $nonBillableItem->title);
            $this->assertEquals(3, $nonBillableItem->quantity); // Bundle qty (1) * individual qty (3)

            return true;
        });
    }

    #[Test]
    public function the_export_can_be_filtered_by_order_status(): void
    {
        Excel::fake();

        // Create orders with different statuses
        $confirmedData = $this->createTestDataForOrder(['status_id' => OrderStatus::confirmed()]);
        $packedData = $this->createTestDataForOrder(['status_id' => OrderStatus::packed()]);
        $canceledData = $this->createTestDataForOrder(['status_id' => OrderStatus::canceled()]);

        $this->actingAsAdmin()
            ->get(route('admin.reports.harvest', [
                'asbo-export' => true,
                'order_status' => [OrderStatus::confirmed(), OrderStatus::packed()]
            ]));

        Excel::assertDownloaded('aggregate_sale_by_order_report.csv', function (AggregateSaleByOrderExport $export) use ($confirmedData, $packedData, $canceledData) {
            $collection = $export->collection();

            // Should contain confirmed and packed orders
            $this->assertTrue($collection->contains('order_id', $confirmedData['order']->id));
            $this->assertTrue($collection->contains('order_id', $packedData['order']->id));

            // Should not contain canceled order
            $this->assertFalse($collection->contains('order_id', $canceledData['order']->id));

            return true;
        });
    }

    #[Test]
    public function the_export_can_be_filtered_by_confirmed_date_range(): void
    {
        Excel::fake();
        Carbon::setTestNow('2024-01-15 12:00:00');

        // Create orders with different confirmed dates
        $dataInRange = $this->createTestDataForOrder(['confirmed_date' => '2024-01-10']);
        $dataOutOfRange = $this->createTestDataForOrder(['confirmed_date' => '2024-01-20']);

        $this->actingAsAdmin()
            ->get(route('admin.reports.harvest', [
                'asbo-export' => true,
                'confirmed_date' => [
                    'start' => '2024-01-01',
                    'end' => '2024-01-15'
                ]
            ]));

        Excel::assertDownloaded('aggregate_sale_by_order_report.csv', function (AggregateSaleByOrderExport $export) use ($dataInRange, $dataOutOfRange) {
            $collection = $export->collection();

            // Should contain order within date range
            $this->assertTrue($collection->contains('order_id', $dataInRange['order']->id));

            // Should not contain order outside date range
            $this->assertFalse($collection->contains('order_id', $dataOutOfRange['order']->id));

            return true;
        });

        Carbon::setTestNow();
    }

    #[Test]
    public function the_export_can_be_filtered_by_pickup_date_range(): void
    {
        Excel::fake();
        Carbon::setTestNow('2024-02-15 12:00:00');

        // Create orders with different pickup dates
        $dataInRange = $this->createTestDataForOrder(['pickup_date' => '2024-02-10']);
        $dataOutOfRange = $this->createTestDataForOrder(['pickup_date' => '2024-02-20']);

        $this->actingAsAdmin()
            ->get(route('admin.reports.harvest', [
                'asbo-export' => true,
                'pickup_date' => [
                    'start' => '2024-02-01',
                    'end' => '2024-02-15'
                ]
            ]));

        Excel::assertDownloaded('aggregate_sale_by_order_report.csv', function (AggregateSaleByOrderExport $export) use ($dataInRange, $dataOutOfRange) {
            $collection = $export->collection();

            // Should contain order within pickup date range
            $this->assertTrue($collection->contains('order_id', $dataInRange['order']->id));

            // Should not contain order outside pickup date range
            $this->assertFalse($collection->contains('order_id', $dataOutOfRange['order']->id));

            return true;
        });

        Carbon::setTestNow();
    }

    #[Test]
    public function the_export_can_be_filtered_by_payment_date_range(): void
    {
        Excel::fake();
        Carbon::setTestNow('2024-03-15 12:00:00');

        // Create orders with different payment dates
        $dataInRange = $this->createTestDataForOrder(['payment_date' => '2024-03-10']);
        $dataOutOfRange = $this->createTestDataForOrder(['payment_date' => '2024-03-20']);

        $this->actingAsAdmin()
            ->get(route('admin.reports.harvest', [
                'asbo-export' => true,
                'payment_date' => [
                    'start' => '2024-03-01',
                    'end' => '2024-03-15'
                ]
            ]));

        Excel::assertDownloaded('aggregate_sale_by_order_report.csv', function (AggregateSaleByOrderExport $export) use ($dataInRange, $dataOutOfRange) {
            $collection = $export->collection();

            // Should contain order within payment date range
            $this->assertTrue($collection->contains('order_id', $dataInRange['order']->id));

            // Should not contain order outside payment date range
            $this->assertFalse($collection->contains('order_id', $dataOutOfRange['order']->id));

            return true;
        });

        Carbon::setTestNow();
    }

    #[Test]
    public function the_export_can_be_filtered_by_order_type(): void
    {
        Excel::fake();

        // Create one-time order
        $oneTimeData = $this->createTestDataForOrder(['type_id' => 1]);

        // Create farm store order
        $farmStoreData = $this->createTestDataForOrder(['type_id' => 4]);

        $this->actingAsAdmin()
            ->get(route('admin.reports.harvest', [
                'asbo-export' => true,
                'order_type_id' => [1] // Only one-time orders
            ]));

        Excel::assertDownloaded('aggregate_sale_by_order_report.csv', function (AggregateSaleByOrderExport $export) use ($oneTimeData, $farmStoreData) {
            $collection = $export->collection();

            // Should contain one-time order
            $this->assertTrue($collection->contains('order_id', $oneTimeData['order']->id));

            // Should not contain farm store order
            $this->assertFalse($collection->contains('order_id', $farmStoreData['order']->id));

            return true;
        });
    }

    #[Test]
    public function the_export_can_be_filtered_by_pickup_location(): void
    {
        Excel::fake();

        // Create different pickup locations
        $pickup1 = Pickup::factory()->create(['title' => 'Location 1']);
        $pickup2 = Pickup::factory()->create(['title' => 'Location 2']);

        $dataAtPickup1 = $this->createTestDataForOrder(['pickup_id' => $pickup1->id]);
        $dataAtPickup2 = $this->createTestDataForOrder(['pickup_id' => $pickup2->id]);

        $this->actingAsAdmin()
            ->get(route('admin.reports.harvest', [
                'asbo-export' => true,
                'pickup_id' => [$pickup1->id]
            ]));

        Excel::assertDownloaded('aggregate_sale_by_order_report.csv', function (AggregateSaleByOrderExport $export) use ($dataAtPickup1, $dataAtPickup2) {
            $collection = $export->collection();

            // Should contain order from pickup 1
            $this->assertTrue($collection->contains('order_id', $dataAtPickup1['order']->id));

            // Should not contain order from pickup 2
            $this->assertFalse($collection->contains('order_id', $dataAtPickup2['order']->id));

            return true;
        });
    }

    #[Test]
    public function the_export_can_be_filtered_by_schedule(): void
    {
        Excel::fake();

        // Create different schedules
        $schedule1 = Schedule::factory()->create(['title' => 'Schedule 1']);
        $schedule2 = Schedule::factory()->create(['title' => 'Schedule 2']);

        $orderOnSchedule1 = Order::factory()->create([
            'confirmed' => true,
            'canceled' => false,
            'schedule_id' => $schedule1->id
        ]);
        OrderItem::factory()->create(['order_id' => $orderOnSchedule1->id]);

        $orderOnSchedule2 = Order::factory()->create([
            'confirmed' => true,
            'canceled' => false,
            'schedule_id' => $schedule2->id
        ]);
        OrderItem::factory()->create(['order_id' => $orderOnSchedule2->id]);

        $this->actingAsAdmin()
            ->get(route('admin.reports.harvest', [
                'asbo-export' => true,
                'schedule_id' => [$schedule1->id]
            ]));

        Excel::assertDownloaded('aggregate_sale_by_order_report.csv', function (AggregateSaleByOrderExport $export) use ($orderOnSchedule1, $orderOnSchedule2) {
            $collection = $export->collection();

            // Should contain order from schedule 1
            $this->assertTrue($collection->contains('order_id', $orderOnSchedule1->id));

            // Should not contain order from schedule 2
            $this->assertFalse($collection->contains('order_id', $orderOnSchedule2->id));

            return true;
        });
    }

    #[Test]
    public function the_export_can_be_filtered_by_customer_id(): void
    {
        Excel::fake();

        $customer1 = User::factory()->create();
        $customer2 = User::factory()->create();

        $dataByCustomer1 = $this->createTestDataForOrder(['customer_id' => $customer1->id]);
        $dataByCustomer2 = $this->createTestDataForOrder(['customer_id' => $customer2->id]);

        $this->actingAsAdmin()
            ->get(route('admin.reports.harvest', [
                'asbo-export' => true,
                'customer' => $customer1->id
            ]));

        Excel::assertDownloaded('aggregate_sale_by_order_report.csv', function (AggregateSaleByOrderExport $export) use ($dataByCustomer1, $dataByCustomer2) {
            $collection = $export->collection();

            // Should contain order by customer 1
            $this->assertTrue($collection->contains('order_id', $dataByCustomer1['order']->id));

            // Should not contain order by customer 2
            $this->assertFalse($collection->contains('order_id', $dataByCustomer2['order']->id));

            return true;
        });
    }

    #[Test]
    public function the_export_can_be_filtered_by_customer_email(): void
    {
        Excel::fake();

        $customer1 = User::factory()->create(['email' => '<EMAIL>']);
        $customer2 = User::factory()->create(['email' => '<EMAIL>']);

        $dataByCustomer1 = $this->createTestDataForOrder([
            'customer_id' => $customer1->id,
            'customer_email' => $customer1->email
        ]);
        $dataByCustomer2 = $this->createTestDataForOrder([
            'customer_id' => $customer2->id,
            'customer_email' => $customer2->email
        ]);

        $this->actingAsAdmin()
            ->get(route('admin.reports.harvest', [
                'asbo-export' => true,
                'customer' => '<EMAIL>'
            ]));

        Excel::assertDownloaded('aggregate_sale_by_order_report.csv', function (AggregateSaleByOrderExport $export) use ($dataByCustomer1, $dataByCustomer2) {
            $collection = $export->collection();

            // Should contain order by customer 1
            $this->assertTrue($collection->contains('order_id', $dataByCustomer1['order']->id));

            // Should not contain order by customer 2
            $this->assertFalse($collection->contains('order_id', $dataByCustomer2['order']->id));

            return true;
        });
    }

    #[Test]
    public function the_export_can_be_filtered_by_customer_name(): void
    {
        Excel::fake();

        $customer1 = User::factory()->create([
            'first_name' => 'John',
            'last_name' => 'Doe'
        ]);
        $customer2 = User::factory()->create([
            'first_name' => 'Jane',
            'last_name' => 'Smith'
        ]);

        $dataByCustomer1 = $this->createTestDataForOrder([
            'customer_id' => $customer1->id,
            'customer_first_name' => $customer1->first_name,
            'customer_last_name' => $customer1->last_name
        ]);
        $dataByCustomer2 = $this->createTestDataForOrder([
            'customer_id' => $customer2->id,
            'customer_first_name' => $customer2->first_name,
            'customer_last_name' => $customer2->last_name
        ]);

        $this->actingAsAdmin()
            ->get(route('admin.reports.harvest', [
                'asbo-export' => true,
                'customer' => 'John Doe'
            ]));

        Excel::assertDownloaded('aggregate_sale_by_order_report.csv', function (AggregateSaleByOrderExport $export) use ($dataByCustomer1, $dataByCustomer2) {
            $collection = $export->collection();

            // Should contain order by John Doe
            $this->assertTrue($collection->contains('order_id', $dataByCustomer1['order']->id));

            // Should not contain order by Jane Smith
            $this->assertFalse($collection->contains('order_id', $dataByCustomer2['order']->id));

            return true;
        });
    }

    #[Test]
    public function the_export_excludes_unconfirmed_and_canceled_orders(): void
    {
        Excel::fake();

        // Create confirmed order (should be included)
        $confirmedData = $this->createTestDataForOrder([
            'confirmed' => true,
            'canceled' => false
        ]);

        // Create unconfirmed order (should be excluded)
        $unconfirmedData = $this->createTestDataForOrder([
            'confirmed' => false,
            'canceled' => false
        ]);

        // Create canceled order (should be excluded)
        $canceledData = $this->createTestDataForOrder([
            'confirmed' => true,
            'canceled' => true
        ]);

        $this->actingAsAdmin()
            ->get(route('admin.reports.harvest', ['asbo-export' => true]));

        Excel::assertDownloaded('aggregate_sale_by_order_report.csv', function (AggregateSaleByOrderExport $export) use ($confirmedData, $unconfirmedData, $canceledData) {
            $collection = $export->collection();

            // Should contain confirmed order
            $this->assertTrue($collection->contains('order_id', $confirmedData['order']->id));

            // Should not contain unconfirmed order
            $this->assertFalse($collection->contains('order_id', $unconfirmedData['order']->id));

            // Should not contain canceled order
            $this->assertFalse($collection->contains('order_id', $canceledData['order']->id));

            return true;
        });
    }

    #[Test]
    public function the_export_contains_correct_headings(): void
    {
        Excel::fake();

        $this->actingAsAdmin()
            ->get(route('admin.reports.harvest', ['asbo-export' => true]));

        Excel::assertDownloaded('aggregate_sale_by_order_report.csv', function (AggregateSaleByOrderExport $export) {
            $headings = $export->headings();

            $expectedHeadings = [
                'Order ID',
                'Order Type',
                'Order Item ID',
                'Product Type',
                'Billable',
                'Product Barcode',
                'Product ID',
                'SKU',
                'Title',
                'Unit of Issue',
                'Quantity',
                'Sort ID',
                'Packing Group',
                'Accounting Class ID',
                'Pounds per Unit',
                'Total Pounds',
                'Retail Price per Unit',
                'Total Retail Price',
                'Billed Price per Unit',
                'Total Billed Price',
                'Discount per Unit',
                'Total Discount',
                'Confirmation Date',
                'Deadline Date',
                'Pack Date',
                'Payment Date',
                'Delivery Date',
                'Location Name',
                'Location ID',
                'Schedule Name',
                'Schedule ID',
                'Customer ID',
                'Customer First Name',
                'Customer Last Name',
                'Customer Phone',
                'Customer Email',
                'Shipping Street',
                'Shipping Street 2',
                'Shipping City',
                'Shipping State',
                'Shipping Zip',
                'Shipping Country',
                'Customer Order Count',
                'Profile Notes',
                'Customer Notes',
                'Private Notes',
                'Invoice Notes',
                'Payment Notes'
            ];

            $this->assertEquals($expectedHeadings, $headings);

            return true;
        });
    }

    #[Test]
    public function the_export_contains_subscription_orders_with_correct_data(): void
    {
        Excel::fake();

        // Create test data for subscription order
        $packingGroup = PackingGroup::factory()->create(['title' => 'Subscription Packing Group']);
        $schedule = Schedule::factory()->create(['title' => 'Subscription Schedule']);
        $pickup = Pickup::factory()->create([
            'title' => 'Subscription Pickup Location',
            'schedule_id' => $schedule->id
        ]);
        $customer = User::factory()->create([
            'first_name' => 'Subscription',
            'last_name' => 'Customer',
            'email' => '<EMAIL>',
            'phone' => '555-9999'
        ]);
        $product = Product::factory()->create([
            'title' => 'Subscription Product',
            'sku' => 'SUB-SKU-001',
            'barcode' => 'SUB-BARCODE-001',
            'unit_of_issue' => 'package',
            'inventory_type' => $packingGroup->id,
            'accounting_class' => 'SUB-CLASS',
            'custom_sort' => 300,
            'is_bundle' => false
        ]);

        // Create recurring order (subscription blueprint)
        $recurringOrder = RecurringOrder::factory()->create([
            'customer_id' => $customer->id,
            'fulfillment_id' => $pickup->id,
            'schedule_id' => $schedule->id,
            'reorder_frequency' => 7
        ]);

        // Create recurring order item
        RecurringOrderItem::factory()->create([
            'order_id' => $recurringOrder->id,
            'customer_id' => $customer->id,
            'product_id' => $product->id,
            'qty' => 3,
            'type' => 'recurring'
        ]);

        // Create actual order generated from the subscription
        $subscriptionOrder = Order::factory()->create([
            'customer_id' => $customer->id,
            'pickup_id' => $pickup->id,
            'schedule_id' => $schedule->id,
            'confirmed' => true,
            'canceled' => false,
            'status_id' => OrderStatus::confirmed(),
            'confirmed_date' => now(),
            'pickup_date' => now()->addDays(3),
            'payment_date' => now(),
            'customer_first_name' => $customer->first_name,
            'customer_last_name' => $customer->last_name,
            'customer_email' => $customer->email,
            'customer_phone' => $customer->phone,
            'blueprint_id' => $recurringOrder->id // This makes it a subscription order
        ]);

        $subscriptionOrderItem = OrderItem::factory()->create([
            'order_id' => $subscriptionOrder->id,
            'product_id' => $product->id,
            'title' => $product->title,
            'unit_price' => 2000, // $20.00
            'store_price' => 2200, // $22.00
            'qty' => 3,
            'fulfilled_qty' => 3,
            'weight' => 2.0,
            'subtotal' => 6000 // $60.00
        ]);

        $this->actingAsAdmin()
            ->get(route('admin.reports.harvest', ['asbo-export' => true]));

        Excel::assertDownloaded('aggregate_sale_by_order_report.csv', function (AggregateSaleByOrderExport $export) use ($subscriptionOrder, $subscriptionOrderItem, $product, $pickup, $schedule, $customer) {
            $collection = $export->collection();

            // Should contain at least one billable item from subscription
            $billableItem = $collection->firstWhere(function ($item) use ($subscriptionOrder) {
                return $item->order_id == $subscriptionOrder->id && $item->billable === 'Y';
            });
            $this->assertNotNull($billableItem, 'Export should contain billable subscription items');

            // Verify this is identified as a subscription order
            $this->assertEquals($subscriptionOrder->id, $billableItem->order_id);
            $this->assertEquals('Subscription', $billableItem->order_type); // Should show as "Subscription" because blueprint_id is set
            $this->assertEquals($subscriptionOrderItem->id, $billableItem->order_item_id);
            $this->assertEquals('Standard', $billableItem->product_type);

            // Verify product data
            $this->assertEquals('Y', $billableItem->billable);
            $this->assertEquals($product->barcode, $billableItem->product_barcode);
            $this->assertEquals($product->id, $billableItem->product_id);
            $this->assertEquals($product->sku, $billableItem->sku);
            $this->assertEquals($product->title, $billableItem->title);
            $this->assertEquals($product->unit_of_issue, $billableItem->unit_of_issue);
            $this->assertEquals(3, $billableItem->quantity);

            // Verify location and schedule data
            $this->assertEquals($pickup->title, $billableItem->location_name);
            $this->assertEquals($pickup->id, $billableItem->location_id);
            $this->assertEquals($schedule->title, $billableItem->schedule_name);
            $this->assertEquals($schedule->id, $billableItem->schedule_id);

            // Verify customer data
            $this->assertEquals($customer->id, $billableItem->customer_id);
            $this->assertEquals($customer->first_name, $billableItem->customer_first_name);
            $this->assertEquals($customer->last_name, $billableItem->customer_last_name);
            $this->assertEquals($customer->email, $billableItem->customer_email);
            $this->assertEquals($customer->phone, $billableItem->customer_phone);

            return true;
        });
    }

    #[Test]
    public function the_export_can_differentiate_between_one_time_and_subscription_orders(): void
    {
        Excel::fake();

        $customer = User::factory()->create();
        $pickup = Pickup::factory()->create();
        $schedule = Schedule::factory()->create();
        $product = Product::factory()->create();

        // Create one-time order (no blueprint_id)
        $oneTimeOrder = Order::factory()->create([
            'customer_id' => $customer->id,
            'pickup_id' => $pickup->id,
            'schedule_id' => $schedule->id,
            'confirmed' => true,
            'canceled' => false,
            'blueprint_id' => null // One-time order
        ]);
        OrderItem::factory()->create([
            'order_id' => $oneTimeOrder->id,
            'product_id' => $product->id
        ]);

        // Create subscription order (with blueprint_id)
        $recurringOrder = RecurringOrder::factory()->create([
            'customer_id' => $customer->id,
            'fulfillment_id' => $pickup->id,
            'schedule_id' => $schedule->id
        ]);
        $subscriptionOrder = Order::factory()->create([
            'customer_id' => $customer->id,
            'pickup_id' => $pickup->id,
            'schedule_id' => $schedule->id,
            'confirmed' => true,
            'canceled' => false,
            'blueprint_id' => $recurringOrder->id // Subscription order
        ]);
        OrderItem::factory()->create([
            'order_id' => $subscriptionOrder->id,
            'product_id' => $product->id
        ]);

        $this->actingAsAdmin()
            ->get(route('admin.reports.harvest', ['asbo-export' => true]));

        Excel::assertDownloaded('aggregate_sale_by_order_report.csv', function (AggregateSaleByOrderExport $export) use ($oneTimeOrder, $subscriptionOrder) {
            $collection = $export->collection();

            // Find one-time order item
            $oneTimeItem = $collection->firstWhere('order_id', $oneTimeOrder->id);
            $this->assertNotNull($oneTimeItem, 'Export should contain one-time order');
            $this->assertEquals('One Time', $oneTimeItem->order_type);

            // Find subscription order item
            $subscriptionItem = $collection->firstWhere('order_id', $subscriptionOrder->id);
            $this->assertNotNull($subscriptionItem, 'Export should contain subscription order');
            $this->assertEquals('Subscription', $subscriptionItem->order_type);

            return true;
        });
    }
}
