<?php

namespace Tests\Feature\Reports;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use App\Models\Tag;
use App\Support\Enums\OrderStatus;
use Illuminate\Support\Collection;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class HarvestReportTest extends TenantTestCase
{
    #[Test]
    public function an_guest_cannot_fetch_the_harvest_report(): void
    {
        $this->get(route('admin.reports.harvest'))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function a_non_admin_cannot_fetch_the_harvest_report(): void
    {
        $this->actingAsCustomer()
            ->get(route('admin.reports.harvest'))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function an_admin_can_fetch_the_harvest_report(): void
    {
        $this->actingAsAdmin()
            ->get(route('admin.reports.harvest'))
            ->assertOk()
            ->assertViewIs('reports.harvest.index')
            ->assertViewHas([
                'savedFilters',
                'appliedFilters',
                'appliedFilter',
                'results',
            ]);
    }

    #[Test]
    public function the_harvest_report_includes_the_expected_products(): void
    {
        $order = Order::factory()->create(['confirmed' => true]);
        $item = OrderItem::factory()->create(['order_id' => $order->id]);

        $this->actingAsAdmin()
            ->get(route('admin.reports.harvest'))
            ->assertOk()
            ->assertViewHas([
                'results' => fn(Collection $arg) => $arg->isNotEmpty()
                    && $arg->contains(fn($arg_item) => $arg_item->id === $item->product_id)
            ]);
    }

    #[Test]
    public function the_harvest_report_includes_the_expected_product_columns(): void
    {
        $order = Order::factory()->create(['confirmed' => true]);
        $product = Product::factory()->create(['title' => 'testtitle', 'barcode' => 'testbar', 'sku' => 'testsku', 'custom_sort' => 'testsort', 'unit_description' => 'testdesc', 'item_cost' => 5, 'inventory' => 123, 'stock_out_inventory' => 12]);
        OrderItem::factory()->create(['order_id' => $order->id, 'product_id' => $product->id]);

        $this->actingAsAdmin()
            ->get(route('admin.reports.harvest'))
            ->assertOk()
            ->assertViewHas([
                'results' => fn(Collection $arg) => $arg->isNotEmpty()
                    && $arg->contains(function (Product $arg_item) use ($product) {
                        return $arg_item->id === $product->id
                            && $arg_item->title === 'testtitle'
                            && $arg_item->weight > 0
                            && $arg_item->sku === 'testsku'
                            && $arg_item->sort === 'testsort'
                            && $arg_item->stock_out_inventory === 12
                            && $arg_item->barcode === 'testbar'
                            && $arg_item->product_id === $product->id;
                    })
            ]);
    }

    #[Test]
    public function the_harvest_report_does_not_include_unconfirmed_orders(): void
    {
        $order = Order::factory()->create(['confirmed' => false]);
        $item = OrderItem::factory()->create(['order_id' => $order->id]);

        $this->actingAsAdmin()
            ->get(route('admin.reports.harvest'))
            ->assertOk()
            ->assertViewHas([
                'results' => fn(Collection $arg) => $arg->doesntContain(fn($arg_item) => $arg_item->id === $item->product_id)
            ]);
    }

    #[Test]
    public function the_harvest_report_does_not_include_canceled_orders(): void
    {
        $order = Order::factory()->create(['confirmed' => true, 'canceled' => true]);
        $item = OrderItem::factory()->create(['order_id' => $order->id]);

        $this->actingAsAdmin()
            ->get(route('admin.reports.harvest'))
            ->assertOk()
            ->assertViewHas([
                'results' => fn(Collection $arg) => $arg->doesntContain(fn($arg_item) => $arg_item->id === $item->product_id)
            ]);
    }

    #[Test]
    public function the_harvest_report_can_filter_by_order_status(): void
    {
        $order_one = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'status_id' => OrderStatus::confirmed()]);
        $item_one = OrderItem::factory()->create(['order_id' => $order_one->id]);

        $order_two = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'status_id' => OrderStatus::processing()]);
        $item_two = OrderItem::factory()->create(['order_id' => $order_two->id]);

        $this->actingAsAdmin()
            ->get(route('admin.reports.harvest', [
                'order_status' => [OrderStatus::processing(), OrderStatus::packed()]
            ]))
            ->assertOk()
            ->assertViewHas([
                'results' => function (Collection $arg) use ($item_one, $item_two) {
                    return $arg->doesntContain(fn($arg_item) => $arg_item->id === $item_one->product_id)
                        && $arg->contains(fn($arg_item) => $arg_item->id === $item_two->product_id);
                }
            ]);
    }

    #[Test]
    public function the_harvest_report_can_filter_by_pickup_date(): void
    {
        $this->markTestSkipped('passing locally');
        $order_one = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'pickup_date' => today()->subDay()]);
        $item_one = OrderItem::factory()->create(['order_id' => $order_one->id]);

        $order_two = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'pickup_date' => today()->addDay()]);
        $item_two = OrderItem::factory()->create(['order_id' => $order_two->id]);

        $this->actingAsAdmin()
            ->get(route('admin.reports.harvest', [
                'pickup_date' => [
                    'start' => '',
                    'end' => '',
                ]
            ]))
            ->assertOk()
            ->assertViewHas('results', function (Collection $arg) use ($item_one, $item_two) {
                return $arg->contains(fn($arg_item) => $arg_item->id == $item_one->id)
                    && $arg->contains(fn($arg_item) => $arg_item->id == $item_two->id);
            });

        $this->get(route('admin.reports.harvest', [
            'pickup_date' => [
                'start' => today()->format('Y-m-d'),
                'end' => '',
            ]
        ]))
            ->assertOk()
            ->assertViewHas([
                'results' => function (Collection $arg) use ($item_one, $item_two) {
                    return $arg->doesntContain(fn($arg_item) => $arg_item->id === $item_one->id)
                        && $arg->contains(fn($arg_item) => $arg_item->id === $item_two->id);
                }
            ]);

        $this->get(route('admin.reports.harvest', [
            'pickup_date' => [
                'start' => '',
                'end' => today()->format('Y-m-d')
            ]
        ]))
            ->assertOk()
            ->assertViewHas([
                'results' => function (Collection $arg) use ($item_one, $item_two) {
                    return $arg->contains(fn($arg_item) => $arg_item->id === $item_one->id)
                        && $arg->doesntContain(fn($arg_item) => $arg_item->id === $item_two->id);
                }
            ]);

        $this->get(route('admin.reports.harvest', [
            'pickup_date' => [
                'start' => today()->subDays(2)->format('Y-m-d'),
                'end' => today()->addDays(2)->format('Y-m-d')
            ]
        ]))
            ->assertOk()
            ->assertViewHas([
                'results' => function (Collection $arg) use ($item_one, $item_two) {
                    return $arg->contains(fn($arg_item) => $arg_item->id === $item_one->id)
                        && $arg->contains(fn($arg_item) => $arg_item->id === $item_two->id);
                }
            ]);

        $this->get(route('admin.reports.harvest', [
            'pickup_date' => [
                'start' => today()->subDays(4)->format('Y-m-d'),
                'end' => today()->subDays(2)->format('Y-m-d')
            ]
        ]))
            ->assertOk()
            ->assertViewHas([
                'results' => function (Collection $arg) use ($item_one, $item_two) {
                    return $arg->doesntContain(fn($arg_item) => $arg_item->id === $item_one->id)
                        && $arg->doesntContain(fn($arg_item) => $arg_item->id === $item_two->id);
                }
            ]);
    }

    #[Test]
    public function the_harvest_report_can_filter_by_pack_deadline_at(): void
    {
        $this->markTestSkipped('passing locally');

        $order_one = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'pack_deadline_at' => today()->subDay()]);
        $item_one = OrderItem::factory()->create(['order_id' => $order_one->id]);

        $order_two = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'pack_deadline_at' => today()->addDay()]);
        $item_two = OrderItem::factory()->create(['order_id' => $order_two->id]);

        $this->actingAsAdmin()
            ->get(route('admin.reports.harvest', [
                'pack_deadline_at' => [
                    'start' => '',
                    'end' => '',
                ]
            ]))
            ->assertOk()
            ->assertViewHas('results', function (Collection $arg) use ($item_one, $item_two) {
                return $arg->contains(fn($arg_item) => $arg_item->id == $item_one->id)
                    && $arg->contains(fn($arg_item) => $arg_item->id == $item_two->id);
            });

        $this->get(route('admin.reports.harvest', [
            'pack_deadline_at' => [
                'start' => today()->format('Y-m-d'),
                'end' => '',
            ]
        ]))
            ->assertOk()
            ->assertViewHas([
                'results' => function (Collection $arg) use ($item_one, $item_two) {
                    return $arg->doesntContain(fn($arg_item) => $arg_item->id === $item_one->id)
                        && $arg->contains(fn($arg_item) => $arg_item->id === $item_two->id);
                }
            ]);

        $this->get(route('admin.reports.harvest', [
            'pack_deadline_at' => [
                'start' => '',
                'end' => today()->format('Y-m-d')
            ]
        ]))
            ->assertOk()
            ->assertViewHas([
                'results' => function (Collection $arg) use ($item_one, $item_two) {
                    return $arg->contains(fn($arg_item) => $arg_item->id === $item_one->id)
                        && $arg->doesntContain(fn($arg_item) => $arg_item->id === $item_two->id);
                }
            ]);

        $this->get(route('admin.reports.harvest', [
            'pack_deadline_at' => [
                'start' => today()->subDays(2)->format('Y-m-d'),
                'end' => today()->addDays(2)->format('Y-m-d')
            ]
        ]))
            ->assertOk()
            ->assertViewHas([
                'results' => function (Collection $arg) use ($item_one, $item_two) {
                    return $arg->contains(fn($arg_item) => $arg_item->id === $item_one->id)
                        && $arg->contains(fn($arg_item) => $arg_item->id === $item_two->id);
                }
            ]);

        $this->get(route('admin.reports.harvest', [
            'pack_deadline_at' => [
                'start' => today()->subDays(4)->format('Y-m-d'),
                'end' => today()->subDays(2)->format('Y-m-d')
            ]
        ]))
            ->assertOk()
            ->assertViewHas([
                'results' => function (Collection $arg) use ($item_one, $item_two) {
                    return $arg->doesntContain(fn($arg_item) => $arg_item->id === $item_one->id)
                        && $arg->doesntContain(fn($arg_item) => $arg_item->id === $item_two->id);
                }
            ]);
    }

    #[Test]
    public function the_harvest_report_can_filter_by_confirmed_date(): void
    {
        $this->markTestSkipped('passing locally');
        $order_one = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => today()->subDay()]);
        $item_one = OrderItem::factory()->create(['order_id' => $order_one->id]);

        $order_two = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => today()->addDay()]);
        $item_two = OrderItem::factory()->create(['order_id' => $order_two->id]);

        $this->actingAsAdmin()
            ->get(route('admin.reports.harvest', [
                'confirmed_date' => [
                    'start' => '',
                    'end' => '',
                ]
            ]))
            ->assertOk()
            ->assertViewHas('results', function (Collection $arg) use ($item_one, $item_two) {
                return $arg->contains(fn($arg_item) => $arg_item->id == $item_one->id)
                    && $arg->contains(fn($arg_item) => $arg_item->id == $item_two->id);
            });

        $this->get(route('admin.reports.harvest', [
            'confirmed_date' => [
                'start' => today()->format('Y-m-d'),
                'end' => '',
            ]
        ]))
            ->assertOk()
            ->assertViewHas([
                'results' => function (Collection $arg) use ($item_one, $item_two) {
                    return $arg->doesntContain(fn($arg_item) => $arg_item->id === $item_one->id)
                        && $arg->contains(fn($arg_item) => $arg_item->id === $item_two->id);
                }
            ]);

        $this->get(route('admin.reports.harvest', [
            'confirmed_date' => [
                'start' => '',
                'end' => today()->format('Y-m-d')
            ]
        ]))
            ->assertOk()
            ->assertViewHas([
                'results' => function (Collection $arg) use ($item_one, $item_two) {
                    return $arg->contains(fn($arg_item) => $arg_item->id === $item_one->id)
                        && $arg->doesntContain(fn($arg_item) => $arg_item->id === $item_two->id);
                }
            ]);

        $this->get(route('admin.reports.harvest', [
            'confirmed_date' => [
                'start' => today()->subDays(2)->format('Y-m-d'),
                'end' => today()->addDays(2)->format('Y-m-d')
            ]
        ]))
            ->assertOk()
            ->assertViewHas([
                'results' => function (Collection $arg) use ($item_one, $item_two) {
                    return $arg->contains(fn($arg_item) => $arg_item->id === $item_one->id)
                        && $arg->contains(fn($arg_item) => $arg_item->id === $item_two->id);
                }
            ]);

        $this->get(route('admin.reports.harvest', [
            'confirmed_date' => [
                'start' => today()->subDays(4)->format('Y-m-d'),
                'end' => today()->subDays(2)->format('Y-m-d')
            ]
        ]))
            ->assertOk()
            ->assertViewHas([
                'results' => function (Collection $arg) use ($item_one, $item_two) {
                    return $arg->doesntContain(fn($arg_item) => $arg_item->id === $item_one->id)
                        && $arg->doesntContain(fn($arg_item) => $arg_item->id === $item_two->id);
                }
            ]);
    }

    #[Test]
    public function the_harvest_report_can_filter_by_collection(): void
    {
        $collection_one = \App\Models\Collection::factory()->create();
        $order_one = Order::factory()->create(['confirmed' => true, 'canceled' => false]);
        $item_one = OrderItem::factory()->create(['order_id' => $order_one->id]);
        $item_one->product->collections()->attach($collection_one);

        $collection_two = \App\Models\Collection::factory()->create();
        $order_two = Order::factory()->create(['confirmed' => true, 'canceled' => false]);
        $item_two = OrderItem::factory()->create(['order_id' => $order_two->id]);
        $item_two->product->collections()->attach($collection_two);

        $this->actingAsAdmin()
            ->get(route('admin.reports.harvest', [
                'collection_id' => $collection_two->id,
            ]))
            ->assertOk()
            ->assertViewHas([
                'results' => function (Collection $arg) use ($item_one, $item_two) {
                    return $arg->doesntContain(fn($arg_item) => $arg_item->product_id === $item_one->product_id)
                        && $arg->contains(fn($arg_item) => $arg_item->product_id === $item_two->product_id);
                }
            ]);
    }

    #[Test]
    public function the_report_can_filter_by_order_tags(): void
    {
        $order_one = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'pickup_date' => today()->subDay()]);
        $item_one = OrderItem::factory()->create(['order_id' => $order_one->id]);
        $tag_one = Tag::factory()->create();
        $order_one->tags()->attach($tag_one);

        $order_two = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'pickup_date' => today()->subDay()]);
        $item_two = OrderItem::factory()->create(['order_id' => $order_two->id]);
        $tag_two = Tag::factory()->create();
        $order_two->tags()->attach($tag_two);

        $order_three = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'pickup_date' => today()->subDay()]);
        $item_three = OrderItem::factory()->create(['order_id' => $order_three->id]);

        $this->actingAsAdmin()
            ->get(route('admin.reports.harvest', [
                'order_tags' => [],
            ]))
            ->assertOk()
            ->assertViewHas([
                'results' => function (Collection $arg) use ($item_one, $item_two, $item_three) {
                    return $arg->contains(fn($arg_item) => $arg_item->product_id === $item_one->product_id)
                        && $arg->contains(fn($arg_item) => $arg_item->product_id === $item_two->product_id)
                        && $arg->contains(fn($arg_item) => $arg_item->product_id === $item_three->product_id);
                }
            ]);

        $this->actingAsAdmin()
            ->get(route('admin.reports.harvest', [
                'order_tags' => [$tag_one->id],
            ]))
            ->assertOk()
            ->assertViewHas([
                'results' => function (Collection $arg) use ($item_one, $item_two, $item_three) {
                    return $arg->contains(fn($arg_item) => $arg_item->product_id === $item_one->product_id)
                        && $arg->doesntContain(fn($arg_item) => $arg_item->product_id === $item_two->product_id)
                        && $arg->doesntContain(fn($arg_item) => $arg_item->product_id === $item_three->product_id);
                }
            ]);

        $this->actingAsAdmin()
            ->get(route('admin.reports.harvest', [
                'order_tags' => [$tag_two->id],
            ]))
            ->assertOk()
            ->assertViewHas([
                'results' => function (Collection $arg) use ($item_one, $item_two, $item_three) {
                    return $arg->doesntContain(fn($arg_item) => $arg_item->product_id === $item_one->product_id)
                        && $arg->contains(fn($arg_item) => $arg_item->product_id === $item_two->product_id)
                        && $arg->doesntContain(fn($arg_item) => $arg_item->product_id === $item_three->product_id);
                }
            ]);
    }
}
