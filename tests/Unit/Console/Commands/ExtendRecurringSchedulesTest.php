<?php

namespace Tests\Unit\Console\Commands;

use App\Jobs\ExtendRepeatingSchedule;
use App\Models\Schedule;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Bus;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class ExtendRecurringSchedulesTest extends TestCase
{
    #[Test]
    public function it_extends_expected_schedules(): void
    {
        Bus::fake([ExtendRepeatingSchedule::class]);

        $tenant_schedule = Schedule::factory()->create(['active' => true, 'type_id' => 2]);
        $non_recurring_schedule = Schedule::factory()->create(['id' => 1234, 'active' => true, 'type_id' => 1]);
        $non_active_schedule = Schedule::factory()->create(['id' => 2234, 'active' => false, 'type_id' => 1]);

        Artisan::call('schedule:extend');

        Bus::assertDispatched(
            ExtendRepeatingSchedule::class,
            fn(ExtendRepeatingSchedule $job) => $job->schedule_id === $tenant_schedule->id
        );

        Bus::assertNotDispatched(
            ExtendRepeatingSchedule::class,
            fn(ExtendRepeatingSchedule $job) => $job->schedule_id === $non_recurring_schedule->id
        );

        Bus::assertNotDispatched(
            ExtendRepeatingSchedule::class,
            fn(ExtendRepeatingSchedule $job) => $job->schedule_id === $non_active_schedule->id
        );
    }
}
