<?php

namespace Tests\Unit\Console\Commands;

use App\Jobs\RecordNewSubscriptionOrdersInDrip;
use Carbon\Carbon;
use Illuminate\Support\Facades\Bus;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class RecordNewSubscriptionOrdersInDripTest extends TestCase
{
    #[Test]
    public function it_records_subscriptions(): void
    {
        Carbon::setTestNow($now = now());

        Bus::fake([RecordNewSubscriptionOrdersInDrip::class]);

        $this->artisan("grazecart:record-subscription-orders-in-drip")
            ->assertExitCode(0);

        Bus::assertDispatchedTimes(RecordNewSubscriptionOrdersInDrip::class, 1);

        Carbon::setTestNow();
    }
}
