<?php

namespace Tests\Unit\Geocoding;

use App\Contracts\Geocoder;
use App\Models\Setting;
use App\Services\Geocoding\GeocoderCa;
use App\Services\Geocoding\Geocodio;
use App\Services\Geocoding\GoogleGeocoder;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class GeocoderTest extends TenantTestCase
{
    #[Test]
    public function it_returns_default_geocoder_when_farm_country_setting_not_canada(): void
    {
        // override base test fake
        $this->forgetMock(Geocoder::class);
        $this->forgetMock(Geocodio::class);

        Setting::updateOrCreate(['key' => 'farm_country'],['value'  => 'Other']);
        $this->assertInstanceOf(Geocodio::class, app(Geocoder::class));
    }

    #[Test]
    public function it_returns_canadian_geocoder_when_farm_country_setting_is_canada(): void
    {
        // override base test fake
        $this->forgetMock(Geocoder::class);
        $this->forgetMock(Geocodio::class);

        Setting::updateOrCreate(['key' => 'farm_country'],['value'  => 'Canada']);
        $this->assertInstanceOf(GeocoderCa::class, app(Geocoder::class));
    }

    #[Test]
    public function it_returns_google_geocoder_when_geocoding_service_setting_is_google(): void
    {
        Setting::flushCache();
        // override base test fake
        $this->forgetMock(Geocoder::class);
        $this->forgetMock(Geocodio::class);

        Setting::updateOrCreate(['key' => 'geocoder'],['value'  => 'google']);
        $this->assertInstanceOf(GoogleGeocoder::class, app(Geocoder::class));
    }

    #[Test]
    public function it_returns_default_geocoder_when_geocoding_service_setting_is_not_google_and_country_is_not_canada(): void
    {
        Setting::flushCache();
        // override base test fake
        $this->forgetMock(Geocoder::class);
        $this->forgetMock(Geocodio::class);
        
        Setting::updateOrCreate(['key' => 'farm_country'],['value'  => 'USA']);
        Setting::updateOrCreate(['key' => 'geocoder'],['value'  => '']);
        $this->assertInstanceOf(Geocodio::class, app(Geocoder::class));
        
        Setting::updateOrCreate(['key' => 'geocoder'],['value'  => 'something']);
        $this->assertInstanceOf(Geocodio::class, app(Geocoder::class));
    }

    public function it_returns_default_geocoder_when_geocoding_service_setting_is_not_google_and_country_is_canada()
    {
        Setting::flushCache();
        // override base test fake
        $this->forgetMock(Geocoder::class);
        $this->forgetMock(Geocodio::class);
        
        Setting::updateOrCreate(['key' => 'farm_country'],['value'  => 'USA']);
        Setting::updateOrCreate(['key' => 'geocoder'],['value'  => '']);
        $this->assertInstanceOf(GeocoderCa::class, app(Geocoder::class));
        
        Setting::updateOrCreate(['key' => 'geocoder'],['value'  => 'something']);
        $this->assertInstanceOf(GeocoderCa::class, app(Geocoder::class));

        $this->assertInstanceOf(Geocodio::class, app(Geocoder::class));
    }
}