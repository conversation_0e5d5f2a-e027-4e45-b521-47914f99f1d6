<?php

namespace Tests\Unit\Exports\QuickBooks;

use App\Exports\QuickBooks\InvoiceExport;
use App\Models\Coupon;
use App\Models\Order;
use App\Models\Product;
use App\Models\User;
use App\Support\Enums\Channel;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class InvoiceExportTest extends TestCase
{
    use RefreshDatabase;

    private InvoiceExport $export;
    private Builder $orderQuery;

    protected function setUp(): void
    {
        parent::setUp();

        $this->orderQuery = Order::query();
        $this->export = new InvoiceExport($this->orderQuery);
    }

    public function test_chunk_size_is_set_correctly()
    {
        $this->assertEquals(500, $this->export->chunkSize());
    }

    public function test_headings_are_correct()
    {
        $expectedHeadings = array_merge(
            [
                'Invoice No',
                'Customer',
                'Company',
                'Invoice Date',
                'Due Date',
                'Shipping Date',
                'Ship Via',
                'Tracking No',
                'Terms',
                'Billing Address Line 1',
                'Billing Address Line 2',
                'Billing Address City',
                'Billing Address Postal Code',
                'Billing Address Country',
                'Billing Address State',
                'Shipping Address Line 1',
                'Shipping Address Line 2',
                'Shipping Address City',
                'Shipping Address Postal Code',
                'Shipping Address Country',
                'Shipping Address State',
                'Memo',
                'Message displayed on sales receipt',
                'Email',
                'Email CC',
                'Email BCC',
                'Customer First Name',
                'Customer Last Name',
                'Print Status',
                'Email Status',
                'Shipping',
                'Sales Tax Code',
                'Sales Tax Amount',
                'Discount Amount',
                'Discount Percent',
                'Subscription Savings',
                'Apply Tax after Discount',
                'Deposit',
                'Location',
                'Enable ACH Payment',
            ],
            [
                'Product/Service',
                'Product/Service SKU',
                'Product/Service Description',
                'Product/Service Quantity',
                'Product/Service Packages',
                'Product/Service Rate',
                'Product/Service Amount',
                'Product/Service Taxable',
                'Product/Service Class',
                'Product/Service Class 2',
            ]
        );

        $this->assertEquals($expectedHeadings, $this->export->headings());
    }

    public function test_sales_channels_are_initialized()
    {
        $this->assertEquals(Channel::all()->toArray(), $this->export->salesChannels);
    }

    public function test_map_with_order_items()
    {
        $user = User::factory()->create();

        $this->actingAs($user);

        // Create a product
        $product = Product::factory()->create([
            'title' => 'Test Product',
            'sku' => 'TEST-123',
            'taxable' => true,
            'accounting_class' => 'Test Class'
        ]);

        // Create an order with the product
        $order = Order::factory()->create([
            'customer_id' => $user->id,
            'customer_first_name' => $user->first_name,
            'customer_last_name' => $user->last_name,
            'customer_email' => $user->email,
            'billing_street' => '123 Main St',
            'billing_city' => 'Test City',
            'billing_state' => 'CA',
            'billing_zip' => '12345',
            'billing_country' => 'USA',
            'shipping_street' => '123 Main St',
            'shipping_city' => 'Test City',
            'shipping_state' => 'CA',
            'shipping_zip' => '12345',
            'shipping_country' => 'USA',
            'pickup_date' => now(),
            'due_date' => now()->addDays(30),
            'tracking_id' => 'TRACK-123',
            'payment_terms' => 'Net 30',
            'packing_notes' => 'Test notes',
            'invoice_notes' => 'Test invoice notes',
            'type_id' => Channel::ONLINE_STORE->value,
            'credit_applied' => 0,
            'delivery_fee' => 0,
        ]);

        // Add an item to the order
        $order->items()->create([
            'product_id' => $product->id,
            'title' => 'Test Product',
            'unit_price' => 1000,
            'qty' => 2,
            'weight' => 1.5,
        ]);

        $mappedData = $this->export->map($order);

        // Verify the mapped data contains the expected values
        $this->assertEquals('GC-' . $order->id, $mappedData[0]['Invoice No']);
        $this->assertEquals($user->first_name . ' ' . $user->last_name . ' (' . $user->email . ')', $mappedData[0]['Customer']);
        $this->assertEquals('Test Product', $mappedData[0]['Product/Service']);
        $this->assertEquals('TEST-123', $mappedData[0]['Product/Service SKU']);
        $this->assertEquals('TRUE', $mappedData[0]['Product/Service Taxable']);
        $this->assertEquals('Test Class', $mappedData[0]['Product/Service Class 2']);
    }

    public function test_map_with_discounts()
    {
        $user = User::factory()->create();

        $order = Order::factory()->create([
            'type_id' => Channel::ONLINE_STORE->value,
            'credit_applied' => 0,
            'delivery_fee' => 0,
        ]);


        // Add a discount to the order
        $coupon = Coupon::factory()->create([
            'code' => 'TEST10',
            'description' => '10% off'
        ]);

        $order->discounts()->attach($coupon->id, ['user_id' => $user->id, 'savings' => 1000]);

        $mappedData = $this->export->map($order);

        // Verify the discount is properly mapped
        $this->assertEquals('TEST10', $mappedData[0]['Product/Service']);
        $this->assertEquals('TEST10', $mappedData[0]['Product/Service SKU']);
        $this->assertEquals('10% off (TEST10)', $mappedData[0]['Product/Service Description']);
        $this->assertEquals('-10.00', $mappedData[0]['Product/Service Rate']);
        $this->assertEquals('FALSE', $mappedData[0]['Product/Service Taxable']);
    }

    public function test_map_with_fees()
    {
        $order = Order::factory()->create([
            'type_id' => Channel::ONLINE_STORE->value,
            'credit_applied' => 0,
            'delivery_fee' => 0,
        ]);

        // Add a fee to the order
        $order->fees()->create([
            'title' => 'Processing Fee',
            'amount' => 500,
            'qty' => 1,
            'taxable' => true,
        ]);

        $mappedData = $this->export->map($order);

        // Verify the fee is properly mapped
        $this->assertEquals('Processing Fee', $mappedData[0]['Product/Service']);
        $this->assertEquals('processing-fee', $mappedData[0]['Product/Service SKU']);
        $this->assertEquals('Processing Fee', $mappedData[0]['Product/Service Description']);
        $this->assertEquals('5.00', $mappedData[0]['Product/Service Rate']);
        $this->assertEquals('TRUE', $mappedData[0]['Product/Service Taxable']);
    }

    public function test_map_with_credit_applied()
    {
        $order = Order::factory()->create([
            'type_id' => Channel::ONLINE_STORE->value,
            'credit_applied' => 2000,
            'delivery_fee' => 0,
        ]);

        $mappedData = $this->export->map($order);

        // Verify the credit is properly mapped
        $this->assertEquals('Credit Applied', $mappedData[0]['Product/Service']);
        $this->assertEquals('credit-applied', $mappedData[0]['Product/Service SKU']);
        $this->assertEquals('Credit Applied', $mappedData[0]['Product/Service Description']);
        $this->assertEquals('-20.00', $mappedData[0]['Product/Service Rate']);
        $this->assertEquals('FALSE', $mappedData[0]['Product/Service Taxable']);
    }

    public function test_map_with_multiple_items()
    {
        $order = Order::factory()->create([
            'type_id' => Channel::ONLINE_STORE->value,
            'credit_applied' => 2000,
            'delivery_fee' => 1500,
        ]);

        // Add a product
        $product = Product::factory()->create([
            'title' => 'Test Product',
            'sku' => 'TEST-123',
            'taxable' => true,
            'accounting_class' => 'Test Class'
        ]);

        // Add an item to the order
        $order->items()->create([
            'product_id' => $product->id,
            'title' => 'Test Product',
            'unit_price' => 1000,
            'qty' => 2,
            'weight' => 1.5,
        ]);

        $mappedData = $this->export->map($order);

        // Should have 3 rows: product, credit, and delivery fee
        $this->assertCount(3, $mappedData);

        // Find each type of row
        $productRow = collect($mappedData)->firstWhere('Product/Service', 'Test Product');
        $creditRow = collect($mappedData)->firstWhere('Product/Service', 'Credit Applied');
        $deliveryRow = collect($mappedData)->firstWhere('Product/Service', 'Delivery Fee');

        // Verify product row
        $this->assertEquals('TEST-123', $productRow['Product/Service SKU']);
        $this->assertEquals('TRUE', $productRow['Product/Service Taxable']);
        $this->assertEquals('Test Class', $productRow['Product/Service Class 2']);

        // Verify credit row
        $this->assertEquals('credit-applied', $creditRow['Product/Service SKU']);
        $this->assertEquals('-20.00', $creditRow['Product/Service Rate']);
        $this->assertEquals('FALSE', $creditRow['Product/Service Taxable']);

        // Verify delivery fee row
        $this->assertEquals('delivery-fee', $deliveryRow['Product/Service SKU']);
        $this->assertEquals('15.00', $deliveryRow['Product/Service Rate']);
        $this->assertEquals('FALSE', $deliveryRow['Product/Service Taxable']);
    }

    public function test_map_with_different_sales_channels()
    {
        $channels = [
            Channel::ONLINE_STORE->value,
            Channel::FARM_STORE->value,
            Channel::WHOLESALE->value,
        ];

        foreach ($channels as $channel) {
            $order = Order::factory()->create([
                'type_id' => $channel,
                'credit_applied' => 0,
                'delivery_fee' => 0,
            ]);


            $mappedData = $this->export->map($order);

            $this->assertEquals($this->export->salesChannels[$channel] ?? null, $mappedData[0]['Product/Service Class']);
        }
    }
}