<?php

namespace Tests\Unit\Jobs;

use App\Jobs\SendSubscriptionDemandReport;
use App\Notifications\SubscriptionDemandReport;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Storage;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class SendSubscriptionDemandReportTest extends TenantTestCase
{
    #[Test]
    public function it_generates_the_report(): void
    {
        Carbon::setTestNow(now());
        Storage::fake('s3');
        Notification::fake();

        $recipients = ['<EMAIL>', '<EMAIL>'];

        config([
            'mail.notifications.subscription_demand_report' => $recipients,
            'filesystems.file_upload_prefix' => 'testing_path'
        ]);

        DB::statement("CREATE OR REPLACE VIEW subscription_demand_by_pack_deadline AS
            SELECT '2025-01-01 00:00:00' AS pack_deadline_at, 1 AS product_id, 4 AS total_product_qty
        ");

        (new SendSubscriptionDemandReport)->handle();

        $timestamp = now()->format('Y_m_d_h_i');
        $expected_filepath = "testing_path/reports/subscription_demand_report_{$timestamp}.csv";

        Storage::disk('s3')->assertExists($expected_filepath);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_sends_the_report_to_the_expected_emails(): void
    {
        Carbon::setTestNow(now());
        Storage::fake('s3');
        Notification::fake();

        $recipients = ['<EMAIL>', '<EMAIL>'];

        config([
            'mail.notifications.subscription_demand_report' => $recipients,
            'filesystems.file_upload_prefix' => 'testing_path'
        ]);

        DB::statement("CREATE OR REPLACE VIEW subscription_demand_by_pack_deadline AS
            SELECT '2025-01-01 00:00:00' AS pack_deadline_at, 1 AS product_id, 4 AS total_product_qty
        ");

        (new SendSubscriptionDemandReport)->handle();

        Notification::assertSentOnDemandTimes(SubscriptionDemandReport::class, 2);

        $timestamp = now()->format('Y_m_d_h_i');
        $expected_filepath = "testing_path/reports/subscription_demand_report_{$timestamp}.csv";

        Notification::assertSentOnDemand(
            SubscriptionDemandReport::class,
            fn(SubscriptionDemandReport $notification, array $channels, object $notifiable)
            => in_array('mail', $channels)
                && in_array($notifiable->routes['mail'], $recipients)
                && $notification->filepath === $expected_filepath
        );

        Carbon::setTestNow();
    }

    #[Test]
    public function it_doesnt_generate_or_send_the_report_when_no_emails_are_configured(): void
    {
        Carbon::setTestNow(now());
        Storage::fake('s3');
        Notification::fake();

        config([
            'mail.notifications.subscription_demand_report' => [],
            'filesystems.file_upload_prefix' => 'testing_path'
        ]);

        DB::statement("CREATE OR REPLACE VIEW subscription_demand_by_pack_deadline AS
            SELECT '2025-01-01 00:00:00' AS pack_deadline_at, 1 AS product_id, 4 AS total_product_qty
        ");

        (new SendSubscriptionDemandReport)->handle();

        $timestamp = now()->format('Y_m_d_h_i');
        $expected_filepath = "testing_path/reports/subscription_demand_report_{$timestamp}.csv";

        Storage::disk('s3')->assertMissing($expected_filepath);

        Notification::assertNothingSent();

        Carbon::setTestNow();
    }

    #[Test]
    public function it_doesnt_generate_or_send_the_report_when_the_view_table_is_missing(): void
    {
        Carbon::setTestNow(now());
        Storage::fake('s3');
        Notification::fake();

        config([
            'mail.notifications.subscription_demand_report' => ['<EMAIL>', '<EMAIL>'],
            'filesystems.file_upload_prefix' => 'testing_path'
        ]);

        DB::statement("DROP VIEW IF EXISTS  subscription_demand_by_pack_deadline");

        (new SendSubscriptionDemandReport)->handle();

        $timestamp = now()->format('Y_m_d_h_i');
        $expected_filepath = "testing_path/reports/subscription_demand_report_{$timestamp}.csv";

        Storage::disk('s3')->assertMissing($expected_filepath);

        Notification::assertNothingSent();

        Carbon::setTestNow();
    }
}
