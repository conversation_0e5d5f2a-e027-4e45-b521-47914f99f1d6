<?php

namespace Tests\Unit\Jobs;

use App\Jobs\BackfillConfirmationDates;
use App\Models\Order;
use App\Models\RecurringOrder;
use App\Services\SubscriptionSettingsService;
use Mo<PERSON>y\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class BackfillConfirmationDatesTest extends TenantTestCase
{

    #[Test]
    public function it_sets_confirmation_date_relative_to_one_time_order_deadline_date(): void
    {
        $order = Order::factory()->create([
            'confirmed' => true,
            'confirmed_date' => null,
            'created_year' => 0,
            'created_month' => 0,
            'created_day' => 0,
            'blueprint_id' => null,
            'deadline_date' => today()->subDays(4),
            'payment_date' => today()->subDays(3),
            'canceled_at' => today()->subDays(2),
            'updated_at' => today()->subDays(1)
        ]);

        $this->mock(SubscriptionSettingsService::class, function (MockInterface $mock) {
            $mock->shouldNotReceive('inventoryManagementDayCount');
        });

        (new BackfillConfirmationDates())->handle();

        $expected_date = $order->deadline_date->copy()->subDay();

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'confirmed_date' => $expected_date,
            'created_year' => $expected_date->year,
            'created_month' => $expected_date->month,
            'created_day' => $expected_date->day
        ]);
    }

    #[Test]
    public function it_sets_confirmation_date_relative_to_subscription_order_deadline_date(): void
    {
        $order = Order::factory()->create([
            'confirmed' => true,
            'confirmed_date' => null,
            'created_year' => 0,
            'created_month' => 0,
            'created_day' => 0,
            'blueprint_id' => RecurringOrder::factory(),
            'deadline_date' => today()->subDays(4),
            'payment_date' => today()->subDays(3),
            'canceled_at' => today()->subDays(2),
            'updated_at' => today()->subDays(1)
        ]);

        $this->mock(SubscriptionSettingsService::class, function (MockInterface $mock) {
            $mock->shouldReceive('inventoryManagementDayCount')->once()->andReturn(4);
        });

        (new BackfillConfirmationDates())->handle();

        $expected_date = $order->deadline_date->copy()->subDays(4);

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'confirmed_date' => $expected_date,
            'created_year' => $expected_date->year,
            'created_month' => $expected_date->month,
            'created_day' => $expected_date->day
        ]);
    }

    #[Test]
    public function it_sets_confirmation_date_relative_to_order_payment_date(): void
    {
        $order = Order::factory()->create([
            'confirmed' => true,
            'confirmed_date' => null,
            'created_year' => 0,
            'created_month' => 0,
            'created_day' => 0,
            'blueprint_id' => null,
            'deadline_date' => null,
            'payment_date' => today()->subDays(3),
            'canceled_at' => today()->subDays(2),
            'updated_at' => today()->subDays(1)
        ]);

        (new BackfillConfirmationDates())->handle();

        $expected_date = $order->payment_date->copy()->subDay();

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'confirmed_date' => $expected_date,
            'created_year' => $expected_date->year,
            'created_month' => $expected_date->month,
            'created_day' => $expected_date->day
        ]);
    }

    #[Test]
    public function it_sets_confirmation_date_relative_to_order_cancel_date(): void
    {
        $order = Order::factory()->create([
            'confirmed' => true,
            'confirmed_date' => null,
            'created_year' => 0,
            'created_month' => 0,
            'created_day' => 0,
            'blueprint_id' => null,
            'deadline_date' => null,
            'payment_date' => null,
            'canceled_at' => today()->subDays(2),
            'updated_at' => today()->subDays(1)
        ]);

        (new BackfillConfirmationDates())->handle();

        $expected_date = $order->canceled_at->copy()->subDay();

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'confirmed_date' => $expected_date,
            'created_year' => $expected_date->year,
            'created_month' => $expected_date->month,
            'created_day' => $expected_date->day
        ]);
    }

    #[Test]
    public function it_sets_confirmation_date_relative_to_order_last_update_date(): void
    {
        $order = Order::factory()->create([
            'confirmed' => true,
            'confirmed_date' => null,
            'created_year' => 0,
            'created_month' => 0,
            'created_day' => 0,
            'blueprint_id' => null,
            'deadline_date' => null,
            'payment_date' => null,
            'canceled_at' => null,
            'updated_at' => today()->subDays(1)
        ]);

        (new BackfillConfirmationDates())->handle();

        $expected_date = $order->updated_at->copy()->subDay();

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'confirmed_date' => $expected_date,
            'created_year' => $expected_date->year,
            'created_month' => $expected_date->month,
            'created_day' => $expected_date->day
        ]);
    }

    #[Test]
    public function it_does_not_update_confirmation_date_for_unconfirmed_orders(): void
    {
        $order = Order::factory()->create([
            'confirmed' => false,
            'confirmed_date' => null,
            'created_year' => 0,
            'created_month' => 0,
            'created_day' => 0,
            'blueprint_id' => null,
            'deadline_date' => today()->subDays(4),
            'payment_date' => today()->subDays(3),
            'canceled_at' => today()->subDays(2),
            'updated_at' => today()->subDays(1)
        ]);

        (new BackfillConfirmationDates())->handle();

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'confirmed_date' => null,
            'created_year' => 0,
            'created_month' => 0,
            'created_day' => 0,
        ]);
    }

    #[Test]
    public function it_does_not_update_confirmation_date_for_orders_with_confirmed_date(): void
    {
        $order = Order::factory()->create([
            'confirmed' => false,
            'confirmed_date' => today()->addWeek(),
            'created_year' => 0,
            'created_month' => 0,
            'created_day' => 0,
            'blueprint_id' => null,
            'deadline_date' => today()->subDays(4),
            'payment_date' => today()->subDays(3),
            'canceled_at' => today()->subDays(2),
            'updated_at' => today()->subDays(1)
        ]);

        (new BackfillConfirmationDates())->handle();

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'confirmed_date' => $order->confirmed_date,
            'created_year' => 0,
            'created_month' => 0,
            'created_day' => 0,
        ]);
    }
}
