<?php

namespace Tests\Unit\Jobs;

use App\Integrations\Drip\Drip;
use App\Jobs\RecordBatchOfEventsInDrip;
use App\Models\Integration;
use Mo<PERSON>y\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class RecordBatchOfEventsInDripTest extends TenantTestCase
{
    #[Test]
    public function it_makes_the_expected_batch_subscriber_updates_request(): void
    {
        Integration::factory()->create([
            'name' => 'drip',
            'enabled' => true,
            'settings' => [
                'api_key' => 'abc123',
                'account_id' => 'def456'
            ]
        ]);

        $events = [
            ['email' => '<EMAIL>', 'action' => 'event1 name', 'properties' => ['some' => 'props1']],
            ['email' => '<EMAIL>', 'action' => 'event2 name', 'properties' => ['some' => 'props2']]
        ];

        $this->mock(Drip::class, function (MockInterface $mock) use ($events) {
            $mock->shouldReceive('configure')->once()->andReturnSelf();
            $mock->shouldReceive('recordBatchOfEvents')->once()->with($events);
        });

        (new RecordBatchOfEventsInDrip($events))->handle();
    }

    #[Test]
    public function it_doesnt_make_request_if_subscribers_is_empty(): void
    {
        Integration::factory()->create([
            'name' => 'drip',
            'enabled' => true,
            'settings' => [
                'api_key' => 'abc123',
                'account_id' => 'def456'
            ]
        ]);

        $events = [];

        $this->mock(Drip::class, function (MockInterface $mock) use ($events) {
            $mock->shouldNotReceive('configure');
            $mock->shouldNotReceive('recordBatchOfEvents');
        });

        (new RecordBatchOfEventsInDrip($events))->handle();
    }

    #[Test]
    public function it_doesnt_make_request_if_drip_integration_is_not_enabled(): void
    {
        Integration::factory()->create([
            'name' => 'drip',
            'enabled' => false,
            'settings' => [
                'api_key' => 'abc123',
                'account_id' => 'def456'
            ]
        ]);

        $events = [
            ['email' => '<EMAIL>', 'action' => 'event1 name', 'properties' => ['some' => 'props1']],
            ['email' => '<EMAIL>', 'action' => 'event2 name', 'properties' => ['some' => 'props2']]
        ];

        $this->mock(Drip::class, function (MockInterface $mock) use ($events) {
            $mock->shouldNotReceive('configure');
            $mock->shouldNotReceive('recordBatchOfEvents');
        });

        (new RecordBatchOfEventsInDrip($events))->handle();
    }
}
