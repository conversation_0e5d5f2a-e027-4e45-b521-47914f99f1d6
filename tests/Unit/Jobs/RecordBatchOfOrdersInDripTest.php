<?php

namespace Tests\Unit\Jobs;

use App\Integrations\Drip\Drip;
use App\Integrations\Drip\DripShopperActivity;
use App\Jobs\RecordBatchOfOrdersInDrip;
use App\Models\Integration;
use Mockery\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class RecordBatchOfOrdersInDripTest extends TenantTestCase
{
    #[Test]
    public function it_makes_the_expected_batch_subscriber_updates_request(): void
    {
        Integration::factory()->create([
            'name' => 'drip',
            'enabled' => true,
            'settings' => [
                'api_key' => 'abc123',
                'account_id' => 'def456'
            ]
        ]);

        $orders = [
            ['email' => '<EMAIL>', 'foo' => 'bar'],
            ['email' => '<EMAIL>', 'foo' => 'bar']
        ];

        $shopper_activity_mock = \Mockery::mock(DripShopperActivity::class, function (MockInterface $mock) use ($orders) {
            $mock->shouldReceive('recordBatchOfOrders')->once()->with($orders);
        });

        $this->mock(Drip::class, function (MockInterface $mock) use ($orders, $shopper_activity_mock) {
            $mock->shouldReceive('configure')->once()->andReturnSelf();
            $mock->shouldReceive('shopperActivity')->once()->andReturn($shopper_activity_mock);
        });

        (new RecordBatchOfOrdersInDrip($orders))->handle();
    }

    #[Test]
    public function it_doesnt_make_request_if_subscribers_is_empty(): void
    {
        Integration::factory()->create([
            'name' => 'drip',
            'enabled' => true,
            'settings' => [
                'api_key' => 'abc123',
                'account_id' => 'def456'
            ]
        ]);

        $orders = [];

        $this->mock(Drip::class, function (MockInterface $mock) use ($orders) {
            $mock->shouldNotReceive('configure');
            $mock->shouldNotReceive('recordBatchOfSubscriberUpdates');
        });

        (new RecordBatchOfOrdersInDrip($orders))->handle();
    }

    #[Test]
    public function it_doesnt_make_request_if_drip_integration_is_not_enabled(): void
    {
        Integration::factory()->create([
            'name' => 'drip',
            'enabled' => false,
            'settings' => [
                'api_key' => 'abc123',
                'account_id' => 'def456'
            ]
        ]);

        $orders = [
            ['email' => '<EMAIL>', 'foo' => 'bar'],
            ['email' => '<EMAIL>', 'foo' => 'bar']
        ];

        $this->mock(Drip::class, function (MockInterface $mock) use ($orders) {
            $mock->shouldNotReceive('configure');
            $mock->shouldNotReceive('recordBatchOfSubscriberUpdates');
        });

        (new RecordBatchOfOrdersInDrip($orders))->handle();
    }
}
