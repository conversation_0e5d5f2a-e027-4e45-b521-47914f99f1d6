<?php

namespace Tests\Unit\Jobs;

use App\Jobs\SendWebhook;
use App\Models\Webhook;
use Illuminate\Http\Client\Request;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Http;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class SendWebhookTest extends TenantTestCase
{
    #[Test]
    public function it_can_be_dispatched(): void
    {
        Bus::fake([SendWebhook::class]);

        $webhook = Webhook::factory()->create();
        
        SendWebhook::dispatch($webhook, ['fizz' => 'buzz']);

        Bus::assertDispatched(SendWebhook::class);
    }

    #[Test]
    public function it_sends_the_expected_request(): void
    {
        $webhook = Webhook::factory()->create();

        Http::fake([
            $webhook->target_url => Http::response(['foo' => 'bar'])
        ]);

        $response = (new SendWebhook($webhook, ['fizz' => 'buzz']))->handle();

        $this->assertEquals(['foo' => 'bar'], $response);

        Http::assertSentCount(1);

        Http::assertSent(function (Request $request) use ($webhook) {
            return $request->url() === $webhook->target_url
                && $request->method() === 'POST'
                && $request['topic'] === $webhook->topic
                && $request['data'] === ['fizz' => 'buzz'];
        });
    }

    #[Test]
    public function it_tries_to_retry_request_three_times_upon_failure(): void
    {
        $webhook = Webhook::factory()->create();

        Http::fake([
            $webhook->target_url => Http::response([], 400)
        ]);

        try {
            (new SendWebhook($webhook, ['fizz' => 'buzz']))->handle();
        } catch (RequestException $exception) {
            $this->assertTrue(true, 'The final exception is thrown as expected.');
        }

        Http::assertSentCount(3);
    }
}