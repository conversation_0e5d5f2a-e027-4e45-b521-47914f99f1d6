<?php

namespace Tests\Unit\Jobs;

use App\Actions\ProcessOrder as ProcessOrderAction;
use App\Jobs\ProcessOrder;
use App\Models\Order;
use Mockery\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class ProcessOrderTest extends TenantTestCase
{
    #[Test]
    public function it_processes_the_order(): void
    {
        $order = Order::factory()->create();

        $this->mock(ProcessOrderAction::class, function (MockInterface $mock) use ($order) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(\Mockery::on( fn(Order $mock) => $mock->id === $order->id))
                ->andReturn([]);
        });

        (new ProcessOrder($order->id))->handle();
    }

    #[Test]
    public function it_does_not_process_invalid_order_ids(): void
    {
        $this->mock(ProcessOrder::class, function (MockInterface $mock) {
            $mock->shouldNotReceive('handle');
        });

        (new ProcessOrder(12312312))->handle();
    }
}
