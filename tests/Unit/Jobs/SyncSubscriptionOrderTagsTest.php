<?php

namespace Tests\Unit\Jobs;

use App\Actions\Order\ApplyTags;
use App\Jobs\SyncSubscriptionOrderTags;
use App\Models\Order;
use App\Models\RecurringOrder;
use Mockery\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class SyncSubscriptionOrderTagsTest extends TenantTestCase
{
    #[Test]
    public function it_syncs_tags_for_an_order(): void
    {
        $order = Order::factory()->create(['blueprint_id' => RecurringOrder::factory(), 'canceled' => 0]);

        $this->mock(ApplyTags::class, function (MockInterface $mock) use ($order) {
            return $mock->shouldReceive('handle')
                ->once()
                ->with(\Mockery::on(fn(Order $arg) => $arg->id === $order->id))
                ->andReturn($order);
        });

        (new SyncSubscriptionOrderTags([$order->id]))->handle();
    }

    #[Test]
    public function it_syncs_tags_for_multiple_orders(): void
    {
        $order_one = Order::factory()->create(['blueprint_id' => RecurringOrder::factory(), 'canceled' => 0]);
        $order_two = Order::factory()->create(['blueprint_id' => RecurringOrder::factory(), 'canceled' => 0]);

        $this->mock(ApplyTags::class, function (MockInterface $mock) use ($order_one, $order_two) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(\Mockery::on(fn(Order $arg) => $arg->id === $order_one->id))
                ->andReturn($order_one);

            $mock->shouldReceive('handle')
                ->once()
                ->with(\Mockery::on(fn(Order $arg) => $arg->id === $order_two->id))
                ->andReturn($order_two);
        });

        (new SyncSubscriptionOrderTags([$order_one->id, $order_two->id]))->handle();
    }

    #[Test]
    public function it_does_not_syncs_tags_for_a_canceled_order(): void
    {
        $order = Order::factory()->create(['blueprint_id' => RecurringOrder::factory(), 'canceled' => 1]);

        $this->mock(ApplyTags::class, function (MockInterface $mock) {
            return $mock->shouldNotReceive('handle');
        });

        (new SyncSubscriptionOrderTags([$order->id]))->handle();
    }

    #[Test]
    public function it_does_not_syncs_tags_for_a_one_time_order(): void
    {
        $order = Order::factory()->create(['blueprint_id' => null, 'canceled' => 0]);

        $this->mock(
            ApplyTags::class,
            fn(MockInterface $mock) => $mock->shouldNotReceive('handle')
        );

        (new SyncSubscriptionOrderTags([$order->id]))->handle();
    }

    #[Test]
    public function it_does_not_syncs_tags_for_an_invalid_order(): void
    {
        $this->mock(
            ApplyTags::class,
            fn(MockInterface $mock) => $mock->shouldNotReceive('handle')
        );

        (new SyncSubscriptionOrderTags([1231241234123]))->handle();
    }
}