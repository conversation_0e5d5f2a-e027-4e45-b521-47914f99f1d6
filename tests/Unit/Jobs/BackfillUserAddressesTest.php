<?php

namespace Tests\Unit\Jobs;

use App\Jobs\BackFillUserAddresses;
use Illuminate\Support\Facades\Bus;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class BackfillUserAddressesTest extends TenantTestCase
{
    #[Test]
    public function it_dispatches_job(): void
    {
        Bus::fake([BackFillUserAddresses::class]);

        $this->artisan('grazecart:backfill-user-addresses')->assertExitCode(0);

        Bus::assertDispatched(BackFillUserAddresses::class);
    }
}
