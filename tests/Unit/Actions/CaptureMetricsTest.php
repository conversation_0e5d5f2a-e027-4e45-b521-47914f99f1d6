<?php

namespace Tests\Unit\Actions;

use App\Actions\CaptureMetrics;
use App\Models\Metric;
use Carbon\Carbon;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class CaptureMetricsTest extends TenantTestCase
{
    #[Test]
    public function it_stores_metrics_in_the_database(): void
    {
        Carbon::setTestNow(now());

        $marketing_review = [
            'review_date' => '2023-10-01',
            'snapshot' => '2023-10-01',
            'metrics' => [
                'metric1' => [
                    'value1' => 100,
                    'value2' => 200,
                ],
                'metric2' => [
                    'value1' => 300,
                    'value2' => 400,
                ],
            ],
        ];

        (new CaptureMetrics)->handle(name: 'marketing_review',  values: $marketing_review);

        $this->assertTrue(
            Metric::query()
                ->where([
                    'name' => 'marketing_review',
                    'captured_at' => today()->format('Y-m-d H:i:s'),
                ])
                ->withExtraAttributes([
                    'review_date' => '2023-10-01',
                    'snapshot' => '2023-10-01',
                ])
                ->exists()
        );

        Carbon::setTestNow();
    }
}
