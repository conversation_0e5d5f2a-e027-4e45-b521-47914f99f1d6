<?php

namespace Tests\Unit\Actions\Order;

use App\Actions\Order\Remove;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use App\Models\RecurringOrder;
use App\Models\User;
use Illuminate\Support\Carbon;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class RemoveTest extends TenantTestCase
{
    #[Test]
    public function it_removes_the_order(): void
    {
        $order = Order::factory()->create();

        (new Remove)->handle($order);

        $this->assertDatabaseMissing(Order::class, ['id' => $order->id]);
    }

    #[Test]
    public function it_removes_the_items_when_order_is_confirmed(): void
    {
        $order = Order::factory()->create(['confirmed' => true]);
        $items = OrderItem::factory(2)->create(['order_id' => $order->id]);

        (new Remove)->handle($order);

        foreach ($items as $item) {
            $this->assertDatabaseMissing(OrderItem::class, ['id' => $item->id]);
        }
    }

    #[Test]
    public function it_releases_full_product_inventory_when_order_is_confirmed(): void
    {
        $order = Order::factory()->create(['confirmed' => true]);
        $product = Product::factory()->create(['track_inventory' => 'yes', 'inventory' => 10]);
        OrderItem::factory()->create([
            'order_id' => $order->id,
            'product_id' => $product,
            'qty' => 2,
            'fulfilled_qty' => 3,
            'stock_status' => 'full'
        ]);

        (new Remove)->handle($order);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product->id,
            'inventory' => 12,
        ]);
    }

    #[Test]
    public function it_releases_short_product_inventory_when_order_is_confirmed(): void
    {
        $order = Order::factory()->create(['confirmed' => true]);
        $product = Product::factory()->create(['track_inventory' => 'yes', 'inventory' => 10]);
        OrderItem::factory()->create([
            'order_id' => $order->id,
            'product_id' => $product,
            'qty' => 2,
            'stock_status' => 'short',
            'fulfilled_qty' => 1
        ]);

        (new Remove)->handle($order);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product->id,
            'inventory' => 11,
        ]);
    }

    #[Test]
    public function it_releases_out_of_stock_product_inventory_when_order_is_confirmed(): void
    {
        $order = Order::factory()->create(['confirmed' => true]);
        $product = Product::factory()->create(['track_inventory' => 'yes', 'inventory' => 10]);
        OrderItem::factory()->create([
            'order_id' => $order->id,
            'product_id' => $product,
            'qty' => 2,
            'stock_status' => 'out',
            'fulfilled_qty' => 1
        ]);

        (new Remove)->handle($order);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product->id,
            'inventory' => 11,
        ]);
    }

    #[Test]
    public function it_does_not_release_product_inventory_when_order_is_is_not_confirmed(): void
    {
        $order = Order::factory()->create(['confirmed' => false]);
        $product = Product::factory()->create(['track_inventory' => 'yes', 'inventory' => 10]);
        OrderItem::factory()->create([
            'order_id' => $order->id,
            'product_id' => $product,
            'qty' => 2,
            'stock_status' => 'full'
        ]);

        (new Remove)->handle($order);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product->id,
            'inventory' => 10,
        ]);
    }

    #[Test]
    public function it_refunds_customer_credit_when_order_is_confirmed(): void
    {
        $user = User::factory()->create(['credit' => 100]);
        $order = Order::factory()->create([
            'customer_id' => $user->id,
            'confirmed' => true,
            'credit_applied' => 1000
        ]);

        (new Remove)->handle($order);

        $this->assertDatabaseHas(User::class, [
            'id' => $user->id,
            'credit' => 1100,
        ]);
    }

    #[Test]
    public function it_does_not_refund_customer_credit_when_order_is_not_confirmed(): void
    {
        $user = User::factory()->create(['credit' => 100]);
        $order = Order::factory()->create([
            'customer_id' => $user->id,
            'confirmed' => false,
            'credit_applied' => 1000
        ]);

        (new Remove)->handle($order);

        $this->assertDatabaseHas(User::class, [
            'id' => $user->id,
            'credit' => 100,
        ]);
    }

    #[Test]
    public function it_decrements_customers_order_count_when_order_is_confirmed(): void
    {
        $user = User::factory()->create(['order_count' => 2, 'recurring_order_count' => 2]);
        $order = Order::factory()->create(['customer_id' => $user->id, 'confirmed' => true, 'is_recurring' => false]);

        (new Remove)->handle($order);

        $this->assertDatabaseHas(User::class, [
            'id' => $user->id,
            'order_count' => 1,
            'recurring_order_count' => 2
        ]);
    }

    #[Test]
    public function it_does_not_decrement_customers_order_count_when_order_is_not_confirmed(): void
    {
        $user = User::factory()->create(['order_count' => 2, 'recurring_order_count' => 2]);
        $order = Order::factory()->create(['customer_id' => $user->id, 'confirmed' => false, 'is_recurring' => false]);

        (new Remove)->handle($order);

        $this->assertDatabaseHas(User::class, [
            'id' => $user->id,
            'order_count' => 2,
            'recurring_order_count' => 2
        ]);
    }

    #[Test]
    public function it_decrements_customers_recurring_order_count_when_recurring_order_is_confirmed(): void
    {
        $user = User::factory()->create(['order_count' => 2, 'recurring_order_count' => 2]);
        $order = Order::factory()->create(['customer_id' => $user->id, 'confirmed' => true, 'is_recurring' => true]);

        (new Remove)->handle($order);

        $this->assertDatabaseHas(User::class, [
            'id' => $user->id,
            'order_count' => 2,
            'recurring_order_count' => 1,
        ]);
    }

    #[Test]
    public function it_does_not_decrement_customers_recurring_order_count_when_recurring_order_is_not_confirmed(): void
    {
        $user = User::factory()->create(['order_count' => 2, 'recurring_order_count' => 2]);
        $order = Order::factory()->create(['customer_id' => $user->id, 'confirmed' => false, 'is_recurring' => true]);

        (new Remove)->handle($order);

        $this->assertDatabaseHas(User::class, [
            'id' => $user->id,
            'order_count' => 2,
            'recurring_order_count' => 2,
        ]);
    }

    #[Test]
    public function it_cancels_customers_recurring_blueprint_when_last_order_on_blueprint_is_removed(): void
    {
        Carbon::setTestNow($now = now());

        $user = User::factory()->create(['order_count' => 2, 'recurring_order_count' => 2]);
        $blueprint = RecurringOrder::factory()->create(['customer_id' => $user->id]);

        $order = Order::factory()->create([
            'customer_id' => $user->id,
            'confirmed' => true,
            'is_recurring' => true,
            'blueprint_id' => $blueprint->id
        ]);

        (new Remove)->handle($order);

        $this->assertDatabaseHas(RecurringOrder::class, [
            'id' => $blueprint->id,
            'deleted_at' => $now->format('Y-m-d H:i:s'),
        ]);
    }

    #[Test]
    public function it_does_not_cancels_customers_recurring_blueprint_when_not_last_order_on_blueprint_is_removed(): void
    {
        Carbon::setTestNow($now = now());

        $user = User::factory()->create(['order_count' => 2, 'recurring_order_count' => 2]);
        $blueprint = RecurringOrder::factory()->create(['customer_id' => $user->id]);

        Order::factory()->create(['blueprint_id' => $blueprint->id]);

        $order = Order::factory()->create([
            'customer_id' => $user->id,
            'confirmed' => true,
            'is_recurring' => true,
            'blueprint_id' => $blueprint->id
        ]);

        (new Remove)->handle($order);

        $this->assertDatabaseHas(RecurringOrder::class, [
            'id' => $blueprint->id,
            'deleted_at' => null
        ]);
    }
}