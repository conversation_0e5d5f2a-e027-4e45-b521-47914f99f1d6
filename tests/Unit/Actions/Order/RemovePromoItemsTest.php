<?php

namespace Tests\Unit\Actions\Order;

use App\Actions\Order\RemovePromoItems;
use App\Models\Order;
use App\Models\OrderItem;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class RemovePromoItemsTest extends TenantTestCase
{
    #[Test]
    public function it_can_remove_promo_items_from_an_order(): void
    {
        $order = Order::factory()->create();

        $standard_item = OrderItem::factory()->create(['order_id' => $order->id, 'type' => 'standard']);
        $promo_item_one = OrderItem::factory()->create(['order_id' => $order->id, 'type' => 'promo']);
        $promo_item_two = OrderItem::factory()->create(['order_id' => $order->id, 'type' => 'promo']);
        $addon_item = OrderItem::factory()->create(['order_id' => $order->id, 'type' => 'addon']);

        (new RemovePromoItems)->handle($order);

        $this->assertDatabaseHas(OrderItem::class, ['id' => $standard_item->id]);
        $this->assertDatabaseMissing(OrderItem::class, ['id' => $promo_item_one->id]);
        $this->assertDatabaseMissing(OrderItem::class, ['id' => $promo_item_two->id]);
        $this->assertDatabaseHas(OrderItem::class, ['id' => $addon_item->id]);
    }
}