<?php

namespace Tests\Unit\Actions\Order;

use App\Actions\IssueGiftCard;
use App\Actions\Order\AddItem;
use App\Events\Order\ItemWasAddedToOrder;
use App\Exceptions\BackOrderException;
use App\Exceptions\ExclusivityException;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use App\Models\Setting;
use App\Services\SubscriptionSettingsService;
use App\Support\Enums\ProductType;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Event;
use Mockery\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class AddItemTest extends TenantTestCase
{
    #[Test]
    public function it_throws_an_exception_when_attempting_to_add_an_excluded_item(): void
    {
        /** @var Order $order */
        $order = Order::factory()->create();

        /** @var Product $product */
        $product = Product::factory()->create();

        $order->pickup->products()->attach($product->id);

        $this->expectException(ExclusivityException::class);
        $this->expectExceptionMessage('This product is currently not available at your location.');

        (new AddItem)->handle($order, $product, 2);
    }

    #[Test]
    public function it_throws_an_exception_when_attempting_to_add_a_quantity_greater_than_the_product_limit(): void
    {
        /** @var Order $order */
        $order = Order::factory()->create();

        /** @var Product $product */
        $product = Product::factory()->create(['settings' => ['quantity_limit' => 2]]);

        $this->expectException(BackOrderException::class);
        $this->expectExceptionMessage("There is a limit of 2 per customer for this product.");

        (new AddItem)->handle($order, $product, 3);
    }

    #[Test]
    public function it_throws_an_exception_when_attempting_to_add_an_item_without_available_quantity(): void
    {
        /** @var Order $order */
        $order = Order::factory()->create();

        /** @var Product $product */
        $product = Product::factory()->create(['track_inventory' => 'yes', 'inventory' => 1]);

        $this->expectException(BackOrderException::class);
        $this->expectExceptionMessage("There is not enough {$product->title} left in stock.");

        (new AddItem)->handle($order, $product, 2);
    }

    #[Test]
    public function it_throws_an_exception_when_attempting_to_update_an_existing_item_without_available_quantity(): void
    {
        /** @var Order $order */
        $order = Order::factory()->create();

        /** @var Product $product */
        $product = Product::factory()->create(['track_inventory' => 'yes', 'inventory' => 4]);

        OrderItem::factory()->create(['order_id' => $order->id, 'product_id' => $product->id, 'qty' => 2]);

        $this->expectException(BackOrderException::class);
        $this->expectExceptionMessage("There is not enough {$product->title} left in stock.");

        (new AddItem)->handle($order, $product, 3);
    }

    #[Test]
    public function it_throws_an_exception_when_attempting_to_add_an_item_to_an_order_has_already_been_paid(): void
    {
        /** @var Order $order */
        $order = Order::factory()->create(['paid' => true]);

        /** @var Product $product */
        $product = Product::factory()->create();

        $this->expectException(BackOrderException::class);
        $this->expectExceptionMessage('A product cannot added because this order has already been paid.');

        (new AddItem)->handle($order, $product, 2);
    }

    #[Test]
    public function it_updates_an_existing_item_when_attempting_to_add_an_already_added_product(): void
    {
        Event::fake([ItemWasAddedToOrder::class]);

        /** @var Order $order */
        $order = Order::factory()->create();

        /** @var Product $product */
        $product = Product::factory()->create();

        /** @var OrderItem $item */
        $item = OrderItem::factory()->create(['order_id' => $order->id, 'product_id' => $product->id, 'qty' => 2]);

        $result = (new AddItem)->handle($order, $product, 3);

        $this->assertEquals($item->id, $result->id);
        $this->assertEquals(5, $result->qty);

        $this->assertDatabaseHas(OrderItem::class, [
            'id' => $item->id,
            'qty' => 5
        ]);
    }

    #[Test]
    public function it_does_not_update_an_existing_item_when_attempting_to_add_an_already_added_gift_card_product(): void
    {
        Event::fake([ItemWasAddedToOrder::class]);

        /** @var Order $order */
        $order = Order::factory()->create();

        /** @var Product $product */
        $product = Product::factory()->create(['type_id' => ProductType::GIFT_CARD->value]);

        /** @var OrderItem $item */
        $item = OrderItem::factory()->create(['order_id' => $order->id, 'product_id' => $product->id, 'qty' => 2]);

        $this->actingAsAdmin();

        $result = (new AddItem)->handle($order, $product, 3);

        $this->assertNotEquals($item->id, $result->id);
        $this->assertEquals(1, $result->qty);

        $this->assertDatabaseHas(OrderItem::class, [
            'id' => $item->id,
            'qty' => 2
        ]);

        $this->assertDatabaseHas(OrderItem::class, [
            'order_id' => $order->id,
            'product_id' => $product->id,
            'qty' => 1
        ]);

        $this->assertEquals(3, OrderItem::where([
            'order_id' => $order->id,
            'product_id' => $product->id,
            'qty' => 1
        ])->count());
    }

    #[Test]
    public function it_adds_an_additional_promo_item_when_attempting_to_add_an_already_added_promo_product(): void
    {
        Event::fake([ItemWasAddedToOrder::class]);

        /** @var Order $order */
        $order = Order::factory()->create();

        /** @var Product $product */
        $product = Product::factory()->create();

        /** @var OrderItem $item */
        $item = OrderItem::factory()->create(['order_id' => $order->id, 'product_id' => $product->id, 'qty' => 2, 'type' => 'promo']);

        $result = (new AddItem)->handle($order, $product, 3, 'promo');

        $this->assertNotEquals($item->id, $result->id);
        $this->assertEquals(3, $result->qty);

        $this->assertDatabaseHas(OrderItem::class, [
            'id' => $item->id,
            'product_id' => $product->id,
            'type' => 'promo',
            'qty' => 2
        ]);

        $this->assertDatabaseHas(OrderItem::class, [
            'id' => $result->id,
            'product_id' => $product->id,
            'type' => 'promo',
            'qty' => 3
        ]);
    }

    #[Test]
    public function it_does_not_dispatch_item_added_to_order_event_when_adding_an_already_added_product(): void
    {
        Event::fake([ItemWasAddedToOrder::class]);

        /** @var Order $order */
        $order = Order::factory()->create();

        /** @var Product $product */
        $product = Product::factory()->create();

        /** @var OrderItem $item */
        $item = OrderItem::factory()->create(['order_id' => $order->id, 'product_id' => $product->id, 'qty' => 2]);

        $result = (new AddItem)->handle($order, $product, 3);

        $this->assertEquals($item->id, $result->id);
        $this->assertEquals(5, $result->qty);

        Event::assertNotDispatched(ItemWasAddedToOrder::class);
    }

    #[Test]
    public function it_adds_a_standard_item_when_adding_an_excluded_product_to_a_one_time_purchase_order(): void
    {
        Event::fake([ItemWasAddedToOrder::class]);

        /** @var Order $order */
        $order = Order::factory()->create(['is_recurring' => false]);

        /** @var Product $product */
        $product = Product::factory()->create();

        Setting::updateOrCreate(['key' => 'recurring_orders_excluded_products'], ['value' => json_encode([$product->id])]);

        $result = (new AddItem)->handle($order, $product, 2);

        $this->assertEquals('standard', $result->type);

        $this->assertDatabaseHas(OrderItem::class, [
            'id' => $result->id,
            'type' => 'standard'
        ]);
    }

    #[Test]
    public function it_adds_an_addon_item_when_adding_an_excluded_product_to_a_recurring_order(): void
    {
        Event::fake([ItemWasAddedToOrder::class]);

        /** @var Product $product */
        $product = Product::factory()->create();

        $this->mock(SubscriptionSettingsService::class, function (MockInterface $mock) use ($product) {
            $mock->shouldReceive('excludedProductIds')->andReturn(collect([$product->id]));
        });

        /** @var Order $order */
        $order = Order::factory()->create(['is_recurring' => true]);

        $result = (new AddItem)->handle($order, $product, 2);

        $this->assertEquals('addon', $result->type);

        $this->assertDatabaseHas(OrderItem::class, [
            'id' => $result->id,
            'type' => 'addon'
        ]);
    }

    #[Test]
    public function it_can_add_a_standard_item(): void
    {
        Carbon::setTestNow(now());

        Event::fake([ItemWasAddedToOrder::class]);

        /** @var Order $order */
        $order = Order::factory()->create();

        /** @var Product $product */
        $product = Product::factory()->create();

        $result = (new AddItem)->handle($order, $product, 2);

        $this->assertDatabaseHas(OrderItem::class, [
            'id' => $result->id,
            'order_id' => $order->id,
            'product_id' => $product->id,
            'user_id' => $order->customer_id,
            'title' => $product->title,
            'type' => 'standard',
            'unit_price' => $product->getUnitPrice(),
            'original_unit_price' => $product->getUnitPrice(),
            'qty' => 2,
            'original_qty' => 2,
            'stock_status' => 'full',
            'fulfilled_qty' => 2,
            'weight' => $product->weight * 2,
            'original_weight' => $product->weight * 2,
            'store_price' => $product->getPrice(),
            'unit_of_issue' => $product->unit_of_issue,
            'taxable' => $product->taxable,
            'created_year' => today()->year,
            'created_month' => today()->month,
            'created_day' => today()->day,
        ]);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_can_add_a_promo_item(): void
    {
        Carbon::setTestNow(now());

        Event::fake([ItemWasAddedToOrder::class]);

        /** @var Order $order */
        $order = Order::factory()->create();

        /** @var Product $product */
        $product = Product::factory()->create();

        $result = (new AddItem)->handle($order, $product, 2, 'promo');

        $this->assertDatabaseHas(OrderItem::class, [
            'id' => $result->id,
            'order_id' => $order->id,
            'product_id' => $product->id,
            'user_id' => $order->customer_id,
            'title' => $product->title,
            'type' => 'promo',
            'unit_price' => $product->getUnitPrice(),
            'original_unit_price' => $product->getUnitPrice(),
            'qty' => 2,
            'original_qty' => 2,
            'stock_status' => 'full',
            'fulfilled_qty' => 2,
            'weight' => $product->weight * 2,
            'original_weight' => $product->weight * 2,
            'store_price' => $product->getPrice(),
            'unit_of_issue' => $product->unit_of_issue,
            'taxable' => $product->taxable,
            'created_year' => today()->year,
            'created_month' => today()->month,
            'created_day' => today()->day,
        ]);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_does_not_decrement_inventory_when_item_has_been_added_to_an_unconfirmed_order(): void
    {
        Event::fake([ItemWasAddedToOrder::class]);

        /** @var Order $order */
        $order = Order::factory()->create(['confirmed' => false]);

        /** @var Product $product */
        $product = Product::factory()->create(['track_inventory' => 'yes', 'inventory' => 10]);

        $result = (new AddItem)->handle($order, $product, 2);

        $this->assertInstanceOf(OrderItem::class, $result);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product->id,
            'inventory' => 10
        ]);
    }

    #[Test]
    public function it_decrements_inventory_when_item_has_been_added_to_a_confirmed_order(): void
    {
        Event::fake([ItemWasAddedToOrder::class]);

        /** @var Order $order */
        $order = Order::factory()->create(['confirmed' => true]);

        /** @var Product $product */
        $product = Product::factory()->create(['track_inventory' => 'yes', 'inventory' => 10]);

        $this->actingAsAdmin();

        $result = (new AddItem)->handle($order, $product, 2);

        $this->assertInstanceOf(OrderItem::class, $result);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product->id,
            'inventory' => 8
        ]);
    }

    #[Test]
    public function it_does_not_record_event_when_item_has_been_added_to_an_confirmed_order(): void
    {
        Event::fake([ItemWasAddedToOrder::class]);

        /** @var Order $order */
        $order = Order::factory()->create(['confirmed' => false]);

        /** @var Product $product */
        $product = Product::factory()->create();

        $result = (new AddItem)->handle($order, $product, 2);

        $this->assertInstanceOf(OrderItem::class, $result);

        $this->assertDatabaseMissing(\App\Models\Event::class, [
            'model_type' => \App\Models\Order::class,
            'model_id' => $order->id,
        ]);
    }

    #[Test]
    public function it_records_event_when_item_has_been_added_to_a_confirmed_order(): void
    {
        Carbon::setTestNow(now());

        Event::fake([ItemWasAddedToOrder::class]);

        /** @var Order $order */
        $order = Order::factory()->create(['confirmed' => true]);

        /** @var Product $product */
        $product = Product::factory()->create();

        $this->actingAsAdmin();

        $result = (new AddItem)->handle($order, $product, 2);

        $this->assertInstanceOf(OrderItem::class, $result);

        $this->assertDatabaseHas(\App\Models\Event::class, [
            'model_type' => \App\Models\Order::class,
            'model_id' => $order->id,
            'description' => "(2) {$product->title} was added to order",
            'event_id' => 'order_item_added',
            'user_id' => auth()->id(), // So we can know who did this...
            'created_at' => now(),
            'metadata' => json_encode([
                'item_id' => $result->id,
                'qty' => 2
            ])
        ]);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_issues_a_gift_card_when_adding_a_gift_card_item(): void
    {
        Event::fake([ItemWasAddedToOrder::class]);

        /** @var Order $order */
        $order = Order::factory()->create();

        $this->mock(IssueGiftCard::class, function (MockInterface $mock) use ($order) {
            $mock->shouldNotReceive('handle');
        });

        /** @var Product $product */
        $product = Product::factory()->create(['type_id' => 2]);

        $result = (new AddItem)->handle($order, $product, 2);

        $this->assertInstanceOf(OrderItem::class, $result);
    }

    #[Test]
    public function it_dispatches_the_expected_event_when_adding_an_item(): void
    {
        Event::fake([ItemWasAddedToOrder::class]);

        /** @var Order $order */
        $order = Order::factory()->create();

        /** @var Product $product */
        $product = Product::factory()->create();

        $result = (new AddItem)->handle($order, $product, 2);

        $this->assertInstanceOf(OrderItem::class, $result);

        Event::assertDispatched(ItemWasAddedToOrder::class, fn (ItemWasAddedToOrder $event) => $event->orderItem->id === $result->id);
    }
}
