<?php

namespace Tests\Unit\Actions\Order;

use App\Actions\Order\Skip;
use App\Actions\Subscription\SyncSubscriptionDatetimes;
use App\Events\Order\OrderWasCanceled;
use App\Events\Subscription\SubscriptionWasSkipped;
use App\Models\Date;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Pickup;
use App\Models\RecurringOrder;
use App\Models\RecurringOrderItem;
use App\Models\Schedule;
use App\Models\Setting;
use App\Models\User;
use Exception;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Event;
use Mockery;
use Mo<PERSON>y\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class SkipTest extends TenantTestCase
{
    #[Test]
    public function it_throws_an_exception_when_order_is_not_recurring(): void
    {
        $order = Order::factory()->create(['blueprint_id' => null]);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage("The order cannot be skipped because it is not recurring.");

        (new Skip)->handle($order, today());
    }

    #[Test]
    public function it_throws_an_exception_when_order_does_not_have_a_blueprint(): void
    {
        $order = Order::factory()->create(['is_recurring' => true, 'blueprint_id' => null]);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage("The order cannot be skipped because it is not recurring.");

        (new Skip)->handle($order, today());
    }

    #[Test]
    public function it_can_skip_an_order_when_subscription_datetimes_are_not_set(): void
    {
        Event::fake([SubscriptionWasSkipped::class, OrderWasCanceled::class]);
        Carbon::setTestNow($now = now());
        Setting::updateOrCreate(['key' => 'recurring_orders_inventory_timing'],['value' => 7]);
        Setting::updateOrCreate(['key' => 'order_deadline_hour'],['value' => 11]);

        $schedule = Schedule::factory()->create();
        $date = Date::factory()->create([
            'schedule_id' => $schedule->id,
            'pickup_date' => today()->addDays(21),
            'order_end_date' => today()->addDays(19)
        ]);
        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);

        $blueprint = RecurringOrder::factory()->create([
            'ready_at' => null,
            'generate_at' => null,
            'skip_count' => 5,
            'reorder_frequency' => 7,
            'fulfillment_id' => $pickup->id
        ]);

        $order = Order::factory()->create([
            'is_recurring' => true,
            'blueprint_id' => $blueprint->id,
            'pickup_id' => $pickup->id,
            'pickup_date' => today(),
            'customer_id' => $blueprint->customer_id
        ]);

        $blueprint->ready_at = $date->pickup_date->format('Y-m-d H:i:s');

        $this->mock(SyncSubscriptionDatetimes::class, function (MockInterface $mock) use ($blueprint, $order) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(
                    Mockery::on(fn (RecurringOrder $arg) => $arg->id === $blueprint->id),
                    Mockery::on(fn (\Carbon\Carbon $arg) => $arg->eq(today())),
                )
                ->andReturn($blueprint);
        });

        (new Skip)->handle($order, today());

        $this->assertDatabaseMissing(Order::class, [
            'id' => $order->id,
        ]);

        $this->assertDatabaseHas(RecurringOrder::class, [
           'id' => $blueprint->id,
           'skip_count' => 6,
        ]);

        $this->assertFalse($blueprint->orders()->where('id', '>', $order->id)->exists());

        $this->assertDatabaseHas(User::class, [
           'id' => $blueprint->customer_id,
           'order_skip_count' => 1
        ]);

        Event::assertDispatched(
            SubscriptionWasSkipped::class,
            fn(SubscriptionWasSkipped $event) =>
                $event->subscription->id === $blueprint->id
                && $event->old_delivery_date->format('Y-m-d H:i:s') === $order->pickup_date->format('Y-m-d H:i:s')
                && $event->new_delivery_date->format('Y-m-d H:i:s') === $date->pickup_date->format('Y-m-d H:i:s')
        );

        Event::assertNotDispatched(
            OrderWasCanceled::class,
            fn(OrderWasCanceled $event) => $event->order->id === $order->id
        );

        Carbon::setTestNow();
    }

    #[Test]
    public function it_can_skip_an_order_when_subscription_datetimes_are_set(): void
    {
        Event::fake([SubscriptionWasSkipped::class, OrderWasCanceled::class]);
        Carbon::setTestNow($now = now());
        Setting::updateOrCreate(['key' => 'recurring_orders_inventory_timing'],['value' => 7]);
        Setting::updateOrCreate(['key' => 'order_deadline_hour'],['value' => 11]);

        $schedule = Schedule::factory()->create();
        Date::factory()->create([
            'schedule_id' => $schedule->id,
            'pickup_date' => today()->addDays(21),
            'order_end_date' => today()->addDays(19)
        ]);

        $expectedDate = Date::factory()->create([
            'schedule_id' => $schedule->id,
            'pickup_date' => today()->addDays(10),
            'order_end_date' => today()->addDays(8)
        ]);

        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);

        $blueprint = RecurringOrder::factory()->create([
            'ready_at' => today()->addDays(3),
            'generate_at' => today()->addDay(),
            'skip_count' => 5,
            'reorder_frequency' => 7,
            'fulfillment_id' => $pickup->id
        ]);

        $order = Order::factory()->create([
            'is_recurring' => true,
            'blueprint_id' => $blueprint->id,
            'pickup_id' => $pickup->id,
            'pickup_date' => today()->addDays(11),
            'customer_id' => $blueprint->customer_id
        ]);

        $blueprint->ready_at = $expectedDate->pickup_date->format('Y-m-d H:i:s');

        $this->mock(SyncSubscriptionDatetimes::class, function (MockInterface $mock) use ($blueprint, $order) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(
                    Mockery::on(fn (RecurringOrder $arg) => $arg->id === $blueprint->id),
                    Mockery::on(fn (\Carbon\Carbon $arg) => $arg->eq(today())),
                )
                ->andReturn($blueprint);
        });

        (new Skip)->handle($order, today());

        $this->assertDatabaseMissing(Order::class, [
            'id' => $order->id,
        ]);

        $this->assertDatabaseHas(RecurringOrder::class, [
           'id' => $blueprint->id,
           'skip_count' => 6,
        ]);

        $this->assertFalse($blueprint->orders()->where('id', '>', $order->id)->exists());

        $this->assertDatabaseHas(User::class, [
           'id' => $blueprint->customer_id,
           'order_skip_count' => 1
        ]);

        Event::assertDispatched(
            SubscriptionWasSkipped::class,
            fn(SubscriptionWasSkipped $event) =>
                $event->subscription->id === $blueprint->id
                && $event->old_delivery_date->format('Y-m-d H:i:s') === $order->pickup_date->format('Y-m-d H:i:s')
                && $event->new_delivery_date->format('Y-m-d H:i:s') === $expectedDate->pickup_date->format('Y-m-d H:i:s')
        );

        Event::assertNotDispatched(
            OrderWasCanceled::class,
            fn(OrderWasCanceled $event) => $event->order->id === $order->id
        );

        Carbon::setTestNow();
    }

    #[Test]
    public function it_can_skip_an_order_when_subscription_week_frequency_is_set(): void
    {
        Event::fake([SubscriptionWasSkipped::class, OrderWasCanceled::class]);
        Carbon::setTestNow($now = now());
        Setting::updateOrCreate(['key' => 'recurring_orders_inventory_timing'],['value' => 7]);
        Setting::updateOrCreate(['key' => 'order_deadline_hour'],['value' => 11]);

        $schedule = Schedule::factory()->create();
        Date::factory()->create([
            'schedule_id' => $schedule->id,
            'pickup_date' => today()->addDays(21),
            'order_end_date' => today()->addDays(19)
        ]);

        $expectedDate = Date::factory()->create([
            'schedule_id' => $schedule->id,
            'pickup_date' => today()->addDays(10),
            'order_end_date' => today()->addDays(8)
        ]);

        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);

        $blueprint = RecurringOrder::factory()->create([
            'ready_at' => today()->addDays(3),
            'generate_at' => today()->addDay(),
            'skip_count' => 5,
            'reorder_frequency' => 7,
            'fulfillment_id' => $pickup->id
        ]);

        $order = Order::factory()->create([
            'is_recurring' => true,
            'blueprint_id' => $blueprint->id,
            'pickup_id' => $pickup->id,
            'pickup_date' => today()->addDays(11),
            'customer_id' => $blueprint->customer_id
        ]);

        $week_frequency = 2;

        $blueprint->ready_at = $expectedDate->pickup_date->format('Y-m-d H:i:s');

        $this->mock(SyncSubscriptionDatetimes::class, function (MockInterface $mock) use ($blueprint, $order, $week_frequency) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(
                    Mockery::on(fn (RecurringOrder $arg) => $arg->id === $blueprint->id),
                    Mockery::on(fn (\Carbon\Carbon $arg) => $arg->eq(today())),
                )
                ->andReturn($blueprint);
        });

        (new Skip)->handle($order, today());

        $this->assertDatabaseMissing(Order::class, [
            'id' => $order->id,
        ]);

        $this->assertDatabaseHas(RecurringOrder::class, [
           'id' => $blueprint->id,
           'skip_count' => 6,
        ]);

        $this->assertFalse($blueprint->orders()->where('id', '>', $order->id)->exists());

        $this->assertDatabaseHas(User::class, [
           'id' => $blueprint->customer_id,
           'order_skip_count' => 1
        ]);

        Event::assertDispatched(
            SubscriptionWasSkipped::class,
            fn(SubscriptionWasSkipped $event) =>
                $event->subscription->id === $blueprint->id
                && $event->old_delivery_date->format('Y-m-d H:i:s') === $order->pickup_date->format('Y-m-d H:i:s')
                && $event->new_delivery_date->format('Y-m-d H:i:s') === $expectedDate->pickup_date->format('Y-m-d H:i:s')
        );

        Event::assertNotDispatched(
            OrderWasCanceled::class,
            fn(OrderWasCanceled $event) => $event->order->id === $order->id
        );

        Carbon::setTestNow();
    }

    #[Test]
    public function it_can_skip_an_order_when_custom_date_is_selected(): void
    {
        Event::fake([SubscriptionWasSkipped::class, OrderWasCanceled::class]);
        Carbon::setTestNow($now = now());
        Setting::updateOrCreate(['key' => 'recurring_orders_inventory_timing'],['value' => 7]);
        Setting::updateOrCreate(['key' => 'order_deadline_hour'],['value' => 11]);

        $schedule = Schedule::factory()->create();
        Date::factory()->create([
            'schedule_id' => $schedule->id,
            'pickup_date' => today()->addDays(21),
            'order_end_date' => today()->addDays(19)
        ]);

        $expectedDate = Date::factory()->create([
            'schedule_id' => $schedule->id,
            'pickup_date' => today()->addDays(10),
            'order_end_date' => today()->addDays(8)
        ]);

        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);

        $blueprint = RecurringOrder::factory()->create([
            'ready_at' => today()->addDays(3),
            'generate_at' => today()->addDay(),
            'skip_count' => 5,
            'reorder_frequency' => 7,
            'fulfillment_id' => $pickup->id
        ]);

        $order = Order::factory()->create([
            'is_recurring' => true,
            'blueprint_id' => $blueprint->id,
            'pickup_id' => $pickup->id,
            'pickup_date' => today()->addDays(11),
            'customer_id' => $blueprint->customer_id
        ]);

        $week_frequency = 5;
        $custom_date = today()->addDays(10)->format('Y-m-d');

        $blueprint->ready_at = $expectedDate->pickup_date->format('Y-m-d H:i:s');

        $this->mock(SyncSubscriptionDatetimes::class, function (MockInterface $mock) use ($blueprint, $order, $week_frequency, $custom_date) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(
                    Mockery::on(fn (RecurringOrder $arg) => $arg->id === $blueprint->id),
                    Mockery::on(fn (\Carbon\Carbon $arg) => $arg->eq(Carbon::parse($custom_date)))
                )
                ->andReturn($blueprint);
        });

        (new Skip)->handle($order, Carbon::parse($custom_date));

        $this->assertDatabaseMissing(Order::class, [
            'id' => $order->id,
        ]);

        $this->assertDatabaseHas(RecurringOrder::class, [
           'id' => $blueprint->id,
           'skip_count' => 6,
        ]);

        $this->assertFalse($blueprint->orders()->where('id', '>', $order->id)->exists());

        $this->assertDatabaseHas(User::class, [
           'id' => $blueprint->customer_id,
           'order_skip_count' => 1
        ]);

        Event::assertDispatched(
            SubscriptionWasSkipped::class,
            fn(SubscriptionWasSkipped $event) =>
                $event->subscription->id === $blueprint->id
                && $event->old_delivery_date->format('Y-m-d H:i:s') === $order->pickup_date->format('Y-m-d H:i:s')
                && $event->new_delivery_date->format('Y-m-d H:i:s') === $expectedDate->pickup_date->format('Y-m-d H:i:s')
        );

        Event::assertNotDispatched(
            OrderWasCanceled::class,
            fn(OrderWasCanceled $event) => $event->order->id === $order->id
        );

        Carbon::setTestNow();
    }

    #[Test]
    public function it_restores_addon_order_items_to_subscription(): void
    {
        Event::fake([SubscriptionWasSkipped::class, OrderWasCanceled::class]);

        $schedule = Schedule::factory()->create();
        $expectedDate = Date::factory()->create([
            'schedule_id' => $schedule->id,
            'pickup_date' => today()->addDays(10),
            'order_end_date' => today()->addDays(8)
        ]);

        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);

        $blueprint = RecurringOrder::factory()->create([
            'skip_count' => 5,
            'fulfillment_id' => $pickup->id
        ]);

        $order = Order::factory()->create([
            'blueprint_id' => $blueprint->id,
            'pickup_id' => $pickup->id,
            'pickup_date' => today()->addDays(11),
            'customer_id' => $blueprint->customer_id
        ]);

        $standard_items = \App\Models\OrderItem::factory(2)->create([
            'order_id' => $order->id,
            'type' => 'standard'
        ]);

        $addon_items = \App\Models\OrderItem::factory(2)->create([
            'order_id' => $order->id,
            'type' => 'addon'
        ]);

        $week_frequency = 2;

        $blueprint->ready_at = $expectedDate->pickup_date->format('Y-m-d H:i:s');

        $this->mock(SyncSubscriptionDatetimes::class, function (MockInterface $mock) use ($blueprint, $order, $week_frequency) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(
                    Mockery::on(fn (RecurringOrder $arg) => $arg->id === $blueprint->id),
                    Mockery::on(fn (\Carbon\Carbon $arg) => $arg->eq(today()))
                )
                ->andReturn($blueprint);
        });

        (new Skip)->handle($order, today());

        $this->assertDatabaseMissing(Order::class, [
            'id' => $order->id,
        ]);

        $addon_items->each(function (OrderItem $item) use ($blueprint) {
            $this->assertDatabaseHas(RecurringOrderItem::class, [
                'order_id' => $blueprint->id,
                'product_id' => $item->product_id,
                'qty' => $item->qty,
                'type' => 'addon',
            ]);
        });

        $standard_items->each(function (OrderItem $item) use ($blueprint) {
            $this->assertDatabaseMissing(RecurringOrderItem::class, [
                'order_id' => $blueprint->id,
                'product_id' => $item->product_id,
            ]);
        });
    }

    #[Test]
    public function it_restores_unit_price_override_when_it_is_not_equal_to_product_unit_price(): void
    {
        Event::fake([SubscriptionWasSkipped::class, OrderWasCanceled::class]);

        $schedule = Schedule::factory()->create();
        $expectedDate = Date::factory()->create([
            'schedule_id' => $schedule->id,
            'pickup_date' => today()->addDays(10),
            'order_end_date' => today()->addDays(8)
        ]);

        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);

        $product = \App\Models\Product::factory()->create(['unit_price' => 9876]);

        $blueprint = RecurringOrder::factory()->create(['fulfillment_id' => $pickup->id]);
        $subscription_items = \App\Models\RecurringOrderItem::factory(2)->create([
            'order_id' => $blueprint->id,
            'product_id' => $product->id,
            'unit_price_override' => null
        ]);

        $order = Order::factory()->create([
            'blueprint_id' => $blueprint->id,
            'pickup_id' => $pickup->id,
            'pickup_date' => today()->addDays(11),
            'customer_id' => $blueprint->customer_id
        ]);

        $items = \App\Models\OrderItem::factory(2)->create([
            'order_id' => $order->id,
            'type' => 'standard',
            'product_id' => $product->id,
            'unit_price' => 1234
        ]);

        $week_frequency = 2;

        $blueprint->ready_at = $expectedDate->pickup_date->format('Y-m-d H:i:s');

        $this->mock(SyncSubscriptionDatetimes::class, function (MockInterface $mock) use ($blueprint, $order, $week_frequency) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(
                    Mockery::on(fn (RecurringOrder $arg) => $arg->id === $blueprint->id),
                    Mockery::on(fn (\Carbon\Carbon $arg) => $arg->eq(today()))
                )
                ->andReturn($blueprint);
        });

        (new Skip)->handle($order, today());

        $this->assertDatabaseMissing(Order::class, [
            'id' => $order->id,
        ]);

        $subscription_items->each(function (RecurringOrderItem $item) use ($blueprint) {
            $this->assertDatabaseHas(RecurringOrderItem::class, [
                'order_id' => $blueprint->id,
                'product_id' => $item->product_id,
                'qty' => $item->qty,
                'unit_price_override' => 1234,
            ]);
        });
    }
}
