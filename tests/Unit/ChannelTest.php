<?php

namespace Tests\Unit;

use App\Support\Enums\Channel;
use Illuminate\Support\Collection;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class ChannelTest extends TenantTestCase
{
    #[Test]
    public function it_returns_a_collection_of_all_channels_without_from_settings(): void
    {
        $channels = Channel::all();

        $this->assertInstanceOf(Collection::class, $channels);
        $this->assertCount(9, $channels);

        $this->assertEquals('Online Store', $channels->get(1));
        $this->assertEquals('Wholesale', $channels->get(2));
    }


    #[Test]
    public function it_returns_a_array_of_channel_ids(): void
    {
        $channels = Channel::ids();

        $this->assertIsArray($channels);
        $this->assertCount(9, $channels);

        foreach (range(1, 9) as $id) {
            $this->assertTrue(in_array($id, $channels));
        }
    }
}
