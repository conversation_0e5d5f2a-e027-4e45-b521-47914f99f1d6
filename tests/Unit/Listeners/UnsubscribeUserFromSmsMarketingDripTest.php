<?php

namespace Tests\Unit\Listeners;

use App\Events\User\UserUnsubscribedFromSmsMarketing;
use App\Integrations\Drip\Drip;
use App\Listeners\User\UserUnsubscribedFromSmsMarketing\UnsubscribeUserFromSmsMarketing;
use App\Models\Integration;
use App\Models\User;
use Exception;
use Mockery;
use Mockery\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class UnsubscribeUserFromSmsMarketingDripTest extends TenantTestCase
{
    #[Test]
    public function it_does_not_track_event_when_integration_does_not_exist(): void
    {
        $this->mock(Drip::class, function (MockInterface $mock) {
            $mock->shouldNotReceive('configure');
            $mock->shouldNotReceive('unsubscribe');
        });

        $user = User::factory()->make();

        (new UnsubscribeUserFromSmsMarketing)->handle(new UserUnsubscribedFromSmsMarketing($user));
    }

    #[Test]
    public function it_does_not_track_event_when_integration_exists_but_is_disabled(): void
    {
        Integration::factory()->create(['name' => 'drip', 'enabled' => false]);

        $this->mock(Drip::class, function (MockInterface $mock) {
            $mock->shouldNotReceive('configure');
            $mock->shouldNotReceive('unsubscribe');
        });

        $user = User::factory()->make();

        (new UnsubscribeUserFromSmsMarketing)->handle(new UserUnsubscribedFromSmsMarketing($user));
    }

    #[Test]
    public function it_tracks_event_when_integration_enabled(): void
    {
        Integration::factory()->create([
            'name' => 'drip',
            'enabled' => true,
        ]);

        $user = User::factory()->create([
            'cell_phone' => '************',
            'subscribed_to_sms_marketing_at' => now()
        ]);

        $this->mock(Drip::class, function (MockInterface $mock) use ($user) {
            $mock->shouldReceive('configure')->once()->andReturnSelf();

            $mock->shouldReceive('subscribe')
                ->once()
                ->with(
                    Mockery::on(fn(User $value) => $value->id === $user->id),
                    true
                );
        });

        (new UnsubscribeUserFromSmsMarketing)->handle(new UserUnsubscribedFromSmsMarketing($user));
    }

    #[Test]
    public function it_swallows_exception_when_tracking_the_event_throws_an_unexpected_exception(): void
    {
        Integration::factory()->create([
            'name' => 'drip',
            'enabled' => true,
        ]);

        $user = User::factory()->create([
            'cell_phone' => '************',
            'subscribed_to_sms_marketing_at' => now()
        ]);

        $this->mock(Drip::class, function (MockInterface $mock) {
            $mock->shouldReceive('configure')->once()->andReturnSelf();

            $mock->shouldReceive('subscribe')
                ->once()
                ->andThrows(new Exception('This is an unhandled exception'));
        });

        (new UnsubscribeUserFromSmsMarketing)->handle(new UserUnsubscribedFromSmsMarketing($user));
    }
}