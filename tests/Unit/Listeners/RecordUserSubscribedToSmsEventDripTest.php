<?php

namespace Tests\Unit\Listeners;

use App\Events\User\UserSubscribedToSmsMarketing;
use App\Integrations\Drip\Jobs\RecordSubscribedToSmsEvent;
use App\Listeners\User\UserSubscribedToSmsMarketing\RecordSubscribedToSmsEventInDrip;
use App\Models\User;
use Illuminate\Support\Facades\Bus;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class RecordUserSubscribedToSmsEventDripTest extends TenantTestCase
{
    #[Test]
    public function it_dispatches_the_expected_job(): void
    {
        Bus::fake([RecordSubscribedToSmsEvent::class]);

        $user = User::factory()->create();

        (new RecordSubscribedToSmsEventInDrip)->handle(new UserSubscribedToSmsMarketing($user, 'some-location'));

        Bus::assertDispatched(RecordSubscribedToSmsEvent::class, function (RecordSubscribedToSmsEvent $job) use ($user) {
            return $job->user_id === $user->id
                && $job->opt_in_location === 'some-location';
        });
    }
}