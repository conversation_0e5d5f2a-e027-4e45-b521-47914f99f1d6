<?php

namespace Tests\Unit\Listeners;

use App\Events\Order\OrderWasPaid;
use App\Listeners\Order\OrderWasPaid\IssueReferralCredit;
use App\Models\Order;
use App\Models\Setting;
use App\Models\User;
use Illuminate\Support\Facades\Event;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class IssueReferralCreditTest extends TenantTestCase
{
    #[Test]
    public function the_listener_is_registered(): void
    {
        Event::fake([OrderWasPaid::class]);

        Event::assertListening(OrderWasPaid::class, IssueReferralCredit::class);
    }

    #[Test]
    public function global_and_user_referral_credits_are_applied(): void
    {
        $referralBonusGlobal = 1000;
        Setting::updateOrCreate(['key' => 'referral_bonus'],['value'  => $referralBonusGlobal]);

        $referralBonusUser = 500;
        $referrer = User::factory()->create([
            'credit' => 0,
            'referral_bonus' => $referralBonusUser,
            'referral_bonus_earned' => 0
        ]);

        $referred = User::factory()->create(['referral_user_id' => $referrer->id]);

        $order = Order::factory()->create([
            'customer_id' => $referred->id,
            'first_time_order' => true,
            'paid' => false,
            'confirmed' => true
        ]);

        $this->assertEquals(0, $referrer->credit);

        (new IssueReferralCredit)->handle(new OrderWasPaid($order));

        $this->assertDatabaseHas('users', [
            'id' => $referrer->id,
            'credit' => $referralBonusGlobal + $referralBonusUser,
            'referral_bonus_earned' => $referralBonusGlobal + $referralBonusUser
        ]);

        $this->assertDatabaseHas('events', [
            'model_type' => \App\Models\User::class,
            'model_id' => $referrer->id,
            'description' => 'Referral bonus.',
            'event_id' => 'credit_applied',
            'user_id' => $referrer->id,
            'metadata' => json_encode([
                'amount' => $referralBonusGlobal + $referralBonusUser,
            ])
        ]);
    }
}
