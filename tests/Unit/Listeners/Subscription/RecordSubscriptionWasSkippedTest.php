<?php

namespace Tests\Unit\Listeners\Subscription;

use App\Events\Subscription\SubscriptionWasSkipped;
use App\Listeners\Subscription\RecordSubscriptionWasSkipped;
use App\Models\RecurringOrder;
use Carbon\Carbon;
use Illuminate\Support\Facades\Event;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class RecordSubscriptionWasSkippedTest extends TenantTestCase
{
    #[Test]
    public function the_listener_is_registered(): void
    {
        Event::fake([SubscriptionWasSkipped::class]);
        Event::assertListening(SubscriptionWasSkipped::class, RecordSubscriptionWasSkipped::class);
    }

    #[Test]
    public function it_records_the_expected_event(): void
    {
        Carbon::setTestNow(now());

        $subscription = RecurringOrder::factory()->create([
            'reorder_frequency' => 14,
            'ready_at' => today()->addWeeks(1)
        ]);

        event(new SubscriptionWasSkipped(
            $subscription,
            today()->addWeeks(2),
            today()->addWeeks(4))
        );

        $this->assertDatabaseHas(\App\Models\Event::class, [
            'user_id' => $subscription->customer_id,
            'model_id' => $subscription->id,
            'model_type' => RecurringOrder::class,
            'event_id' => SubscriptionWasSkipped::class,
            'description' => 'The subscription was skipped',
            'metadata' => json_encode([
                'reorder_frequency' => 14,
                'old_delivery_date' => today()->addWeeks(2)->format('Y-m-d H:i:s'),
                'new_delivery_date' => today()->addWeeks(4)->format('Y-m-d H:i:s')
            ])
        ]);

        Carbon::setTestNow();
    }
}
