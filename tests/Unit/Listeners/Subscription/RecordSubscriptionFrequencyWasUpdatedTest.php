<?php

namespace Tests\Unit\Listeners\Subscription;

use App\Events\Subscription\SubscriptionFrequencyWasUpdated;
use App\Listeners\Subscription\RecordSubscriptionFrequencyWasUpdated;
use App\Models\RecurringOrder;
use Carbon\Carbon;
use Illuminate\Support\Facades\Event;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class RecordSubscriptionFrequencyWasUpdatedTest extends TenantTestCase
{
    #[Test]
    public function the_listener_is_registered(): void
    {
        Event::fake([SubscriptionFrequencyWasUpdated::class]);
        Event::assertListening(SubscriptionFrequencyWasUpdated::class, RecordSubscriptionFrequencyWasUpdated::class);
    }

    #[Test]
    public function it_records_the_expected_event(): void
    {
        Carbon::setTestNow(now());

        $subscription = RecurringOrder::factory()->create();

        event(new SubscriptionFrequencyWasUpdated(
            $subscription,
            14,
            7
        ));

        $this->assertDatabaseHas(\App\Models\Event::class, [
            'user_id' => $subscription->customer_id,
            'model_id' => $subscription->id,
            'model_type' => RecurringOrder::class,
            'event_id' => SubscriptionFrequencyWasUpdated::class,
            'description' => 'The customer updated their subscription frequency.',
            'metadata' => json_encode([
                'old_frequency' => 14,
                'new_frequency' => 7
            ])
        ]);

        Carbon::setTestNow();
    }
}
