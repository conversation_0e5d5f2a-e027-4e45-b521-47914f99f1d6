<?php

namespace Tests\Unit\Services;

use App\Cart\Item;
use App\Cart\Offer;
use App\Contracts\Cartable;
use App\Models\Cart;
use App\Models\Pickup;
use App\Models\Product;
use App\Models\Schedule;
use App\Services\OfferService;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class OfferServiceTest extends TenantTestCase
{
    #[Test]
    public function it_include_subscriptions_offer_when_cart_is_eligible(): void
    {
        $service = app(OfferService::class);

        $schedule = Schedule::factory()->create(['type_id' => Schedule::TYPE_REPEATING, 'reorder_frequency' => ['7','14']]);
        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);
        $cart = new Cart;
        $cart->extra_attributes->set('delivery_method_id', $pickup->id);
        $cart->addItemToCart(new Item('some-id', Product::factory()->create()));

        $this->actingAsCustomer();
        $offers = $service->forCart($cart);

        $offer = $offers->first(fn (Offer $offer) => $offer->name === Offer::SUBSCRIPTION);
        $this->assertInstanceOf(Offer::class, $offer);
        $this->assertEquals(Offer::SUBSCRIPTION, $offer->name);
    }

    #[Test]
    public function it_can_complete_the_subscription_offer_with_valid_subscription(): void
    {
        $product = Product::factory()->create();

        $mock_cart = \Mockery::mock(Cartable::class);
        $mock_cart->shouldReceive('setCartAsSubscriptionPurchase')->once()
            ->with(14, $product->id)->andReturnSelf();

        $service = app(OfferService::class);

        $service->complete($mock_cart, Offer::SUBSCRIPTION, ['purchase_type' => 'recurring', 'frequency' => 14, 'product_incentive_id' => $product->id]);
    }

    #[Test]
    public function it_can_complete_the_subscription_offer_with_a_one_time_purchase(): void
    {
        $mock_cart = \Mockery::mock(Cartable::class);
        $mock_cart->shouldReceive('setCartAsOneTimePurchase')->once()
            ->withNoArgs()->andReturnSelf();

        $service = app(OfferService::class);

        $service->complete($mock_cart, Offer::SUBSCRIPTION, ['purchase_type' => 'one_time_purchase']);
    }
}
