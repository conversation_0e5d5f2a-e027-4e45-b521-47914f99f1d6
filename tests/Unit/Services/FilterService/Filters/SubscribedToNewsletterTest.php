<?php

namespace Tests\Unit\Services\FilterService\Filters;

use App\Services\FilterService\Filters\SubscribedToNewsletter;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class SubscribedToNewsletterTest extends TestCase
{
    use CreatesFakeRequest;

    #[Test]
    public function it_can_return_its_properties_for_not_newsletter(): void
    {
        $collection = (new SubscribedToNewsletter)->handle([$this->createRequest(['newsletter' => '0']), collect()], function ($passable) {
            list($request, $collection) = $passable;
            return $collection;
        });

        $this->assertTrue($collection->has('newsletter'));

        /** @var SubscribedToNewsletter $filter */
        $filter = $collection->get('newsletter');

        $this->assertEquals('Newsletter:', $filter->label());
        $this->assertEquals('Not Subscribed', $filter->value());
        $this->assertEquals('http://test.test?foo=bar', $filter->removeFilterUrl());

    }

    #[Test]
    public function it_can_return_its_properties_for_newsletter(): void
    {
        $collection = (new SubscribedToNewsletter)->handle([$this->createRequest(['newsletter' => '1']), collect()], function ($passable) {
            list($request, $collection) = $passable;
            return $collection;
        });

        $this->assertTrue($collection->has('newsletter'));

        /** @var SubscribedToNewsletter $filter */
        $filter = $collection->get('newsletter');

        $this->assertEquals('Newsletter:', $filter->label());
        $this->assertEquals('Subscribed', $filter->value());
        $this->assertEquals('http://test.test?foo=bar', $filter->removeFilterUrl());

    }
}