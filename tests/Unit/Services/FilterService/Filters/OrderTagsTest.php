<?php

namespace Tests\Unit\Services\FilterService\Filters;

use App\Models\Tag as TagModel;
use App\Services\FilterService\Filters\OrderTags;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class OrderTagsTest extends TenantTestCase
{
    use CreatesFakeRequest;

    #[Test]
    public function it_can_return_its_properties_for_one_tag(): void
    {
        $tag = TagModel::factory()->create(['type' => TagModel::type('order')]);

        $collection = (new OrderTags)->handle([$this->createRequest(['order_tags' => $tag->id]), collect()], function ($passable) {
            list($request, $collection) = $passable;
            return $collection;
        });

        $this->assertTrue($collection->has('order_tags'));

        /** @var OrderTags $filter */
        $filter = $collection->get('order_tags');

        $this->assertEquals('Order Tags:', $filter->label());
        $this->assertEquals($tag->title, $filter->value());
        $this->assertEquals('http://test.test?foo=bar', $filter->removeFilterUrl());

    }

    #[Test]
    public function it_can_return_its_properties_for_an_array_of_statuses(): void
    {
        $tags = TagModel::factory()->times(2)->create(['type' => TagModel::type('order')]);

        $collection = (new OrderTags)->handle([$this->createRequest(['order_tags' => $tags->pluck('id')->toArray()]), collect()], function ($passable) {
            list($request, $collection) = $passable;
            return $collection;
        });

        $this->assertTrue($collection->has('order_tags'));

        /** @var OrderTags $filter */
        $filter = $collection->get('order_tags');

        $this->assertEquals('Order Tags:', $filter->label());
        $this->assertEquals($tags->pluck('title')->implode(', '), $filter->value());
        $this->assertEquals('http://test.test?foo=bar', $filter->removeFilterUrl());

    }
}