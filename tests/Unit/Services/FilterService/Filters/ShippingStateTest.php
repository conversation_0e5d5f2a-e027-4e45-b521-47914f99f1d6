<?php

namespace Tests\Unit\Services\FilterService\Filters;

use App\Services\FilterService\Filters\ShippingState;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class ShippingStateTest extends TestCase
{
    use CreatesFakeRequest;

    #[Test]
    public function it_can_return_its_properties(): void
    {
        $collection = (new ShippingState)->handle([$this->createRequest(['shipping_state' => '123']), collect()], function ($passable) {
            list($request, $collection) = $passable;
            return $collection;
        });

        $this->assertTrue($collection->has('shipping_state'));

        /** @var ShippingState $filter */
        $filter = $collection->get('shipping_state');

        $this->assertEquals('State:', $filter->label());
        $this->assertEquals('123', $filter->value());
        $this->assertEquals('http://test.test?foo=bar', $filter->removeFilterUrl());

    }
}