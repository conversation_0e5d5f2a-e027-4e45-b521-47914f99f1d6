<?php

namespace Tests\Unit\Services\FilterService\Filters;

use App\Services\FilterService\Filters\Rating;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class RatingTest extends TestCase
{
    use CreatesFakeRequest;

    #[Test]
    public function it_can_return_its_properties(): void
    {
        $collection = (new Rating)->handle([$this->createRequest(['rating' => '2']), collect()], function ($passable) {
            list($request, $collection) = $passable;
            return $collection;
        });

        $this->assertTrue($collection->has('rating'));

        /** @var Rating $filter */
        $filter = $collection->get('rating');

        $this->assertEquals('Rating:', $filter->label());
        $this->assertEquals('&#9733;&#9733;&#9734;&#9734;&#9734;', $filter->value());
        $this->assertEquals('http://test.test?foo=bar', $filter->removeFilterUrl());

    }

}