<?php

namespace Tests\Unit\Services\FilterService\Filters;

use App\Services\FilterService\Filters\Paid;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class PaidTest extends TestCase
{
    use CreatesFakeRequest;

    #[Test]
    public function it_can_return_its_properties_for_not_paid(): void
    {
        $collection = (new Paid)->handle([$this->createRequest(['paid' => '0']), collect()], function ($passable) {
            list($request, $collection) = $passable;
            return $collection;
        });

        $this->assertTrue($collection->has('paid'));

        /** @var Paid $filter */
        $filter = $collection->get('paid');

        $this->assertEquals('Not Paid', $filter->label());
        $this->assertEquals('', $filter->value());
        $this->assertEquals('http://test.test?foo=bar', $filter->removeFilterUrl());

    }

    #[Test]
    public function it_can_return_its_properties_for_paid(): void
    {
        $collection = (new Paid)->handle([$this->createRequest(['paid' => '1']), collect()], function ($passable) {
            list($request, $collection) = $passable;
            return $collection;
        });

        $this->assertTrue($collection->has('paid'));

        /** @var Paid $filter */
        $filter = $collection->get('paid');

        $this->assertEquals('Paid', $filter->label());
        $this->assertEquals('', $filter->value());
        $this->assertEquals('http://test.test?foo=bar', $filter->removeFilterUrl());

    }
}