<?php

namespace Tests\Unit\Services\FilterService\Filters;

use App\Services\FilterService\Filters\LocationStatus;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class LocationStatusTest extends TestCase
{
    use CreatesFakeRequest;

    #[Test]
    public function it_can_return_its_properties_for_one_status(): void
    {
        $collection = (new LocationStatus)->handle([$this->createRequest(['location_status' => 3]), collect()], function ($passable) {
            list($request, $collection) = $passable;
            return $collection;
        });

        $this->assertTrue($collection->has('location_status'));

        /** @var LocationStatus $filter */
        $filter = $collection->get('location_status');

        $this->assertEquals('Status:', $filter->label());
        $this->assertEquals('Closed', $filter->value());
        $this->assertEquals('http://test.test?foo=bar', $filter->removeFilterUrl());

    }

    #[Test]
    public function it_can_return_its_properties_for_an_array_of_statuses(): void
    {
        $collection = (new LocationStatus)->handle([$this->createRequest(['location_status' => [1,3]]), collect()], function ($passable) {
            list($request, $collection) = $passable;
            return $collection;
        });

        $this->assertTrue($collection->has('location_status'));

        /** @var LocationStatus $filter */
        $filter = $collection->get('location_status');

        $this->assertEquals('Status:', $filter->label());
        $this->assertEquals('Open, Closed', $filter->value());
        $this->assertEquals('http://test.test?foo=bar', $filter->removeFilterUrl());

    }
}