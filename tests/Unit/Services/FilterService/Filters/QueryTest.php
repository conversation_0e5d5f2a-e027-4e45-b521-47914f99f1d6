<?php

namespace Tests\Unit\Services\FilterService\Filters;

use App\Services\FilterService\Filters\Query;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class QueryTest extends TestCase
{
    use CreatesFakeRequest;

    #[Test]
    public function it_can_return_its_properties(): void
    {
        $collection = (new Query)->handle([$this->createRequest(['q' => '123']), collect()], function ($passable) {
            list($request, $collection) = $passable;
            return $collection;
        });

        $this->assertTrue($collection->has('q'));

        /** @var Query $filter */
        $filter = $collection->get('q');

        $this->assertEquals('Search:', $filter->label());
        $this->assertEquals('123', $filter->value());
        $this->assertEquals('http://test.test?foo=bar', $filter->removeFilterUrl());
    }
}