<?php

namespace Tests\Unit\Services\FilterService\Filters;

use App\Models\Vendor as VendorModel;
use App\Services\FilterService\Filters\Vendor;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class VendorTest extends TenantTestCase
{
    use CreatesFakeRequest;

    #[Test]
    public function it_can_return_its_properties_for_one_vendor(): void
    {
        $vendor = VendorModel::factory()->create();

        $collection = (new Vendor)->handle([$this->createRequest(['vendor_id' => $vendor->id]), collect()], function ($passable) {
            list($request, $collection) = $passable;
            return $collection;
        });

        $this->assertTrue($collection->has('vendor_id'));

        /** @var Vendor $filter */
        $filter = $collection->get('vendor_id');

        $this->assertEquals('Vendor:', $filter->label());
        $this->assertEquals($vendor->title, $filter->value());
        $this->assertEquals('http://test.test?foo=bar', $filter->removeFilterUrl());

    }

    #[Test]
    public function it_can_return_its_properties_for_an_array_of_statuses(): void
    {
        $vendors = VendorModel::factory()->times(2)->create();

        $collection = (new Vendor)->handle([$this->createRequest(['vendor_id' => $vendors->pluck('id')->toArray()]), collect()], function ($passable) {
            list($request, $collection) = $passable;
            return $collection;
        });

        $this->assertTrue($collection->has('vendor_id'));

        /** @var Vendor $filter */
        $filter = $collection->get('vendor_id');

        $this->assertEquals('Vendor:', $filter->label());
        $this->assertEquals($vendors->pluck('title')->implode(', '), $filter->value());
        $this->assertEquals('http://test.test?foo=bar', $filter->removeFilterUrl());

    }
}