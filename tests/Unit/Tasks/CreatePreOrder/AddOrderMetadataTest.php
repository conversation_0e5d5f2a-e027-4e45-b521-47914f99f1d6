<?php

namespace Tests\Unit\Tasks\CreatePreOrder;

use App\Models\Card;
use App\Models\Order;
use App\Models\Pickup;
use App\Models\Product;
use App\Models\User;
use App\Support\Enums\OrderStatus;
use App\Tasks\CreatePreOrder\AddOrderMetadata;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class AddOrderMetadataTest extends TenantTestCase
{
    #[Test]
    public function it_adds_order_metadata_when_user_does_not_have_a_pickup(): void
    {
        $pickup = Pickup::factory()->create(['schedule_id' => null]);
        $user = User::factory()->create(['pickup_point' => 0]);
        $card = Card::factory()->create(['user_id' => $user->id]);
        $order = Order::factory()->create();
        $product = Product::factory()->create(['fulfillment_id' => $pickup->id]);

        $order = (new AddOrderMetadata)->handle($user, $order, $product, collect(['payment_id' => 1, 'payment_source_id' => $card->source_id]));

        $this->assertInstanceOf(Order::class, $order);

        $this->assertEquals(1, $order->type_id);
        $this->assertEquals(1, $order->payment_id);
        $this->assertEquals($card->id, $order->payment_source_id);
        $this->assertEquals(0, $order->schedule_id);
        $this->assertEquals($pickup->id, $order->pickup_id);
        $this->assertNull($order->deadline_date);
        $this->assertNull($order->pickup_date);
        $this->assertNull($order->original_pickup_date);
        $this->assertEquals($user->id, $order->customer_id);
        $this->assertEquals($user->first_name, $order->customer_first_name);
        $this->assertEquals($user->last_name, $order->customer_last_name);
        $this->assertEquals($user->phone, $order->customer_phone);
        $this->assertEquals($user->email, $order->customer_email);
        $this->assertEquals($user->street, $order->shipping_street);
        $this->assertEquals($user->street_2, $order->shipping_street_2);
        $this->assertEquals($user->city, $order->shipping_city);
        $this->assertEquals($user->state, $order->shipping_state);
        $this->assertEquals($user->zip, $order->shipping_zip);
        $this->assertEquals($user->billing_street, $order->billing_street);
        $this->assertEquals($user->billing_street_2, $order->billing_street_2);
        $this->assertEquals($user->billing_city, $order->billing_city);
        $this->assertEquals($user->billing_state, $order->billing_state);
        $this->assertEquals($user->billing_zip, $order->billing_zip);
        $this->assertEquals(1, $order->first_time_order);
        $this->assertFalse($order->confirmed);
        $this->assertFalse($order->packed);
        $this->assertFalse($order->paid);
        $this->assertEquals(OrderStatus::preOrder(), $order->status_id);
    }
}
