<?php

namespace Tests\Unit\Models;

use App\Models\OrderItem;
use App\Models\Product;
use App\Models\Tag;
use App\Support\Enums\ProductType;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class OrderItemTest extends TenantTestCase
{
    #[Test]
    public function it_can_get_the_fulfilled_quantity(): void
    {
        $item = OrderItem::factory()->make([
            'qty' => 3,
            'fulfilled_qty' => 2,
            'stock_status' => 'full'
        ]);

        $this->assertEquals(3, $item->fulfilledQuantity());

        $item = OrderItem::factory()->make([
            'qty' => 3,
            'fulfilled_qty' => 2,
            'stock_status' => 'short'
        ]);

        $this->assertEquals(2, $item->fulfilledQuantity());

        $item = OrderItem::factory()->make([
            'qty' => 3,
            'fulfilled_qty' => 2,
            'stock_status' => 'out'
        ]);

        $this->assertEquals(2, $item->fulfilledQuantity());

        $item = OrderItem::factory()->make([
            'qty' => 3,
            'fulfilled_qty' => 2,
            'stock_status' => 'other'
        ]);

        $this->assertEquals(3, $item->fulfilledQuantity());
    }

    #[Test]
    public function it_can_release_product_inventory(): void
    {
        $product = Product::factory()->create(['inventory' => 2]);
        $item = OrderItem::factory()->make([
            'product_id' => $product->id,
            'qty' => 3,
            'fulfilled_qty' => 2,
            'stock_status' => 'full'
        ]);

        $item->releaseProductInventory();

        $this->assertDatabaseHas(Product::class, [
            'id' => $product->id,
            'inventory' => 5
        ]);

        $product = Product::factory()->create(['inventory' => 2]);
        $item = OrderItem::factory()->make([
            'product_id' => $product->id,
            'qty' => 3,
            'fulfilled_qty' => 2,
            'stock_status' => 'short'
        ]);

        $item->releaseProductInventory();

        $this->assertDatabaseHas(Product::class, [
            'id' => $product->id,
            'inventory' => 4
        ]);

        $product = Product::factory()->create(['inventory' => 2]);
        $item = OrderItem::factory()->make([
            'product_id' => $product->id,
            'qty' => 3,
            'fulfilled_qty' => 2,
            'stock_status' => 'out'
        ]);

        $item->releaseProductInventory();

        $this->assertDatabaseHas(Product::class, [
            'id' => $product->id,
            'inventory' => 4
        ]);

        $product = Product::factory()->create(['inventory' => 2]);
        $item = OrderItem::factory()->make([
            'product_id' => $product->id,
            'qty' => 3,
            'fulfilled_qty' => 2,
            'stock_status' => 'other'
        ]);

        $item->releaseProductInventory();

        $this->assertDatabaseHas(Product::class, [
            'id' => $product->id,
            'inventory' => 5
        ]);
    }

    #[Test]
    public function it_can_release_bundle_product_inventory(): void
    {
        $product_one = Product::factory()->create(['inventory' => 2]);
        $product_two = Product::factory()->create(['inventory' => 3]);
        $bundle_product = Product::factory()->create(['inventory' => 3, 'track_inventory' => 'bundle', 'is_bundle' => true]);

        $bundle_product->bundle()->attach($product_one->id, ['qty' => 2]);
        $bundle_product->bundle()->attach($product_two->id, ['qty' => 3]);

        $item = OrderItem::factory()->make([
            'product_id' => $bundle_product->id,
            'qty' => 3,
            'fulfilled_qty' => 2,
            'stock_status' => 'full'
        ]);

        $item->releaseProductInventory();

        $this->assertDatabaseHas(Product::class, [
            'id' => $product_one->id,
            'inventory' => 8 // 2 + (3*2)
        ]);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product_two->id,
            'inventory' => 12 // 3 + (3*3)
        ]);

        $this->assertDatabaseHas(Product::class, [
            'id' => $bundle_product->id,
            'inventory' => 3
        ]);

        $product_one = Product::factory()->create(['inventory' => 2]);
        $product_two = Product::factory()->create(['inventory' => 3]);
        $bundle_product = Product::factory()->create(['inventory' => 3, 'track_inventory' => 'bundle', 'is_bundle' => true]);

        $bundle_product->bundle()->attach($product_one->id, ['qty' => 2]);
        $bundle_product->bundle()->attach($product_two->id, ['qty' => 3]);

        $item = OrderItem::factory()->make([
            'product_id' => $bundle_product->id,
            'qty' => 3,
            'fulfilled_qty' => 2,
            'stock_status' => 'short'
        ]);

        $item->releaseProductInventory();

        $this->assertDatabaseHas(Product::class, [
            'id' => $product_one->id,
            'inventory' => 6 // 2 + (2*2)
        ]);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product_two->id,
            'inventory' => 9 // 3 + (2*3)
        ]);

        $this->assertDatabaseHas(Product::class, [
            'id' => $bundle_product->id,
            'inventory' => 3
        ]);

        $product_one = Product::factory()->create(['inventory' => 2]);
        $product_two = Product::factory()->create(['inventory' => 3]);
        $bundle_product = Product::factory()->create(['inventory' => 3, 'track_inventory' => 'bundle', 'is_bundle' => true]);

        $bundle_product->bundle()->attach($product_one->id, ['qty' => 2]);
        $bundle_product->bundle()->attach($product_two->id, ['qty' => 3]);

        $item = OrderItem::factory()->make([
            'product_id' => $bundle_product->id,
            'qty' => 3,
            'fulfilled_qty' => 2,
            'stock_status' => 'out'
        ]);

        $item->releaseProductInventory();

        $this->assertDatabaseHas(Product::class, [
            'id' => $product_one->id,
            'inventory' => 6 // 2 + (2*2)
        ]);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product_two->id,
            'inventory' => 9 // 3 + (2*3)
        ]);

        $this->assertDatabaseHas(Product::class, [
            'id' => $bundle_product->id,
            'inventory' => 3
        ]);
    }

    #[Test]
    public function it_can_consume_product_inventory(): void
    {
        $product = Product::factory()->create(['inventory' => 8]);
        $item = OrderItem::factory()->make([
            'product_id' => $product->id,
            'qty' => 3,
            'fulfilled_qty' => 2,
            'stock_status' => 'full'
        ]);

        $item->consumeProductInventory();

        $this->assertDatabaseHas(Product::class, [
            'id' => $product->id,
            'inventory' => 5
        ]);

        $product = Product::factory()->create(['inventory' => 8]);
        $item = OrderItem::factory()->make([
            'product_id' => $product->id,
            'qty' => 3,
            'fulfilled_qty' => 2,
            'stock_status' => 'short'
        ]);

        $item->consumeProductInventory();

        $this->assertDatabaseHas(Product::class, [
            'id' => $product->id,
            'inventory' => 6
        ]);

        $product = Product::factory()->create(['inventory' => 8]);
        $item = OrderItem::factory()->make([
            'product_id' => $product->id,
            'qty' => 3,
            'fulfilled_qty' => 2,
            'stock_status' => 'out'
        ]);

        $item->consumeProductInventory();

        $this->assertDatabaseHas(Product::class, [
            'id' => $product->id,
            'inventory' => 6
        ]);

        $product = Product::factory()->create(['inventory' => 8]);
        $item = OrderItem::factory()->make([
            'product_id' => $product->id,
            'qty' => 3,
            'fulfilled_qty' => 2,
            'stock_status' => 'other'
        ]);

        $item->consumeProductInventory();

        $this->assertDatabaseHas(Product::class, [
            'id' => $product->id,
            'inventory' => 5
        ]);
    }

    #[Test]
    public function it_can_consume_bundle_product_inventory(): void
    {
        $product_one = Product::factory()->create(['inventory' => 20]);
        $product_two = Product::factory()->create(['inventory' => 30]);
        $bundle_product = Product::factory()->create(['inventory' => 3, 'track_inventory' => 'bundle', 'is_bundle' => true]);

        $bundle_product->bundle()->attach($product_one->id, ['qty' => 2]);
        $bundle_product->bundle()->attach($product_two->id, ['qty' => 3]);

        $item = OrderItem::factory()->make([
            'product_id' => $bundle_product->id,
            'qty' => 3,
            'fulfilled_qty' => 2,
            'stock_status' => 'full'
        ]);

        $item->consumeProductInventory();

        $this->assertDatabaseHas(Product::class, [
            'id' => $product_one->id,
            'inventory' => 14 // 20 - (3*2)
        ]);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product_two->id,
            'inventory' => 21 // 30 + (3*3)
        ]);

        $this->assertDatabaseHas(Product::class, [
            'id' => $bundle_product->id,
            'inventory' => 3
        ]);

        $product_one = Product::factory()->create(['inventory' => 20]);
        $product_two = Product::factory()->create(['inventory' => 30]);
        $bundle_product = Product::factory()->create(['inventory' => 3, 'track_inventory' => 'bundle', 'is_bundle' => true]);

        $bundle_product->bundle()->attach($product_one->id, ['qty' => 2]);
        $bundle_product->bundle()->attach($product_two->id, ['qty' => 3]);

        $item = OrderItem::factory()->make([
            'product_id' => $bundle_product->id,
            'qty' => 3,
            'fulfilled_qty' => 2,
            'stock_status' => 'short'
        ]);

        $item->consumeProductInventory();

        $this->assertDatabaseHas(Product::class, [
            'id' => $product_one->id,
            'inventory' => 16 // 20 - (2*2)
        ]);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product_two->id,
            'inventory' => 24 // 30 - (2*3)
        ]);

        $this->assertDatabaseHas(Product::class, [
            'id' => $bundle_product->id,
            'inventory' => 3
        ]);

        $product_one = Product::factory()->create(['inventory' => 20]);
        $product_two = Product::factory()->create(['inventory' => 30]);
        $bundle_product = Product::factory()->create(['inventory' => 3, 'track_inventory' => 'bundle', 'is_bundle' => true]);

        $bundle_product->bundle()->attach($product_one->id, ['qty' => 2]);
        $bundle_product->bundle()->attach($product_two->id, ['qty' => 3]);

        $item = OrderItem::factory()->make([
            'product_id' => $bundle_product->id,
            'qty' => 3,
            'fulfilled_qty' => 2,
            'stock_status' => 'out'
        ]);

        $item->consumeProductInventory();

        $this->assertDatabaseHas(Product::class, [
            'id' => $product_one->id,
            'inventory' => 16 // 20 - (2*2)
        ]);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product_two->id,
            'inventory' => 24 // 30 - (2*3)
        ]);

        $this->assertDatabaseHas(Product::class, [
            'id' => $bundle_product->id,
            'inventory' => 3
        ]);
    }

    #[Test]
    public function it_knows_if_its_a_promo_item(): void
    {
        $this->assertTrue(OrderItem::factory()->make(['type' => 'promo'])->isPromoItem());
        $this->assertFalse(OrderItem::factory()->make(['type' => 'standard'])->isPromoItem());
    }

    #[Test]
    public function it_can_be_converted_to_a_cart_item(): void
    {
        $product = Product::factory()->create(['unit_price' => '12.34']);
        /** @var OrderItem $item */
        $item = OrderItem::factory()->create(['qty' => 3, 'product_id' => $product->id]);

        $cart_item = $item->toCartItem();

        $this->assertEquals($item->id, $cart_item->id);
        $this->assertEquals($item->product->id, $cart_item->product->id);
        $this->assertEquals(1234, $cart_item->price());
        $this->assertEquals(3, $cart_item->quantity);
    }

    #[Test]
    public function it_can_fulfill_full_product_inventory(): void
    {
        $product = Product::factory()->create(['inventory' => 4, 'track_inventory' => 'yes']);

        $item = OrderItem::factory()->make([
            'product_id' => $product->id,
            'qty' => 3,
            'fulfilled_qty' => 0,
            'stock_status' => 'out'
        ]);

        $item->fulfillInventory();

        $this->assertDatabaseHas(Product::class, [
            'id' => $product->id,
            'inventory' => 1
        ]);

        $this->assertDatabaseHas(OrderItem::class, [
            'id' => $item->id,
            'qty' => 3,
            'fulfilled_qty' => 3,
            'stock_status' => 'full'
        ]);

        $this->assertFalse($item->order->tags()->where('title', Tag::INVENTORY_ERROR)->exists());
    }

    #[Test]
    public function it_can_fulfill_short_product_inventory(): void
    {
        $product = Product::factory()->create(['inventory' => 2, 'track_inventory' => 'yes']);

        $item = OrderItem::factory()->make([
            'product_id' => $product->id,
            'qty' => 3,
            'fulfilled_qty' => 0,
            'stock_status' => 'out'
        ]);

        $item->fulfillInventory();

        $this->assertDatabaseHas(Product::class, [
            'id' => $product->id,
            'inventory' => 0
        ]);

        $this->assertDatabaseHas(OrderItem::class, [
            'id' => $item->id,
            'qty' => 3,
            'fulfilled_qty' => 2,
            'stock_status' => 'short'
        ]);

        $this->assertTrue($item->order->tags()->where('title', Tag::INVENTORY_ERROR)->exists());
    }

    #[Test]
    public function it_can_fulfill_out_of_stock_product_inventory(): void
    {
        $product = Product::factory()->create(['inventory' => 0, 'track_inventory' => 'yes']);

        $item = OrderItem::factory()->make([
            'product_id' => $product->id,
            'qty' => 3,
            'fulfilled_qty' => 0,
            'stock_status' => 'short'
        ]);

        $item->fulfillInventory();

        $this->assertDatabaseHas(Product::class, [
            'id' => $product->id,
            'inventory' => 0
        ]);

        $this->assertDatabaseHas(OrderItem::class, [
            'id' => $item->id,
            'qty' => 3,
            'fulfilled_qty' => 0,
            'stock_status' => 'out'
        ]);

        $this->assertTrue($item->order->tags()->where('title', Tag::INVENTORY_ERROR)->exists());
    }

    #[Test]
    public function it_can_fulfill_untracked_product_inventory(): void
    {
        $product = Product::factory()->create(['inventory' => 0, 'track_inventory' => 'no']);

        $item = OrderItem::factory()->make([
            'product_id' => $product->id,
            'qty' => 3,
            'fulfilled_qty' => 0,
            'stock_status' => 'out'
        ]);

        $item->fulfillInventory();

        $this->assertDatabaseHas(Product::class, [
            'id' => $product->id,
            'inventory' => 0
        ]);

        $this->assertDatabaseHas(OrderItem::class, [
            'id' => $item->id,
            'qty' => 3,
            'fulfilled_qty' => 3,
            'stock_status' => 'full'
        ]);

        $this->assertFalse($item->order->tags()->where('title', Tag::INVENTORY_ERROR)->exists());
    }

    #[Test]
    public function it_can_fulfill_full_bundle_product_inventory(): void
    {
        $bundle = Product::factory()->create(['inventory' => 8, 'track_inventory' => 'bundle', 'is_bundle' => true]);
        $product_one = Product::factory()->create(['inventory' => 8, 'track_inventory' => 'yes']);
        $bundle->bundle()->attach($product_one->id, ['qty' => 2]);

        $product_two = Product::factory()->create(['inventory' => 6, 'track_inventory' => 'yes']);
        $bundle->bundle()->attach($product_two->id, ['qty' => 1]);

        $product_three = Product::factory()->create(['inventory' => 6, 'track_inventory' => 'no']);
        $bundle->bundle()->attach($product_three->id, ['qty' => 1]);

        $item = OrderItem::factory()->make([
            'product_id' => $bundle->id,
            'qty' => 3,
            'fulfilled_qty' => 0,
            'stock_status' => 'out'
        ]);

        $item->fulfillInventory();

        $this->assertDatabaseHas(Product::class, [
            'id' => $bundle->id,
            'inventory' => 8
        ]);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product_one->id,
            'inventory' => 2 // 8 - (3*2)
        ]);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product_two->id,
            'inventory' => 3 // 6 - (3*1)
        ]);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product_three->id,
            'inventory' => 6 // does not track
        ]);

        $this->assertDatabaseHas(OrderItem::class, [
            'id' => $item->id,
            'qty' => 3,
            'fulfilled_qty' => 3,
            'stock_status' => 'full'
        ]);

        $this->assertFalse($item->order->tags()->where('title', Tag::INVENTORY_ERROR)->exists());
    }

    #[Test]
    public function it_can_fulfill_short_stock_bundle_product_inventory(): void
    {
        $bundle = Product::factory()->create(['inventory' => 8, 'track_inventory' => 'bundle', 'is_bundle' => true]);
        $product_one = Product::factory()->create(['inventory' => 3, 'track_inventory' => 'yes']);
        $bundle->bundle()->attach($product_one->id, ['qty' => 2]);

        $product_two = Product::factory()->create(['inventory' => 6, 'track_inventory' => 'yes']);
        $bundle->bundle()->attach($product_two->id, ['qty' => 1]);

        $product_three = Product::factory()->create(['inventory' => 6, 'track_inventory' => 'no']);
        $bundle->bundle()->attach($product_three->id, ['qty' => 1]);

        $item = OrderItem::factory()->make([
            'product_id' => $bundle->id,
            'qty' => 3,
            'fulfilled_qty' => 0,
            'stock_status' => 'out'
        ]);

        $item->fulfillInventory();

        $this->assertDatabaseHas(Product::class, [
            'id' => $bundle->id,
            'inventory' => 8
        ]);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product_one->id,
            'inventory' => 1 // 3 - (1*2)
        ]);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product_two->id,
            'inventory' => 5 // 6 - (1*1)
        ]);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product_three->id,
            'inventory' => 6 // does not track
        ]);

        $this->assertDatabaseHas(OrderItem::class, [
            'id' => $item->id,
            'qty' => 3,
            'fulfilled_qty' => 1,
            'stock_status' => 'short'
        ]);

        $this->assertTrue($item->order->tags()->where('title', Tag::INVENTORY_ERROR)->exists());
    }

    #[Test]
    public function it_can_fulfill_out_of_stock_bundle_product_inventory(): void
    {
        $bundle = Product::factory()->create(['inventory' => 8, 'track_inventory' => 'bundle', 'is_bundle' => true]);
        $product_one = Product::factory()->create(['inventory' => 1, 'track_inventory' => 'yes']);
        $bundle->bundle()->attach($product_one->id, ['qty' => 2]);

        $product_two = Product::factory()->create(['inventory' => 6, 'track_inventory' => 'yes']);
        $bundle->bundle()->attach($product_two->id, ['qty' => 1]);

        $product_three = Product::factory()->create(['inventory' => 6, 'track_inventory' => 'no']);
        $bundle->bundle()->attach($product_three->id, ['qty' => 1]);

        $item = OrderItem::factory()->make([
            'product_id' => $bundle->id,
            'qty' => 3,
            'fulfilled_qty' => 0,
            'stock_status' => 'short'
        ]);

        $item->fulfillInventory();

        $this->assertDatabaseHas(Product::class, [
            'id' => $bundle->id,
            'inventory' => 8
        ]);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product_one->id,
            'inventory' => 1 // 1 - (0*2)
        ]);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product_two->id,
            'inventory' => 6 // 6 - (0*1)
        ]);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product_three->id,
            'inventory' => 6 // does not track
        ]);

        $this->assertDatabaseHas(OrderItem::class, [
            'id' => $item->id,
            'qty' => 3,
            'fulfilled_qty' => 0,
            'stock_status' => 'out'
        ]);

        $this->assertTrue($item->order->tags()->where('title', Tag::INVENTORY_ERROR)->exists());
    }

    #[Test]
    public function it_is_not_eligible_for_subscription_savings_when_product_is_gift_card(): void
    {
        $product = Product::factory()->create(['type_id' => ProductType::GIFT_CARD->value]);
        $item = OrderItem::factory()->create(['product_id' => $product->id]);

        $this->assertFalse($item->isEligibleForSubscriptionSavings());
    }

    #[Test]
    public function it_can_determine_if_it_is_fulfilled_virtually(): void
    {
        $product = Product::factory()->create();
        $item = OrderItem::factory()->create(['product_id' => $product->id]);

        $this->assertTrue($item->isFulfilledVirtually());

        $product = Product::factory()->create(['settings' => ['fulfilment_method' => 'virtual']]);
        $item = OrderItem::factory()->create(['product_id' => $product->id]);

        $this->assertTrue($item->isFulfilledVirtually());

        $product = Product::factory()->create(['settings' => ['fulfilment_method' => 'physical']]);
        $item = OrderItem::factory()->create(['product_id' => $product->id]);

        $this->assertFalse($item->isFulfilledVirtually());
    }

    #[Test]
    public function it_can_fulfill_inventory_without_threshold_check(): void
    {
        $product = Product::factory()->create(['inventory' => 8, 'oos_threshold_inventory' => 8]);
        $item = OrderItem::factory()->make([
            'product_id' => $product->id,
            'qty' => 3,
            'fulfilled_qty' => 0,
            'stock_status' => 'other'
        ]);

        $item->fulfillInventoryWithoutThresholdCheck();

        $this->assertDatabaseHas(OrderItem::class, [
            'id' => $item->id,
            'qty' => 3,
            'fulfilled_qty' => 3,
            'stock_status' => 'full'
        ]);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product->id,
            'inventory' => 5,
        ]);

        $product = Product::factory()->create(['inventory' => 8, 'oos_threshold_inventory' => 8]);
        $item = OrderItem::factory()->make([
            'product_id' => $product->id,
            'qty' => 10,
            'fulfilled_qty' => 0,
            'stock_status' => 'other'
        ]);

        $item->fulfillInventoryWithoutThresholdCheck();

        $this->assertDatabaseHas(OrderItem::class, [
            'id' => $item->id,
            'qty' => 10,
            'fulfilled_qty' => 8,
            'stock_status' => 'short'
        ]);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product->id,
            'inventory' => 0
        ]);

        $product = Product::factory()->create(['inventory' => 0, 'oos_threshold_inventory' => 0]);
        $item = OrderItem::factory()->make([
            'product_id' => $product->id,
            'qty' => 3,
            'fulfilled_qty' => 1,
            'stock_status' => 'other'
        ]);

        $item->fulfillInventoryWithoutThresholdCheck();

        $this->assertDatabaseHas(OrderItem::class, [
            'id' => $item->id,
            'qty' => 3,
            'fulfilled_qty' => 0,
            'stock_status' => 'out'
        ]);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product->id,
            'inventory' => 0
        ]);
    }

    #[Test]
    public function it_can_fulfill_bundle_inventory_without_threshold_check(): void
    {
        $product_one = Product::factory()->create(['inventory' => 20, 'oos_threshold_inventory' => 20]);
        $product_two = Product::factory()->create(['inventory' => 30, 'oos_threshold_inventory' => 30]);
        $bundle_product = Product::factory()->create(['inventory' => 3, 'track_inventory' => 'bundle', 'is_bundle' => true]);

        $bundle_product->bundle()->attach($product_one->id, ['qty' => 2]);
        $bundle_product->bundle()->attach($product_two->id, ['qty' => 3]);

        $item = OrderItem::factory()->make([
            'product_id' => $bundle_product->id,
            'qty' => 3,
            'fulfilled_qty' => 2,
            'stock_status' => 'other'
        ]);

        $item->fulfillInventoryWithoutThresholdCheck();

        $this->assertDatabaseHas(OrderItem::class, [
            'id' => $item->id,
            'qty' => 3,
            'fulfilled_qty' => 3,
            'stock_status' => 'full'
        ]);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product_one->id,
            'inventory' => 14 // 20 - (3*2)
        ]);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product_two->id,
            'inventory' => 21 // 30 + (3*3)
        ]);

        $this->assertDatabaseHas(Product::class, [
            'id' => $bundle_product->id,
            'inventory' => 3
        ]);

        $product_one = Product::factory()->create(['inventory' => 20, 'oos_threshold_inventory' => 20]);
        $product_two = Product::factory()->create(['inventory' => 7, 'oos_threshold_inventory' => 7]);
        $bundle_product = Product::factory()->create(['inventory' => 3, 'track_inventory' => 'bundle', 'is_bundle' => true]);

        $bundle_product->bundle()->attach($product_one->id, ['qty' => 2]);
        $bundle_product->bundle()->attach($product_two->id, ['qty' => 3]);

        $item = OrderItem::factory()->make([
            'product_id' => $bundle_product->id,
            'qty' => 3,
            'fulfilled_qty' => 0,
            'stock_status' => 'other'
        ]);

        $item->fulfillInventoryWithoutThresholdCheck();

        $this->assertDatabaseHas(OrderItem::class, [
            'id' => $item->id,
            'qty' => 3,
            'fulfilled_qty' => 2, // only enough for 2 of product_two
            'stock_status' => 'short'
        ]);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product_one->id,
            'inventory' => 16 // 20 - (2*2)
        ]);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product_two->id,
            'inventory' => 1 // 7 - (2*2)
        ]);

        $this->assertDatabaseHas(Product::class, [
            'id' => $bundle_product->id,
            'inventory' => 3
        ]);

        $product_one = Product::factory()->create(['inventory' => 0, 'oos_threshold_inventory' => 0]);
        $product_two = Product::factory()->create(['inventory' => 30, 'oos_threshold_inventory' => 30]);
        $bundle_product = Product::factory()->create(['inventory' => 3, 'track_inventory' => 'bundle', 'is_bundle' => true]);

        $bundle_product->bundle()->attach($product_one->id, ['qty' => 2]);
        $bundle_product->bundle()->attach($product_two->id, ['qty' => 3]);

        $item = OrderItem::factory()->make([
            'product_id' => $bundle_product->id,
            'qty' => 3,
            'fulfilled_qty' => 1,
            'stock_status' => 'other'
        ]);

        $item->fulfillInventoryWithoutThresholdCheck();

        $this->assertDatabaseHas(OrderItem::class, [
            'id' => $item->id,
            'qty' => 3,
            'fulfilled_qty' => 0, // product_one out of stock
            'stock_status' => 'out'
        ]);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product_one->id,
            'inventory' => 0
        ]);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product_two->id,
            'inventory' => 30
        ]);

        $this->assertDatabaseHas(Product::class, [
            'id' => $bundle_product->id,
            'inventory' => 3
        ]);
    }

    #[Test]
    public function it_can_fulfill_inventory_with_threshold_check(): void
    {
        $product = Product::factory()->create(['inventory' => 8, 'oos_threshold_inventory' => 2]);
        $item = OrderItem::factory()->make([
            'product_id' => $product->id,
            'qty' => 3,
            'fulfilled_qty' => 0,
            'stock_status' => 'other'
        ]);

        $item->fulfillInventory();

        $this->assertDatabaseHas(OrderItem::class, [
            'id' => $item->id,
            'qty' => 3,
            'fulfilled_qty' => 3,
            'stock_status' => 'full'
        ]);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product->id,
            'inventory' => 5,
        ]);

        $product = Product::factory()->create(['inventory' => 8, 'oos_threshold_inventory' => 5]);
        $item = OrderItem::factory()->make([
            'product_id' => $product->id,
            'qty' => 6,
            'fulfilled_qty' => 0,
            'stock_status' => 'other'
        ]);

        $item->fulfillInventory();

        $this->assertDatabaseHas(OrderItem::class, [
            'id' => $item->id,
            'qty' => 6,
            'fulfilled_qty' => 3, // only enough for 3 above threshold
            'stock_status' => 'short'
        ]);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product->id,
            'inventory' => 5
        ]);

        $product = Product::factory()->create(['inventory' => 3, 'oos_threshold_inventory' => 3]);
        $item = OrderItem::factory()->make([
            'product_id' => $product->id,
            'qty' => 3,
            'fulfilled_qty' => 1,
            'stock_status' => 'other'
        ]);

        $item->fulfillInventory();

        $this->assertDatabaseHas(OrderItem::class, [
            'id' => $item->id,
            'qty' => 3,
            'fulfilled_qty' => 0, // already at threshold
            'stock_status' => 'out'
        ]);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product->id,
            'inventory' => 3
        ]);
    }

    #[Test]
    public function it_can_fulfill_bundle_inventory_with_threshold_check(): void
    {
        $product_one = Product::factory()->create(['inventory' => 20, 'oos_threshold_inventory' => 2]);
        $product_two = Product::factory()->create(['inventory' => 30, 'oos_threshold_inventory' => 3]);
        $bundle_product = Product::factory()->create(['inventory' => 3, 'track_inventory' => 'bundle', 'is_bundle' => true]);

        $bundle_product->bundle()->attach($product_one->id, ['qty' => 2]);
        $bundle_product->bundle()->attach($product_two->id, ['qty' => 3]);

        $item = OrderItem::factory()->make([
            'product_id' => $bundle_product->id,
            'qty' => 3,
            'fulfilled_qty' => 2,
            'stock_status' => 'other'
        ]);

        $item->fulfillInventory();

        $this->assertDatabaseHas(OrderItem::class, [
            'id' => $item->id,
            'qty' => 3,
            'fulfilled_qty' => 3,
            'stock_status' => 'full'
        ]);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product_one->id,
            'inventory' => 14 // 20 - (3*2)
        ]);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product_two->id,
            'inventory' => 21 // 30 + (3*3)
        ]);

        $this->assertDatabaseHas(Product::class, [
            'id' => $bundle_product->id,
            'inventory' => 3
        ]);

        $product_one = Product::factory()->create(['inventory' => 20, 'oos_threshold_inventory' => 2]);
        $product_two = Product::factory()->create(['inventory' => 7, 'oos_threshold_inventory' => 4]);
        $bundle_product = Product::factory()->create(['inventory' => 3, 'track_inventory' => 'bundle', 'is_bundle' => true]);

        $bundle_product->bundle()->attach($product_one->id, ['qty' => 2]);
        $bundle_product->bundle()->attach($product_two->id, ['qty' => 3]);

        $item = OrderItem::factory()->make([
            'product_id' => $bundle_product->id,
            'qty' => 3,
            'fulfilled_qty' => 0,
            'stock_status' => 'other'
        ]);

        $item->fulfillInventory();

        $this->assertDatabaseHas(OrderItem::class, [
            'id' => $item->id,
            'qty' => 3,
            'fulfilled_qty' => 1, // only enough for 1 of product_two
            'stock_status' => 'short'
        ]);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product_one->id,
            'inventory' => 18 // 20 - (2*1)
        ]);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product_two->id,
            'inventory' => 4 // 7 - (3*1)
        ]);

        $this->assertDatabaseHas(Product::class, [
            'id' => $bundle_product->id,
            'inventory' => 3
        ]);

        $product_one = Product::factory()->create(['inventory' => 20, 'oos_threshold_inventory' => 20]);
        $product_two = Product::factory()->create(['inventory' => 30, 'oos_threshold_inventory' => 2]);
        $bundle_product = Product::factory()->create(['inventory' => 3, 'track_inventory' => 'bundle', 'is_bundle' => true]);

        $bundle_product->bundle()->attach($product_one->id, ['qty' => 2]);
        $bundle_product->bundle()->attach($product_two->id, ['qty' => 3]);

        $item = OrderItem::factory()->make([
            'product_id' => $bundle_product->id,
            'qty' => 3,
            'fulfilled_qty' => 1,
            'stock_status' => 'other'
        ]);

        $item->fulfillInventory();

        $this->assertDatabaseHas(OrderItem::class, [
            'id' => $item->id,
            'qty' => 3,
            'fulfilled_qty' => 0, // product_one at threshold
            'stock_status' => 'out'
        ]);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product_one->id,
            'inventory' => 20
        ]);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product_two->id,
            'inventory' => 30
        ]);

        $this->assertDatabaseHas(Product::class, [
            'id' => $bundle_product->id,
            'inventory' => 3
        ]);
    }

    #[Test]
    public function it_knows_if_its_an_addon_item(): void
    {
        $item = OrderItem::factory()->make(['type' => 'standard']);

        $this->assertFalse($item->isAddonItem());

        $item->type = 'addon';

        $this->assertTrue($item->isAddonItem());
    }
}
