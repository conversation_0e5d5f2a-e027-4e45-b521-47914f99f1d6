<?php

namespace Tests\Unit\Models;

use App\Actions\Billing\AddPaymentMethodToCustomer;
use App\Actions\Cart\CreateCartFromOrder;
use App\Billing\Gateway\PaymentMethod;
use App\Exceptions\OrderDoesNotMeetRequirementsException;
use App\Models\Address;
use App\Models\Card;
use App\Models\Cart;
use App\Models\Event;
use App\Models\Order;
use App\Models\Pickup;
use App\Models\ProductPriceGroup;
use App\Models\RecurringOrder;
use App\Models\Schedule;
use App\Models\Setting;
use App\Models\User;
use App\Services\SettingsService;
use App\Support\Enums\OrderStatus;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\WithFaker;
use Mockery;
use Mockery\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class UserTest extends TenantTestCase
{
    use WithFaker;

    #[Test]
    public function it_can_apply_a_credit(): void
    {
        /** @var User $user */
        $user = User::factory()->create(['credit' => 200]);

        $this->assertDatabaseCount(Event::class, 0);

        $user->applyCredit(500, 'some apply reason');

        $this->assertDatabaseHas('users', [
            'id' => $user->id,
            'credit' => 700
        ]);

        $this->assertDatabaseCount(Event::class, 1);

        $this->assertDatabaseHas('events', [
            'model_type' => \App\Models\User::class,
            'model_id' => $user->id,
            'description' => 'some apply reason',
            'event_id' => 'credit_applied',
            'metadata' => json_encode(['amount' => 500])
        ]);
    }

    #[Test]
    public function it_cannot_apply_a_zero_credit(): void
    {
        /** @var User $user */
        $user = User::factory()->create(['credit' => 100]);

        $this->assertDatabaseCount(Event::class, 0);

        $user->applyCredit(0, 'some apply reason');

        $this->assertDatabaseHas('users', [
            'id' => $user->id,
            'credit' => 100
        ]);

        $this->assertDatabaseCount(Event::class, 0);
    }

    #[Test]
    public function it_cannot_apply_a_negative_credit(): void
    {
        /** @var User $user */
        $user = User::factory()->create(['credit' => 100]);

        $this->assertDatabaseCount(Event::class, 0);

        $user->applyCredit(-1, 'some apply reason');

        $this->assertDatabaseHas('users', [
            'id' => $user->id,
            'credit' => 100
        ]);

        $this->assertDatabaseCount(Event::class, 0);
    }

    #[Test]
    public function it_can_remove_a_credit(): void
    {
        /** @var User $user */
        $user = User::factory()->create(['credit' => 500]);

        $this->assertDatabaseCount(Event::class, 0);

        $user->removeCredit(100, 'some removal reason');

        $this->assertDatabaseHas('users', [
            'id' => $user->id,
            'credit' => 400
        ]);

        $this->assertDatabaseCount(Event::class, 1);

        $this->assertDatabaseHas('events', [
            'model_type' => \App\Models\User::class,
            'model_id' => $user->id,
            'description' => 'some removal reason',
            'event_id' => 'credit_removed',
            'metadata' => json_encode(['amount' => 100])
        ]);
    }

    #[Test]
    public function it_can_remove_a_credit_larger_than_current_credit(): void
    {
        /** @var User $user */
        $user = User::factory()->create(['credit' => 500]);

        $this->assertDatabaseCount(Event::class, 0);

        $user->removeCredit(1000, 'some removal reason');

        $this->assertDatabaseHas('users', [
            'id' => $user->id,
            'credit' => 0
        ]);

        $this->assertDatabaseCount(Event::class, 1);

        $this->assertDatabaseHas('events', [
            'model_type' => \App\Models\User::class,
            'model_id' => $user->id,
            'description' => 'some removal reason',
            'event_id' => 'credit_removed',
            'metadata' => json_encode(['amount' => 1000])
        ]);
    }

    #[Test]
    public function it_cannot_remove_a_zero_credit(): void
    {
        /** @var User $user */
        $user = User::factory()->create(['credit' => 500]);

        $this->assertDatabaseCount(Event::class, 0);

        $user->removeCredit(0, 'some removal reason');

        $this->assertDatabaseHas('users', [
            'id' => $user->id,
            'credit' => 500
        ]);

        $this->assertDatabaseCount(Event::class, 0);
    }

    #[Test]
    public function it_cannot_remove_a_negative_credit(): void
    {
        /** @var User $user */
        $user = User::factory()->create(['credit' => 500]);

        $this->assertDatabaseCount(Event::class, 0);

        $user->removeCredit(-1, 'some removal reason');

        $this->assertDatabaseHas('users', [
            'id' => $user->id,
            'credit' => 500
        ]);

        $this->assertDatabaseCount(Event::class, 0);
    }

    #[Test]
    public function it_does_not_fetch_open_order_when_order_canceled_column_is_true(): void
    {
        /** @var User $user */
        $user = User::factory()->create();
        Order::factory()->create(['customer_id' => $user->id, 'canceled' => true]);

        $this->assertNull($user->queryOpenOrder());
    }

    #[Test]
    public function it_does_not_fetch_open_order_when_order_is_in_canceled_status(): void
    {
        /** @var User $user */
        $user = User::factory()->create();
        Order::factory()->create(['customer_id' => $user->id, 'status_id' => OrderStatus::canceled()]);

        $this->assertNull($user->queryOpenOrder());
    }

    #[Test]
    public function it_does_not_fetch_open_order_when_order_is_in_completed_status(): void
    {
        /** @var User $user */
        $user = User::factory()->create();
        Order::factory()->create(['customer_id' => $user->id, 'status_id' => OrderStatus::completed()]);

        $this->assertNull($user->queryOpenOrder());
    }

    #[Test]
    public function it_does_not_fetch_open_order_when_order_is_in_picked_up_status(): void
    {
        /** @var User $user */
        $user = User::factory()->create();
        Order::factory()->create(['customer_id' => $user->id, 'status_id' => OrderStatus::pickedUp()]);

        $this->assertNull($user->queryOpenOrder());
    }

    #[Test]
    public function it_does_not_fetch_open_order_when_order_is_confirmed_and_ordering_mode_is_not_defined(): void
    {
        /** @var User $user */
        $user = User::factory()->create();
        Order::factory()->create([
            'customer_id' => $user->id,
            'confirmed' => true,
            'deadline_date' => today()->addDay(),
            'pickup_date' => today()->addDay()
        ]);

        $this->assertNull($user->queryOpenOrder());
    }

    #[Test]
    public function it_does_not_fetch_open_order_when_order_is_confirmed_and_ordering_mode_edits_are_disabled(): void
    {
        Setting::updateOrCreate(['key' => 'ordering_mode'], ['value' => false]);
        Setting::flushCache();

        /** @var User $user */
        $user = User::factory()->create();
        $order = Order::factory()->create([
            'customer_id' => $user->id,
            'confirmed' => true,
            'deadline_date' => today()->addDay(),
            'pickup_date' => today()->addDay()
        ]);

        $this->assertNull($user->queryOpenOrder());
    }

    #[Test]
    public function it_does_not_fetch_open_order_when_order_is_confirmed_and_ordering_mode_edits_are_enabled_and_pickup_date_is_null(): void
    {
        Setting::updateOrCreate(['key' => 'ordering_mode'], ['value' => false]);
        Setting::flushCache();

        /** @var User $user */
        $user = User::factory()->create();
        Order::factory()->create([
            'customer_id' => $user->id,
            'confirmed' => true,
            'deadline_date' => null,
            'pickup_date' => null,
        ]);

        $this->assertNull($user->queryOpenOrder());
    }

    #[Test]
    public function it_does_not_fetch_open_order_when_order_is_confirmed_and_ordering_mode_edits_are_enabled_and_deadline_date_has_passed(): void
    {
        Setting::updateOrCreate(['key' => 'ordering_mode'], ['value' => true]);
        Setting::flushCache();

        /** @var User $user */
        $user = User::factory()->create();
        Order::factory()->create([
            'customer_id' => $user->id,
            'confirmed' => true,
            'deadline_date' => today()->subDay(),
            'pickup_date' => today()->subDay(),
        ]);

        $this->assertNull($user->queryOpenOrder());
    }

    #[Test]
    public function it_does_not_fetch_open_order_when_order_is_confirmed_and_ordering_mode_edits_are_enabled_and_deadline_datetime_has_passed(): void
    {
        Carbon::setTestNow($now = now());

        Setting::updateOrCreate(['key' => 'ordering_mode'], ['value' => true]);
        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value' => $now->hour]);
        Setting::flushCache();

        /** @var User $user */
        $user = User::factory()->create();

        Order::factory()->create([
            'customer_id' => $user->id,
            'confirmed' => true,
            'deadline_date' => today(),
            'pickup_date' => today()->addDay(),
        ]);

        $this->assertNull($user->queryOpenOrder());

        Carbon::setTestNow();
    }

    #[Test]
    public function it_fetches_recurring_open_order_when_order_over_a_one_off_order(): void
    {
        Setting::updateOrCreate(['key' => 'ordering_mode'], ['value' => true]);
        Setting::flushCache();

        /** @var User $user */
        $user = User::factory()->create();

        $recurring_order = Order::factory()->create([
            'is_recurring' => true,
            'blueprint_id' =>RecurringOrder::factory(),
            'customer_id' => $user->id,
            'confirmed' => true,
            'pickup_date' => today()
        ]);

        Order::factory()->create([
            'is_recurring' => false,
            'blueprint_id' => null,
            'customer_id' => $user->id,
            'confirmed' => true,
            'pickup_date' => today()
        ]);

        $open_order = $user->queryOpenOrder();

        $this->assertNotNull($open_order);
        $this->assertEquals($recurring_order->id, $open_order->id);
    }

    #[Test]
    public function it_cannot_reorder_an_order_when_the_user_does_not_have_a_current_order(): void
    {
        /** @var User $user */
        $user = User::factory()->create();

        $this->mock(CreateCartFromOrder::class, function (MockInterface $mock) {
            $mock->shouldNotReceive('handle');
        });

        $this->expectException(OrderDoesNotMeetRequirementsException::class);
        $this->expectExceptionMessage('You can not reorder because the order window is currently closed.');

        $user->reorderFromOrder(Order::factory()->create());
    }

    #[Test]
    public function it_cannot_reorder_an_order_when_the_current_order_window_is_closed(): void
    {
        /** @var User $user */
        $user = User::factory()->create();
        $schedule = Schedule::factory()->create();
        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);
        $order = Order::factory()->create(['customer_id' => $user->id, 'pickup_id' => $pickup->id]);

        $this->mock(CreateCartFromOrder::class, function (MockInterface $mock) {
            $mock->shouldNotReceive('handle');
        });

        $this->expectException(OrderDoesNotMeetRequirementsException::class);
        $this->expectExceptionMessage('You can not reorder because the order window is currently closed.');

        $user->reorderFromOrder(Order::factory()->create());
    }

    #[Test]
    public function it_can_reorder_an_order(): void
    {
        /** @var User $user */
        $pickup = Pickup::factory()->create(['schedule_id' => Schedule::factory()->hasDate()]);
        $user = User::factory()->create(['pickup_point' => $pickup->id]);

        $previous_order = Order::factory()->create(['customer_id' => $user->id, 'pickup_id' => $pickup->id, 'pickup_date' => today()->subWeek()]);

        $this->mock(CreateCartFromOrder::class, function (MockInterface $mock) use ($previous_order, $user) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(
                    Mockery::on(fn(Order $arg) => $arg->id === $previous_order->id),
                    Mockery::on(fn(Cart $arg) => $arg->cartCustomer()->id === $user->id)
                );
        });

        $result = $user->reorderFromOrder($previous_order);

        $this->assertInstanceOf(CreateCartFromOrder::class, $result);
    }

    #[Test]
    public function it_can_fetch_notification_recipients(): void
    {
        User::factory()->admin()
            ->create(['first_name' => 'admin', 'last_name' => 'one', 'email' => '<EMAIL>', 'settings' => ['email_notify_new_orders' => 0]]);

        User::factory()->admin()
            ->create(['first_name' => 'admin', 'last_name' => 'two', 'email' => '<EMAIL>', 'settings' => ['email_notify_new_orders' => 1]]);

        User::factory()->admin()
            ->create(['first_name' => 'admin', 'last_name' => 'three', 'email' => '<EMAIL>', 'settings' => ['email_notify_new_orders' => 1]]);

        User::factory()->admin()
            ->create(['first_name' => 'admin', 'last_name' => 'four', 'email' => '<EMAIL>', 'settings' => ['email_notify_new_orders' => 1]]);

        Setting::updateOrCreate(['key' => 'email_general'], ['value' => '<EMAIL>']);

        $this->assertEquals([
            '<EMAIL>' => 'admin two',
            '<EMAIL>' => 'admin three',
        ], User::notificationRecipients('email_notify_new_orders'));
    }

    #[Test]
    public function it_can_fetch_a_users_current_order(): void
    {
        Carbon::setTestNow(now());

        Setting::updateOrCreate(['key' => 'ordering_mode'], ['value' => 0]);

        // customer has no orders
        $customer = User::factory()->create();
        $this->assertNull($customer->currentOrder());

        // order is a preorder
        Order::factory()->create([
            'customer_id' => $customer->id,
            'is_recurring' => false,
            'status_id' => OrderStatus::preOrder(),
            'confirmed' => true,
            'pickup_date' => today()->addDays(2)
        ]);

        $this->assertNull($customer->refresh()->currentOrder());

        // single, recurring order but deadline date time has passed
        Setting::updateOrCreate(
            ['key' => 'order_deadline_hour'],
            ['value' => 0]
        );

        $o = Order::factory()->create([
            'customer_id' => $customer->id,
            'is_recurring' => true,
            'status_id' => OrderStatus::confirmed(),
            'deadline_date' => today(),
            'confirmed' => false,
            'pickup_date' => today()->addDays(2)
        ]);

        $this->assertNull($customer->refresh()->currentOrder());

        // single, recurring order and deadline date time has not passed
        $customer = User::factory()->create();

        Setting::updateOrCreate(
            ['key' => 'order_deadline_hour'],
            ['value' => 23]
        );

        $expected_order = Order::factory()->create([
            'customer_id' => $customer->id,
            'blueprint_id' => RecurringOrder::factory(),
            'status_id' => OrderStatus::confirmed(),
            'deadline_date' => today()->addDay(),
            'pickup_date' => today()->addDays(2),
            'confirmed' => true,
        ]);

        $current_order = $customer->refresh()->currentOrder();
        $this->assertNotNull($current_order);
        $this->assertInstanceOf(Order::class, $current_order);
        $this->assertEquals($expected_order->id, $current_order->id);

        // multiple recurring and deadline date time has not passed, returns first / oldest
        $customer = User::factory()->create();

        $unexpected_order = Order::factory()->create([
            'customer_id' => $customer->id,
            'blueprint_id' => RecurringOrder::factory(),
            'status_id' => OrderStatus::confirmed(),
            'deadline_date' => today()->addDays(3),
            'confirmed' => true,
            'pickup_date' => today()->addDays(3)
        ]);

        $expected_order = Order::factory()->create([
            'customer_id' => $customer->id,
            'blueprint_id' => RecurringOrder::factory(),
            'status_id' => OrderStatus::confirmed(),
            'deadline_date' => today()->addDays(2),
            'confirmed' => true,
            'pickup_date' => today()->addDays(2)
        ]);

        $current_order = $customer->refresh()->currentOrder();
        $this->assertNotNull($current_order);
        $this->assertInstanceOf(Order::class, $current_order);
        $this->assertEquals($expected_order->id, $current_order->id);

        // single one-off order but confirmed
        $customer = User::factory()->create();

        Order::factory()->create([
            'customer_id' => $customer->id,
            'is_recurring' => false,
            'status_id' => OrderStatus::confirmed(),
            'confirmed' => false,
            'confirmed_date' => today(),
            'deadline_date' => today()->addDays(2),
            'pickup_date' => today()->addDays(2)
        ]);

        $this->assertNull($customer->refresh()->currentOrder());

        Carbon::setTestNow();
    }

    #[Test]
    public function it_can_get_its_price_group_id(): void
    {
        /** @var User $user */
        $user = User::factory()->create(['pickup_point' => 0, 'pricing_group_id' => null]);

        $this->assertNull($user->priceGroupId());

        /** @var ProductPriceGroup $group_one */
        $group_one = ProductPriceGroup::factory()->create();

        $pickup = Pickup::factory()->create(['pricing_group_id' => $group_one]);

        /** @var User $user */
        $user = User::factory()->create(['pickup_point' => $pickup->id, 'pricing_group_id' => null]);

        $this->assertEquals($group_one->id, $user->priceGroupId());

        $group_two = ProductPriceGroup::factory()->create();

        /** @var User $user */
        $user = User::factory()->create(['pickup_point' => $pickup->id, 'pricing_group_id' => $group_two->id]);

        $this->assertEquals($group_two->id, $user->priceGroupId());
    }

    #[Test]
    public function it_can_get_the_formatted_sms_number(): void
    {
        $user = User::factory()->make(['phone' => null, 'cell_phone' => null]);
        $this->assertNull($user->formattedSMSNumber());

        $user = User::factory()->make(['phone' => '************', 'cell_phone' => null]);
        $this->assertEquals('+12025550146', $user->formattedSMSNumber());

        $user = User::factory()->make(['phone' => '************', 'cell_phone' => '************']);
        $this->assertEquals('+12025550147', $user->formattedSMSNumber());

        $user = User::factory()->make(['phone' => '555-0146', 'cell_phone' => null]);
        $this->assertNull($user->formattedSMSNumber());

        $user = User::factory()->make(['phone' => '2025550147', 'cell_phone' => '']);
        $this->assertEquals('+12025550147', $user->formattedSMSNumber());

        $user = User::factory()->make(['phone' => '(202)5550147', 'cell_phone' => '']);
        $this->assertEquals('+12025550147', $user->formattedSMSNumber());

        $user = User::factory()->make(['phone' => '(*************', 'cell_phone' => '']);
        $this->assertEquals('+12025550147', $user->formattedSMSNumber());

        $user = User::factory()->make(['phone' => '************', 'cell_phone' => '']);
        $this->assertEquals('+12025550147', $user->formattedSMSNumber());

        $user = User::factory()->make(['phone' => '555-0146', 'cell_phone' => '555-0147']);
        $this->assertNull($user->formattedSMSNumber());
    }

    #[Test]
    public function it_has_users(): void
    {
        $pickup = Pickup::factory()->create();

        $user_one = User::factory()->create(['pickup_point' => 0]);
        $user_two = User::factory()->create(['pickup_point' => $pickup->id]);

        $users = $pickup->users()->get();

        $this->assertFalse($users->contains(fn (User $user) => $user->id === $user_one->id));
        $this->assertTrue($users->contains(fn (User $user) => $user->id === $user_two->id));
    }

    #[Test]
    public function it_scopes_to_users_who_receive_last_chance_email(): void
    {
        $pickup = Pickup::factory()->create();

        // no valid email
        $unexpected_user_one = User::factory()->create(['email' => '', 'order_deadline_email_reminder' => true]);

        // unsubscribed
        $unexpected_user_two = User::factory()->create(['order_deadline_email_reminder' => false]);

        // has subscription
        $unexpected_user_three = User::factory()->create(['order_deadline_email_reminder' => true]);
        RecurringOrder::factory()->create(['customer_id' => $unexpected_user_three->id]);

        $expected_user = User::factory()->create(['email' => '<EMAIL>', 'order_deadline_email_reminder' => true]);

        $users = User::receivesLastChanceEmail()->get();

        $this->assertFalse($users->contains(fn (User $user) => $user->id === $unexpected_user_one->id));
        $this->assertFalse($users->contains(fn (User $user) => $user->id === $unexpected_user_two->id));
        $this->assertFalse($users->contains(fn (User $user) => $user->id === $unexpected_user_three->id));
        $this->assertTrue($users->contains(fn (User $user) => $user->id === $expected_user->id));
    }

    #[Test]
    public function it_knows_if_its_a_stripe_customer(): void
    {
        $user = User::factory()->make(['customer_id' => '']);
        $this->assertFalse($user->isStripeCustomer());

        $user->customer_id = 'abc';
        $this->assertTrue($user->isStripeCustomer());
    }

    #[Test]
    public function all_requests_to_show_page_are_redirected_to_the_edit_page(): void
    {
        $user = User::factory()->create();

        $this->actingAsAdmin()->get(route('admin.users.show', compact('user')))
            ->assertRedirect(route('admin.users.edit', compact('user')));
    }

    #[Test]
    public function it_has_many_addresses(): void
    {
        $user = User::factory()->create();

        $address_two = Address::factory()->create();

        $address_one = Address::factory()->create();
        $user->addresses()->save($address_one);
        $this->assertDatabaseHas('addressables', [
            'address_id' => $address_one->id,
            'addressable_id' => $user->id,
            'addressable_type' => User::class,
            'name' => null,
            'street_2' => '',
            'instructions' => null,
            'is_default' => false
        ]);

        $user->addresses()->save($address_two, [
            'name' => 'test name 2',
            'street_2' => 'Apt 2',
            'instructions' => 'Bring it here 2.',
            'is_default' => true
        ]);

        $this->assertDatabaseHas('addressables', [
            'address_id' => $address_two->id,
            'addressable_id' => $user->id,
            'addressable_type' => User::class,
            'name' => 'test name 2',
            'street_2' => 'Apt 2',
            'instructions' => 'Bring it here 2.',
            'is_default' => true
        ]);

        foreach ($user->addresses as $user_address) {
            $this->assertNotNull($user_address->location);
        }
    }

    #[Test]
    public function it_can_add_a_default_payment_method_to_a_user(): void
    {
        $user = User::factory()->create();

        $payment_method = new PaymentMethod(
            id: 'src_1JroTYC1Poh057RbV7LbT4iS',
            customer_id: 'cus_1234',
            customer_name: 'Test Tester',
            exp_month: '05',
            exp_year: '2028',
            brand: 'some brand',
            last_four: '0022',
        );

        $this->mock(AddPaymentMethodToCustomer::class, function (MockInterface $mock) use ($user, $payment_method) {
            $mock->shouldReceive('handle')
                ->once()
                ->with($user, $payment_method, true)
                ->andReturn(new Card);
        });

        $user->addPaymentMethod($payment_method, true);
    }

    #[Test]
    public function it_can_add_non_default_payment_method_to_a_user(): void
    {
        $user = User::factory()->create();

        $payment_method = new PaymentMethod(
            id: 'src_1JroTYC1Poh057RbV7LbT4iS',
            customer_id: 'cus_1234',
            customer_name: 'Test Tester',
            exp_month: '05',
            exp_year: '2028',
            brand: 'some brand',
            last_four: '0022',
        );

        $this->mock(AddPaymentMethodToCustomer::class, function (MockInterface $mock) use ($user, $payment_method) {
            $mock->shouldReceive('handle')
                ->once()
                ->with($user, $payment_method, false)
                ->andReturn(new Card);
        });

        $user->addPaymentMethod($payment_method);
    }

    #[Test]
    public function it_migrates_and_return_its_default_address_when_user_does_not_have_an_address(): void
    {
        $user = User::factory()->create([
            'street' => $this->faker->streetAddress(),
            'street_2' => $this->faker->streetAddress(),
            'city' => $this->faker->city(),
            'state' => $this->faker->stateAbbr(),
            'zip' => $this->faker->postcode(),
            'country' => 'USA',
        ]);

        $attributes = $user->defaultShippingAttributes();

        $this->assertNotNull($attributes['address_id']);
        $this->assertEquals($user->street, $attributes['street']);
        $this->assertEquals($user->street_2, $attributes['street_2']);
        $this->assertEquals($user->city, $attributes['city']);
        $this->assertEquals($user->state, $attributes['state']);
        $this->assertEquals($user->zip, $attributes['postal_code']);
        $this->assertEquals(app(SettingsService::class)->farmCountry(), $attributes['country']);
    }

    #[Test]
    public function it_can_return_its_default_address_when_user_has_a_non_default_address(): void
    {
        $user = User::factory()->create([
            'street' => $this->faker->streetAddress(),
            'street_2' => $this->faker->streetAddress(),
            'city' => $this->faker->city(),
            'state' => $this->faker->stateAbbr(),
            'zip' => $this->faker->postcode(),
            'country' => 'USA',
        ]);

        $address = Address::factory()->create([
            'street' => $this->faker->streetAddress(),
            'city' => $this->faker->city(),
            'state' => $this->faker->stateAbbr(),
            'postal_code' => $this->faker->postcode(),
            'country' => 'CA',
        ]);

        $street_2 = $this->faker->streetAddress();

        $user->addresses()->attach($address->id, [
            'name' => 'something',
            'street_2' => $street_2,
            'is_default' => false
        ]);

        $this->assertEquals([
            'address_id' => null,
            'street' => $user->street,
            'street_2' => $user->street_2,
            'city' => $user->city,
            'state' => $user->state,
            'postal_code' => $user->zip,
            'country' => 'USA',
        ],  $user->defaultShippingAttributes());
    }

    #[Test]
    public function it_can_return_its_default_address_when_user_has_a_default_address(): void
    {
        $user = User::factory()->create([
            'street' => $this->faker->streetAddress(),
            'street_2' => $this->faker->streetAddress(),
            'city' => $this->faker->city(),
            'state' => $this->faker->stateAbbr(),
            'zip' => $this->faker->postcode(),
            'country' => 'USA',
        ]);

        $address = Address::factory()->create([
            'street' => $this->faker->streetAddress(),
            'city' => $this->faker->city(),
            'state' => $this->faker->stateAbbr(),
            'postal_code' => $this->faker->postcode(),
            'country' => 'CA',
        ]);

        $street_2 = $this->faker->streetAddress();

        $user->addresses()->attach($address->id, [
            'name' => 'something',
            'street_2' => $street_2,
            'is_default' => true
        ]);

        $this->assertEquals([
            'address_id' => $address->id,
            'street' => $address->street,
            'street_2' => $street_2,
            'city' => $address->city,
            'state' => $address->state,
            'postal_code' => $address->postal_code,
            'country' => 'CA',
        ],  $user->defaultShippingAttributes());
    }

    #[Test]
    public function it_can_fetch_the_deleted_customer(): void
    {
        User::where('email', '<EMAIL>')->delete();

        $deleted = User::deletedCustomer();

        $this->assertDatabaseHas(User::class, [
            'id' => $deleted->id,
            'email' => '<EMAIL>',
            'first_name' => 'Deleted',
            'last_name' => 'Customer',
        ]);

        $new_deleted = User::deletedCustomer();

        $this->assertEquals($deleted->id, $new_deleted->id);
    }

    #[Test]
    public function it_updates_the_default_shipping_address_to_a_new_address(): void
    {
        $address = [
            'address_id' => null,
            'street' => '123 Main St',
            'street_2' => 'Apt 1',
            'city' => 'Test City',
            'state' => 'TE',
            'postal_code' => '12345',
            'country' => 'USA',
        ];

        $user = User::factory()->create();

        $user->updateDefaultShippingAddress($address);

        $new_address = Address::where([
            'street' => '123 Main St',
            'city' => 'Test City',
            'state' => 'TE',
            'postal_code' => '12345',
            'country' => 'USA',
        ])->first();

        $this->assertNotNull($new_address);

        $this->assertTrue(
            $user->addresses()
                ->default()
                ->where(['address_id' => $new_address->id, 'street_2' => 'Apt 1'])
                ->exists()
        );
    }

    #[Test]
    public function it_updates_the_default_shipping_address_to_an_address(): void
    {
        /** @var Address $address */
        $address = Address::factory()->create();

        $user = User::factory()->create();

        $user->updateDefaultShippingAddress([
            'address_id' => $address->id,
            'street' => $address->street,
            'street_2' => 'Apt 1',
            'city' => $address->city,
            'state' => $address->state,
            'postal_code' => $address->postal_code,
            'country' => $address->country,
        ]);

        $this->assertEquals(1, Address::where([
            'street' => $address->street,
            'city' => $address->city,
            'state' => $address->state,
            'postal_code' => $address->postal_code,
            'country' => $address->country,
        ])->count());

        $this->assertTrue(
            $user->addresses()
                ->default()
                ->where(['address_id' => $address->id, 'street_2' => 'Apt 1'])
                ->exists()
        );
    }

    #[Test]
    public function it_removes_the_existing_default_address_when_updating_the_default_address_address(): void
    {
        /** @var Address $address */
        $address = Address::factory()->create();

        $user = User::factory()->create();

        $user->addresses()->attach($address->id, ['is_default' => true]);

        /** @var Address $new_address */
        $new_address = Address::factory()->create();

        $user->updateDefaultShippingAddress([
            'address_id' => $new_address->id,
            'street' => $new_address->street,
            'street_2' => 'Apt 1',
            'city' => $new_address->city,
            'state' => $new_address->state,
            'postal_code' => $new_address->postal_code,
            'country' => $new_address->country,
        ]);

        $this->assertFalse(
            $user->addresses()
                ->default()
                ->where(['address_id' => $address->id])
                ->exists()
        );

        $this->assertTrue(
            $user->addresses()
                ->default()
                ->where(['address_id' => $new_address->id, 'street_2' => 'Apt 1'])
                ->exists()
        );
    }

    #[Test]
    public function it_can_get_its_initials(): void
    {
        $user = User::factory()->make(['first_name' => 'john', 'last_name' => 'doe']);
        $this->assertEquals('JD', $user->initials());
    }

    protected function setUp(): void
    {
        parent::setUp();
        Setting::updateOrCreate(['key' => 'one_page_checkout'], ['value' => 0]);
        Setting::updateOrCreate(['key' => 'cart_service'], ['value' => 'order']);
    }
}
