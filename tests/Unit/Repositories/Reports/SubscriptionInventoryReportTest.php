<?php

namespace Tests\Unit\Repositories\Reports;

use App\Models\Collection;
use App\Models\Product;
use App\Models\RecurringOrder;
use App\Models\RecurringOrderItem;
use App\Models\Vendor;
use App\Repositories\Reports\SubscriptionInventoryReport;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class SubscriptionInventoryReportTest extends TenantTestCase
{
    #[Test]
    public function it_generates_a_product_inventory_report_grouped_by_product(): void
    {
        $product_one = Product::factory()->create(['inventory' => 5, 'title' => 'steak', 'sku' => 'A523']);
        $product_two = Product::factory()->create(['inventory' => 10, 'title' => 'milk', 'sku' => 'A223']);
        $product_three = Product::factory()->create(['inventory' => 15, 'title' => 'chicken', 'sku' => 'A323']);

        $date_one = today()->addDays(4);
        $subscription_one = RecurringOrder::factory()->create(['generate_at' => $date_one]);
        RecurringOrderItem::factory()->create(['order_id' => $subscription_one->id, 'product_id' => $product_one->id, 'qty' => 5]);
        RecurringOrderItem::factory()->create(['order_id' => $subscription_one->id, 'product_id' => $product_two->id, 'qty' => 3]);

        $subscription_two = RecurringOrder::factory()->create(['generate_at' => $date_one]);
        RecurringOrderItem::factory()->create(['order_id' => $subscription_two->id, 'product_id' => $product_two->id, 'qty' => 1]);
        RecurringOrderItem::factory()->create(['order_id' => $subscription_two->id, 'product_id' => $product_three->id, 'qty' => 8]);

        $date_two = today()->addDays(2);
        $subscription_three = RecurringOrder::factory()->create(['generate_at' => $date_two]);
        RecurringOrderItem::factory()->create(['order_id' => $subscription_three->id, 'product_id' => $product_one->id, 'qty' => 4]);
        RecurringOrderItem::factory()->create(['order_id' => $subscription_three->id, 'product_id' => $product_three->id, 'qty' => 2]);

        $date_three = today()->addDays(6);
        $subscription_four = RecurringOrder::factory()->create(['generate_at' => $date_three]);
        RecurringOrderItem::factory()->create(['order_id' => $subscription_four->id, 'product_id' => $product_two->id, 'qty' => 10]);
        RecurringOrderItem::factory()->create(['order_id' => $subscription_four->id, 'product_id' => $product_three->id, 'qty' => 13]);

        $report = (new SubscriptionInventoryReport)->handle([]);

        $this->assertCount(3, $report);

        $row_one = $report[0];
        $this->assertEquals($product_two->id, $row_one->product_id);
        $this->assertEquals($product_two->title, $row_one->product_title);
        $this->assertEquals($product_two->sku, $row_one->product_sku);
        $this->assertEquals(14, $row_one->on_order);
        $this->assertEquals($product_two->inventory, $row_one->current_inventory);

        $row_two = $report[1];
        $this->assertEquals($product_three->id, $row_two->product_id);
        $this->assertEquals($product_three->title, $row_two->product_title);
        $this->assertEquals($product_three->sku, $row_two->product_sku);
        $this->assertEquals(23, $row_two->on_order);
        $this->assertEquals($product_three->inventory, $row_two->current_inventory);

        $row_three = $report[2];
        $this->assertEquals($product_one->id, $row_three->product_id);
        $this->assertEquals($product_one->title, $row_three->product_title);
        $this->assertEquals($product_one->sku, $row_three->product_sku);
        $this->assertEquals(9, $row_three->on_order);
        $this->assertEquals($product_one->inventory, $row_three->current_inventory);
    }

    #[Test]
    public function it_generates_a_product_inventory_report_for_the_base_product_of_bundle_products(): void
    {
        $base_product_one = Product::factory()->create(['inventory' => 5, 'title' => 'base steak', 'sku' => 'A523']);
        $base_product_two = Product::factory()->create(['inventory' => 10, 'title' => 'base milk', 'sku' => 'A223']);
        $base_product_three = Product::factory()->create(['inventory' => 15, 'title' => 'base chicken', 'sku' => 'A323']);

        $bundle_product_one = Product::factory()->create(['inventory' => 5, 'title' => 'bundle 1', 'sku' => 'B523']);
        $bundle_product_one->bundle()->attach($base_product_one->id, ['qty' => 2]);
        $bundle_product_one->bundle()->attach($base_product_two->id, ['qty' => 4]);

        $bundle_product_two = Product::factory()->create(['inventory' => 10, 'title' => 'bundle 2', 'sku' => 'B223']);
        $bundle_product_two->bundle()->attach($base_product_three->id, ['qty' => 3]);

        $date_one = today()->addDays(4);
        $subscription_one = RecurringOrder::factory()->create(['generate_at' => $date_one]);
        RecurringOrderItem::factory()->create(['order_id' => $subscription_one->id, 'product_id' => $bundle_product_one->id, 'qty' => 5]);
        RecurringOrderItem::factory()->create(['order_id' => $subscription_one->id, 'product_id' => $bundle_product_two->id, 'qty' => 3]);

        $subscription_two = RecurringOrder::factory()->create(['generate_at' => $date_one]);
        RecurringOrderItem::factory()->create(['order_id' => $subscription_two->id, 'product_id' => $bundle_product_two->id, 'qty' => 1]);
        RecurringOrderItem::factory()->create(['order_id' => $subscription_two->id, 'product_id' => $base_product_three->id, 'qty' => 8]);

        $date_two = today()->addDays(2);
        $subscription_three = RecurringOrder::factory()->create(['generate_at' => $date_two]);
        RecurringOrderItem::factory()->create(['order_id' => $subscription_three->id, 'product_id' => $bundle_product_one->id, 'qty' => 4]);
        RecurringOrderItem::factory()->create(['order_id' => $subscription_three->id, 'product_id' => $base_product_three->id, 'qty' => 2]);

        $report = (new SubscriptionInventoryReport)->handle([]);

        $this->assertCount(3, $report);

        $row_one = $report[0];
        $this->assertEquals($base_product_two->id, $row_one->product_id);
        $this->assertEquals($base_product_two->title, $row_one->product_title);
        $this->assertEquals($base_product_two->sku, $row_one->product_sku);
        $this->assertEquals(36, $row_one->on_order);
        $this->assertEquals($base_product_two->inventory, $row_one->current_inventory);

        $row_two = $report[1];
        $this->assertEquals($base_product_three->id, $row_two->product_id);
        $this->assertEquals($base_product_three->title, $row_two->product_title);
        $this->assertEquals($base_product_three->sku, $row_two->product_sku);
        $this->assertEquals(22, $row_two->on_order);
        $this->assertEquals($base_product_three->inventory, $row_two->current_inventory);

        $row_three = $report[2];
        $this->assertEquals($base_product_one->id, $row_three->product_id);
        $this->assertEquals($base_product_one->title, $row_three->product_title);
        $this->assertEquals($base_product_one->sku, $row_three->product_sku);
        $this->assertEquals(18, $row_three->on_order);
        $this->assertEquals($base_product_one->inventory, $row_three->current_inventory);
    }

    #[Test]
    public function it_can_filter_by_generates_at_date(): void
    {
        $product_one = Product::factory()->create(['inventory' => 5, 'title' => 'steak', 'sku' => 'A523']);
        $product_two = Product::factory()->create(['inventory' => 10, 'title' => 'milk', 'sku' => 'A223']);
        $product_three = Product::factory()->create(['inventory' => 15, 'title' => 'chicken', 'sku' => 'A323']);

        $subscription_one = RecurringOrder::factory()->create(['generate_at' => today()->addDays(2), 'ready_at' => today()->addDays(5)]);
        RecurringOrderItem::factory()->create(['order_id' => $subscription_one->id, 'product_id' => $product_one->id, 'qty' => 5]);

        $subscription_two = RecurringOrder::factory()->create(['generate_at' => today()->addDays(4), 'ready_at' => today()->addDays(7)]);
        RecurringOrderItem::factory()->create(['order_id' => $subscription_two->id, 'product_id' => $product_three->id, 'qty' => 2]);

        $subscription_three = RecurringOrder::factory()->create(['generate_at' => today()->addDays(6), 'ready_at' => today()->addDays(9)]);
        RecurringOrderItem::factory()->create(['order_id' => $subscription_three->id, 'product_id' => $product_two->id, 'qty' => 10]);

        $report = (new SubscriptionInventoryReport)->handle([
            'date_type' => 'generate_at',
            'date_range' => [
                'end' => today()->addDays(1)
            ]
        ]);

        $this->assertCount(0, $report);

        $report = (new SubscriptionInventoryReport)->handle([
            'date_type' => 'generate_at',
            'date_range' => [
                'start' => today()->addDays(7)
            ]
        ]);

        $this->assertCount(0, $report);

        $report = (new SubscriptionInventoryReport)->handle([
            'date_type' => 'generate_at',
            'date_range' => [
                'start' => today()->addDays(1),
                'end' => today()->addDays(3)
            ]
        ]);

        $this->assertCount(1, $report);

        $report = (new SubscriptionInventoryReport)->handle([
            'date_type' => 'generate_at',
            'date_range' => [
                'start' => today()->addDays(1),
                'end' => today()->addDays(5)
            ]
        ]);

        $this->assertCount(2, $report);

        $report = (new SubscriptionInventoryReport)->handle([
            'date_type' => 'generate_at',
            'date_range' => [
                'start' => today()->addDays(3),
                'end' => today()->addDays(7)
            ]
        ]);

        $this->assertCount(2, $report);
    }

    #[Test]
    public function it_can_filter_by_ready_at_date(): void
    {
        $product_one = Product::factory()->create(['inventory' => 5, 'title' => 'steak', 'sku' => 'A523']);
        $product_two = Product::factory()->create(['inventory' => 10, 'title' => 'milk', 'sku' => 'A223']);
        $product_three = Product::factory()->create(['inventory' => 15, 'title' => 'chicken', 'sku' => 'A323']);

        $subscription_one = RecurringOrder::factory()->create(['generate_at' => today()->addDays(2), 'ready_at' => today()->addDays(5)]);
        RecurringOrderItem::factory()->create(['order_id' => $subscription_one->id, 'product_id' => $product_one->id, 'qty' => 5]);

        $subscription_two = RecurringOrder::factory()->create(['generate_at' => today()->addDays(4), 'ready_at' => today()->addDays(7)]);
        RecurringOrderItem::factory()->create(['order_id' => $subscription_two->id, 'product_id' => $product_three->id, 'qty' => 2]);

        $subscription_three = RecurringOrder::factory()->create(['generate_at' => today()->addDays(6), 'ready_at' => today()->addDays(9)]);
        RecurringOrderItem::factory()->create(['order_id' => $subscription_three->id, 'product_id' => $product_two->id, 'qty' => 10]);

        $report = (new SubscriptionInventoryReport)->handle([
            'date_type' => 'ready_at',
            'date_range' => [
                'end' => today()->addDays(4)
            ]
        ]);

        $this->assertCount(0, $report);

        $report = (new SubscriptionInventoryReport)->handle([
            'date_type' => 'ready_at',
            'date_range' => [
                'start' => today()->addDays(10)
            ]
        ]);

        $this->assertCount(0, $report);

        $report = (new SubscriptionInventoryReport)->handle([
            'date_type' => 'ready_at',
            'date_range' => [
                'start' => today()->addDays(4),
                'end' => today()->addDays(6)
            ]
        ]);

        $this->assertCount(1, $report);

        $report = (new SubscriptionInventoryReport)->handle([
            'date_type' => 'ready_at',
            'date_range' => [
                'start' => today()->addDays(4),
                'end' => today()->addDays(8)
            ]
        ]);

        $this->assertCount(2, $report);

        $report = (new SubscriptionInventoryReport)->handle([
            'date_type' => 'ready_at',
            'date_range' => [
                'start' => today()->addDays(6),
                'end' => today()->addDays(10)
            ]
        ]);

        $this->assertCount(2, $report);
    }

    #[Test]
    public function it_can_filter_by_delivery_methods(): void
    {
        $product_one = Product::factory()->create(['inventory' => 5, 'title' => 'steak', 'sku' => 'A523']);
        $product_two = Product::factory()->create(['inventory' => 10, 'title' => 'milk', 'sku' => 'A223']);
        $product_three = Product::factory()->create(['inventory' => 15, 'title' => 'chicken', 'sku' => 'A323']);

        $subscription_one = RecurringOrder::factory()->create();
        RecurringOrderItem::factory()->create(['order_id' => $subscription_one->id, 'product_id' => $product_one->id, 'qty' => 5]);

        $subscription_two = RecurringOrder::factory()->create();
        RecurringOrderItem::factory()->create(['order_id' => $subscription_two->id, 'product_id' => $product_three->id, 'qty' => 2]);

        $subscription_three = RecurringOrder::factory()->create();
        RecurringOrderItem::factory()->create(['order_id' => $subscription_three->id, 'product_id' => $product_two->id, 'qty' => 10]);

        $report = (new SubscriptionInventoryReport)->handle([
            'delivery_methods' => [$subscription_one->fulfillment_id],
        ]);

        $this->assertCount(1, $report);

        $report = (new SubscriptionInventoryReport)->handle([
            'delivery_methods' => [$subscription_two->fulfillment_id],
        ]);

        $this->assertCount(1, $report);

        $report = (new SubscriptionInventoryReport)->handle([
            'delivery_methods' => [$subscription_one->fulfillment_id, $subscription_three->fulfillment_id],
        ]);

        $this->assertCount(2, $report);
    }

    #[Test]
    public function it_can_filter_by_vendor(): void
    {
        $product_one = Product::factory()->create(['inventory' => 5, 'title' => 'steak', 'sku' => 'A523', 'vendor_id' => Vendor::factory()]);
        $product_two = Product::factory()->create(['inventory' => 10, 'title' => 'milk', 'sku' => 'A223', 'vendor_id' => Vendor::factory()]);
        $product_three = Product::factory()->create(['inventory' => 15, 'title' => 'chicken', 'sku' => 'A323', 'vendor_id' => Vendor::factory()]);

        $subscription_one = RecurringOrder::factory()->create();
        RecurringOrderItem::factory()->create(['order_id' => $subscription_one->id, 'product_id' => $product_one->id, 'qty' => 5]);

        $subscription_two = RecurringOrder::factory()->create();
        RecurringOrderItem::factory()->create(['order_id' => $subscription_two->id, 'product_id' => $product_three->id, 'qty' => 2]);

        $subscription_three = RecurringOrder::factory()->create();
        RecurringOrderItem::factory()->create(['order_id' => $subscription_three->id, 'product_id' => $product_two->id, 'qty' => 10]);

        $report = (new SubscriptionInventoryReport)->handle([
            'vendor_id' => $product_one->vendor_id,
        ]);

        $this->assertCount(1, $report);

        $report = (new SubscriptionInventoryReport)->handle([
            'vendor_id' => $product_two->vendor_id,
        ]);

        $this->assertCount(1, $report);

        $report = (new SubscriptionInventoryReport)->handle([
            'vendor_id' => 1231231,
        ]);

        $this->assertCount(0, $report);
    }

    #[Test]
    public function it_can_filter_by_sku(): void
    {
        $product_one = Product::factory()->create(['inventory' => 5, 'title' => 'steak', 'sku' => 'A523']);
        $product_two = Product::factory()->create(['inventory' => 10, 'title' => 'milk', 'sku' => 'A223']);
        $product_three = Product::factory()->create(['inventory' => 15, 'title' => 'chicken', 'sku' => 'A323']);

        $subscription_one = RecurringOrder::factory()->create();
        RecurringOrderItem::factory()->create(['order_id' => $subscription_one->id, 'product_id' => $product_one->id, 'qty' => 5]);

        $subscription_two = RecurringOrder::factory()->create();
        RecurringOrderItem::factory()->create(['order_id' => $subscription_two->id, 'product_id' => $product_three->id, 'qty' => 2]);

        $subscription_three = RecurringOrder::factory()->create();
        RecurringOrderItem::factory()->create(['order_id' => $subscription_three->id, 'product_id' => $product_two->id, 'qty' => 10]);

        $report = (new SubscriptionInventoryReport)->handle([
            'sku' => 'A523',
        ]);

        $this->assertCount(1, $report);

        $report = (new SubscriptionInventoryReport)->handle([
            'sku' => 'A223',
        ]);

        $this->assertCount(1, $report);

        $report = (new SubscriptionInventoryReport)->handle([
            'sku' => '12903812',
        ]);

        $this->assertCount(0, $report);
    }

    #[Test]
    public function it_can_filter_by_product_collection(): void
    {
        $product_one = Product::factory()->create(['inventory' => 5, 'title' => 'steak', 'sku' => 'A523']);
        $collection_one = Collection::factory()->create();
        $collection_one->products()->attach($product_one->id);

        $product_two = Product::factory()->create(['inventory' => 10, 'title' => 'milk', 'sku' => 'A223']);
        $collection_two = Collection::factory()->create();
        $collection_two->products()->attach($product_two->id);

        $product_three = Product::factory()->create(['inventory' => 15, 'title' => 'chicken', 'sku' => 'A323']);
        $collection_three = Collection::factory()->create();
        $collection_three->products()->attach($product_three->id);

        $subscription_one = RecurringOrder::factory()->create();
        RecurringOrderItem::factory()->create(['order_id' => $subscription_one->id, 'product_id' => $product_one->id, 'qty' => 5]);

        $subscription_two = RecurringOrder::factory()->create();
        RecurringOrderItem::factory()->create(['order_id' => $subscription_two->id, 'product_id' => $product_three->id, 'qty' => 2]);

        $subscription_three = RecurringOrder::factory()->create();
        RecurringOrderItem::factory()->create(['order_id' => $subscription_three->id, 'product_id' => $product_two->id, 'qty' => 10]);

        $report = (new SubscriptionInventoryReport)->handle([
            'collection_id' => $collection_one->id,
        ]);

        $this->assertCount(1, $report);

        $report = (new SubscriptionInventoryReport)->handle([
            'collection_id' => $collection_two->id,
        ]);

        $this->assertCount(1, $report);

        $report = (new SubscriptionInventoryReport)->handle([
            'collection_id' => 10289379012
        ]);

        $this->assertCount(0, $report);
    }

    #[Test]
    public function it_does_not_include_deleted_subscriptions(): void
    {
        $product_one = Product::factory()->create(['inventory' => 5, 'title' => 'steak', 'sku' => 'A523']);
        $product_two = Product::factory()->create(['inventory' => 10, 'title' => 'milk', 'sku' => 'A223']);

        $date_one = today()->addDays(4);
        $subscription_one = RecurringOrder::factory()->create(['generate_at' => $date_one, 'deleted_at' => now()]);
        RecurringOrderItem::factory()->create(['order_id' => $subscription_one->id, 'product_id' => $product_one->id, 'qty' => 5]);
        RecurringOrderItem::factory()->create(['order_id' => $subscription_one->id, 'product_id' => $product_two->id, 'qty' => 3]);

        $report = (new SubscriptionInventoryReport)->handle([]);

        $this->assertCount(0, $report);
    }

    #[Test]
    public function it_does_not_include_subscriptions_without_a_generate_at_value(): void
    {
        $product_one = Product::factory()->create(['inventory' => 5, 'title' => 'steak', 'sku' => 'A523']);
        $product_two = Product::factory()->create(['inventory' => 10, 'title' => 'milk', 'sku' => 'A223']);

        $date_one = today()->addDays(4);
        $subscription_one = RecurringOrder::factory()->create(['generate_at' => null, 'ready_at' => $date_one]);
        RecurringOrderItem::factory()->create(['order_id' => $subscription_one->id, 'product_id' => $product_one->id, 'qty' => 5]);
        RecurringOrderItem::factory()->create(['order_id' => $subscription_one->id, 'product_id' => $product_two->id, 'qty' => 3]);

        $report = (new SubscriptionInventoryReport)->handle([]);

        $this->assertCount(0, $report);
    }

    #[Test]
    public function it_does_not_include_subscriptions_without_a_ready_at_value(): void
    {
        $product_one = Product::factory()->create(['inventory' => 5, 'title' => 'steak', 'sku' => 'A523']);
        $product_two = Product::factory()->create(['inventory' => 10, 'title' => 'milk', 'sku' => 'A223']);

        $date_one = today()->addDays(4);
        $subscription_one = RecurringOrder::factory()->create(['generate_at' => $date_one, 'ready_at' => null]);
        RecurringOrderItem::factory()->create(['order_id' => $subscription_one->id, 'product_id' => $product_one->id, 'qty' => 5]);
        RecurringOrderItem::factory()->create(['order_id' => $subscription_one->id, 'product_id' => $product_two->id, 'qty' => 3]);

        $report = (new SubscriptionInventoryReport)->handle([]);

        $this->assertCount(0, $report);
    }
}