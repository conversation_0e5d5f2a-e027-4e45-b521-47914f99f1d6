<?php

namespace Tests\Unit\Repositories\Reports;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Pickup;
use App\Models\Schedule;
use App\Repositories\Reports\DeliverySalesRevenueByLocation;
use App\Support\Enums\OrderStatus;
use Illuminate\Support\Carbon;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class DeliverySalesRevenueByLocationTest extends TenantTestCase
{
    #[Test]
    public function it_calculates_totals_by_pickup_location(): void
    {
        $pickup_one = Pickup::factory()->create();
        $order_one = Order::factory()->create(['pickup_id' => $pickup_one->id, 'confirmed' => true, 'canceled' => false]);
        OrderItem::factory(2)->create(['order_id' => $order_one->id, 'subtotal' => 111, 'weight' => 1.111]);

        $pickup_two = Pickup::factory()->create();
        $order_two = Order::factory()->create(['pickup_id' => $pickup_two->id, 'confirmed' => true, 'canceled' => false]);
        OrderItem::factory(2)->create(['order_id' => $order_two->id, 'subtotal' => 333, 'weight' => 3.333]);

        $results = (new DeliverySalesRevenueByLocation)->handle([]);

        $this->assertTrue($results->contains(function ($result) use ($pickup_one) {
            return $result->pickup_id === $pickup_one->id
                && $result->pickup_title === $pickup_one->title
                && $result->order_count === 1
                && $result->subtotal === '222'
                && $result->weight === '2.222';
        }));

        $this->assertTrue($results->contains(function ($result) use ($pickup_two) {
            return $result->pickup_id === $pickup_two->id
                && $result->pickup_title === $pickup_two->title
                && $result->order_count === 1
                && $result->subtotal === '666'
                && $result->weight === '6.666';
        }));
    }

    #[Test]
    public function it_filters_out_non_confirmed_orders_by_default(): void
    {
        $pickup = Pickup::factory()->create();
        $order = Order::factory()->create(['pickup_id' => $pickup->id, 'confirmed' => false, 'canceled' => false, 'status_id' => OrderStatus::confirmed()]);
        OrderItem::factory(2)->create(['order_id' => $order->id, 'subtotal' => 111, 'weight' => 1.111]);
        OrderItem::factory(2)->create(['order_id' => $order->id, 'subtotal' => 333, 'weight' => 3.333]);

        $results = (new DeliverySalesRevenueByLocation)->handle([]);

        $this->assertEmpty($results);
    }

    #[Test]
    public function it_filters_out_canceled_orders_by_default(): void
    {
        $pickup = Pickup::factory()->create();

        $order = Order::factory()->create(['pickup_id' => $pickup->id, 'confirmed' => true, 'canceled' => true, 'status_id' => OrderStatus::confirmed()]);
        OrderItem::factory(2)->create(['order_id' => $order->id, 'subtotal' => 111, 'weight' => 1.111]);
        OrderItem::factory(2)->create(['order_id' => $order->id, 'subtotal' => 333, 'weight' => 3.333]);

        $results = (new DeliverySalesRevenueByLocation)->handle([]);

        $this->assertEmpty($results);
    }

    #[Test]
    public function it_can_filter_by_order_status(): void
    {
        $pickup = Pickup::factory()->create();

        $order = Order::factory()->create(['pickup_id' => $pickup->id, 'confirmed' => true, 'canceled' => false, 'status_id' => OrderStatus::confirmed()]);
        OrderItem::factory(2)->create(['order_id' => $order->id, 'subtotal' => 111, 'weight' => 1.111]);
        OrderItem::factory(2)->create(['order_id' => $order->id, 'subtotal' => 333, 'weight' => 3.333]);

        $results = (new DeliverySalesRevenueByLocation)->handle([]);

        $this->assertTrue($results->contains(function ($result) use ($pickup) {
            return $result->pickup_id === $pickup->id
                && $result->pickup_title === $pickup->title
                && $result->order_count === 1
                && $result->subtotal === '888'
                && $result->weight === '8.888';
        }));

        $results = (new DeliverySalesRevenueByLocation)->handle(['order_status' => [OrderStatus::confirmed()]]);

        $this->assertTrue($results->contains(function ($result) use ($pickup) {
            return $result->pickup_id === $pickup->id
                && $result->pickup_title === $pickup->title
                && $result->order_count === 1
                && $result->subtotal === '888'
                && $result->weight === '8.888';
        }));

        $results = (new DeliverySalesRevenueByLocation)->handle(['order_status' => [OrderStatus::canceled()]]);

        $this->assertEmpty($results);
    }

    #[Test]
    public function it_can_filter_by_confirmed_date(): void
    {
        Carbon::setTestNow(now());

        $pickup = Pickup::factory()->create();

        $order = Order::factory()->create(['pickup_id' => $pickup->id, 'confirmed' => true, 'canceled' => false, 'confirmed_date' => today()]);
        OrderItem::factory(2)->create(['order_id' => $order->id, 'subtotal' => 111, 'weight' => 1.111]);
        OrderItem::factory(2)->create(['order_id' => $order->id, 'subtotal' => 333, 'weight' => 3.333]);

        $results = (new DeliverySalesRevenueByLocation)->handle([]);

        $this->assertTrue($results->contains(function ($result) use ($pickup) {
            return $result->pickup_id === $pickup->id
                && $result->pickup_title === $pickup->title
                && $result->order_count === 1
                && $result->subtotal === '888'
                && $result->weight === '8.888';
        }));

        $results = (new DeliverySalesRevenueByLocation)->handle(['confirmed_date' => ['start' => today()]]);

        $this->assertTrue($results->contains(function ($result) use ($pickup) {
            return $result->pickup_id === $pickup->id
                && $result->pickup_title === $pickup->title
                && $result->order_count === 1
                && $result->subtotal === '888'
                && $result->weight === '8.888';
        }));

        $results = (new DeliverySalesRevenueByLocation)->handle(['confirmed_date' => ['end' => today()]]);

        $this->assertTrue($results->contains(function ($result) use ($pickup) {
            return $result->pickup_id === $pickup->id
                && $result->pickup_title === $pickup->title
                && $result->order_count === 1
                && $result->subtotal === '888'
                && $result->weight === '8.888';
        }));

        $results = (new DeliverySalesRevenueByLocation)->handle(['confirmed_date' => ['start' => today()->addDay()]]);

        $this->assertEmpty($results);

        $results = (new DeliverySalesRevenueByLocation)->handle(['confirmed_date' => ['end' => today()->subDay()]]);

        $this->assertEmpty($results);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_can_filter_by_pickup_date(): void
    {
        Carbon::setTestNow(now());

        $pickup = Pickup::factory()->create();

        $order = Order::factory()->create(['pickup_id' => $pickup->id, 'confirmed' => true, 'canceled' => false, 'pickup_date' => today()]);
        OrderItem::factory(2)->create(['order_id' => $order->id, 'subtotal' => 111, 'weight' => 1.111]);
        OrderItem::factory(2)->create(['order_id' => $order->id, 'subtotal' => 333, 'weight' => 3.333]);

        $results = (new DeliverySalesRevenueByLocation)->handle([]);

        $this->assertTrue($results->contains(function ($result) use ($pickup) {
            return $result->pickup_id === $pickup->id
                && $result->pickup_title === $pickup->title
                && $result->order_count === 1
                && $result->subtotal === '888'
                && $result->weight === '8.888';
        }));

        $results = (new DeliverySalesRevenueByLocation)->handle(['pickup_date' => ['start' => today()]]);

        $this->assertTrue($results->contains(function ($result) use ($pickup) {
            return $result->pickup_id === $pickup->id
                && $result->pickup_title === $pickup->title
                && $result->order_count === 1
                && $result->subtotal === '888'
                && $result->weight === '8.888';
        }));

        $results = (new DeliverySalesRevenueByLocation)->handle(['pickup_date' => ['end' => today()]]);

        $this->assertTrue($results->contains(function ($result) use ($pickup) {
            return $result->pickup_id === $pickup->id
                && $result->pickup_title === $pickup->title
                && $result->order_count === 1
                && $result->subtotal === '888'
                && $result->weight === '8.888';
        }));

        $results = (new DeliverySalesRevenueByLocation)->handle(['pickup_date' => ['start' => today()->addDay()]]);

        $this->assertEmpty($results);

        $results = (new DeliverySalesRevenueByLocation)->handle(['pickup_date' => ['end' => today()->subDay()]]);

        $this->assertEmpty($results);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_can_filter_by_first_time_order_status(): void
    {
        $pickup = Pickup::factory()->create();

        $order = Order::factory()->create(['pickup_id' => $pickup->id, 'confirmed' => true, 'canceled' => false, 'first_time_order' => true]);
        OrderItem::factory(2)->create(['order_id' => $order->id, 'subtotal' => 111, 'weight' => 1.111]);
        OrderItem::factory(2)->create(['order_id' => $order->id, 'subtotal' => 333, 'weight' => 3.333]);

        $results = (new DeliverySalesRevenueByLocation)->handle([]);

        $this->assertTrue($results->contains(function ($result) use ($pickup) {
            return $result->pickup_id === $pickup->id
                && $result->pickup_title === $pickup->title
                && $result->order_count === 1
                && $result->subtotal === '888'
                && $result->weight === '8.888';
        }));

        $results = (new DeliverySalesRevenueByLocation)->handle(['first_time_order' => true]);

        $this->assertTrue($results->contains(function ($result) use ($pickup) {
            return $result->pickup_id === $pickup->id
                && $result->pickup_title === $pickup->title
                && $result->order_count === 1
                && $result->subtotal === '888'
                && $result->weight === '8.888';
        }));

        $results = (new DeliverySalesRevenueByLocation)->handle(['first_time_order' => false]);

        $this->assertEmpty($results);
    }

    #[Test]
    public function it_can_filter_by_fulfillment_error_status(): void
    {
        $pickup = Pickup::factory()->create();

        $order = Order::factory()->create(['pickup_id' => $pickup->id, 'confirmed' => true, 'canceled' => false, 'fulfillment_error' => true]);
        OrderItem::factory(2)->create(['order_id' => $order->id, 'subtotal' => 111, 'weight' => 1.111]);
        OrderItem::factory(2)->create(['order_id' => $order->id, 'subtotal' => 333, 'weight' => 3.333]);

        $results = (new DeliverySalesRevenueByLocation)->handle([]);

        $this->assertTrue($results->contains(function ($result) use ($pickup) {
            return $result->pickup_id === $pickup->id
                && $result->pickup_title === $pickup->title
                && $result->order_count === 1
                && $result->subtotal === '888'
                && $result->weight === '8.888';
        }));

        $results = (new DeliverySalesRevenueByLocation)->handle(['fulfillment_error' => true]);

        $this->assertTrue($results->contains(function ($result) use ($pickup) {
            return $result->pickup_id === $pickup->id
                && $result->pickup_title === $pickup->title
                && $result->order_count === 1
                && $result->subtotal === '888'
                && $result->weight === '8.888';
        }));

        $results = (new DeliverySalesRevenueByLocation)->handle(['fulfillment_error' => false]);

        $this->assertEmpty($results);
    }

    #[Test]
    public function it_can_filter_by_sales_channel(): void
    {
        $pickup = Pickup::factory()->create();

        $order = Order::factory()->create(['pickup_id' => $pickup->id, 'confirmed' => true, 'canceled' => false, 'type_id' => 1]);
        OrderItem::factory(2)->create(['order_id' => $order->id, 'subtotal' => 111, 'weight' => 1.111]);
        OrderItem::factory(2)->create(['order_id' => $order->id, 'subtotal' => 333, 'weight' => 3.333]);

        $results = (new DeliverySalesRevenueByLocation)->handle([]);

        $this->assertTrue($results->contains(function ($result) use ($pickup) {
            return $result->pickup_id === $pickup->id
                && $result->pickup_title === $pickup->title
                && $result->order_count === 1
                && $result->subtotal === '888'
                && $result->weight === '8.888';
        }));

        $results = (new DeliverySalesRevenueByLocation)->handle(['order_type_id' => [1]]);

        $this->assertTrue($results->contains(function ($result) use ($pickup) {
            return $result->pickup_id === $pickup->id
                && $result->pickup_title === $pickup->title
                && $result->order_count === 1
                && $result->subtotal === '888'
                && $result->weight === '8.888';
        }));

        $results = (new DeliverySalesRevenueByLocation)->handle(['order_type_id' => [2]]);

        $this->assertEmpty($results);
    }

    #[Test]
    public function it_can_filter_by_schedule(): void
    {
        $schedule = Schedule::factory()->create();
        $other_schedule = Schedule::factory()->create();
        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);

        $order = Order::factory()->create(['pickup_id' => $pickup->id, 'confirmed' => true, 'canceled' => false]);
        OrderItem::factory(2)->create(['order_id' => $order->id, 'subtotal' => 111, 'weight' => 1.111]);
        OrderItem::factory(2)->create(['order_id' => $order->id, 'subtotal' => 333, 'weight' => 3.333]);

        $results = (new DeliverySalesRevenueByLocation)->handle([]);

        $this->assertTrue($results->contains(function ($result) use ($pickup) {
            return $result->pickup_id === $pickup->id
                && $result->pickup_title === $pickup->title
                && $result->order_count === 1
                && $result->subtotal === '888'
                && $result->weight === '8.888';
        }));

        $results = (new DeliverySalesRevenueByLocation)->handle(['schedule_id' => [$schedule->id]]);

        $this->assertTrue($results->contains(function ($result) use ($pickup) {
            return $result->pickup_id === $pickup->id
                && $result->pickup_title === $pickup->title
                && $result->order_count === 1
                && $result->subtotal === '888'
                && $result->weight === '8.888';
        }));

        $results = (new DeliverySalesRevenueByLocation)->handle(['schedule_id' => [$other_schedule->id]]);

        $this->assertEmpty($results);
    }
}
