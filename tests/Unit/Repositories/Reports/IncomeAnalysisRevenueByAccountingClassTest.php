<?php

namespace Tests\Unit\Repositories\Reports;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Payment;
use App\Models\Product;
use App\Repositories\Reports\IncomeAnalysisRevenueByAccountingClass;
use App\Support\Enums\OrderStatus;
use Illuminate\Support\Carbon;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class IncomeAnalysisRevenueByAccountingClassTest extends TenantTestCase
{
    #[Test]
    public function it_calculates_summary_totals_by_accounting_class(): void
    {
        $product_one = Product::factory()->create(['accounting_class' => 'test one']);
        $product_two = Product::factory()->create(['accounting_class' => 'test two']);

        $order = Order::factory()->create(['confirmed' => true, 'canceled' => false]);
        OrderItem::factory(2)->create(['order_id' => $order->id, 'product_id' => $product_one->id, 'subtotal' => 111, 'weight' => 1.111]);
        OrderItem::factory(2)->create(['order_id' => $order->id, 'product_id' => $product_two->id, 'subtotal' => 333, 'weight' => 3.333]);

        $results = (new IncomeAnalysisRevenueByAccountingClass)->handle([]);

        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'test one' && $result->total === '222' && $result->weight === '2.222'));
        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'test two' && $result->total === '666' && $result->weight === '6.666'));
    }

    #[Test]
    public function it_sums_online_store_channel_totals_by_accounting_class(): void
    {
        $product_one = Product::factory()->create(['accounting_class' => 'test one']);
        $product_two = Product::factory()->create(['accounting_class' => 'test two']);

        $order = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'type_id' => 1]);
        OrderItem::factory(2)->create(['order_id' => $order->id, 'product_id' => $product_one->id, 'subtotal' => 111, 'weight' => 1.111]);
        OrderItem::factory(2)->create(['order_id' => $order->id, 'product_id' => $product_two->id, 'subtotal' => 333, 'weight' => 3.333]);

        $results = (new IncomeAnalysisRevenueByAccountingClass)->handle([], true);

        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'test one' && $result->channel_online_store_weight === '2.222' && $result->channel_online_store_total === '222' && $result->channel_combined_weight === '2.222' && $result->channel_combined_total === '222'));
        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'test two' && $result->channel_online_store_weight === '6.666' && $result->channel_online_store_total === '666' && $result->channel_combined_weight === '6.666' && $result->channel_combined_total === '666'));
    }

    #[Test]
    public function it_sums_wholesale_channel_totals_by_accounting_class(): void
    {
        $product_one = Product::factory()->create(['accounting_class' => 'test one']);
        $product_two = Product::factory()->create(['accounting_class' => 'test two']);

        $order = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'type_id' => 2]);
        OrderItem::factory(2)->create(['order_id' => $order->id, 'product_id' => $product_one->id, 'subtotal' => 111, 'weight' => 1.111]);
        OrderItem::factory(2)->create(['order_id' => $order->id, 'product_id' => $product_two->id, 'subtotal' => 333, 'weight' => 3.333]);

        $results = (new IncomeAnalysisRevenueByAccountingClass)->handle([], true);

        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'test one' && $result->channel_wholesale_weight === '2.222' && $result->channel_wholesale_total === '222' && $result->channel_combined_weight === '2.222' && $result->channel_combined_total === '222'));
        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'test two' && $result->channel_wholesale_weight === '6.666' && $result->channel_wholesale_total === '666' && $result->channel_combined_weight === '6.666' && $result->channel_combined_total === '666'));
    }

    #[Test]
    public function it_sums_distribution_channel_totals_by_accounting_class(): void
    {
        $product_one = Product::factory()->create(['accounting_class' => 'test one']);
        $product_two = Product::factory()->create(['accounting_class' => 'test two']);

        $order = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'type_id' => 3]);
        OrderItem::factory(2)->create(['order_id' => $order->id, 'product_id' => $product_one->id, 'subtotal' => 111, 'weight' => 1.111]);
        OrderItem::factory(2)->create(['order_id' => $order->id, 'product_id' => $product_two->id, 'subtotal' => 333, 'weight' => 3.333]);

        $results = (new IncomeAnalysisRevenueByAccountingClass)->handle([], true);

        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'test one' && $result->channel_distribution_weight === '2.222' && $result->channel_distribution_total === '222' && $result->channel_combined_weight === '2.222' && $result->channel_combined_total === '222'));
        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'test two' && $result->channel_distribution_weight === '6.666' && $result->channel_distribution_total === '666' && $result->channel_combined_weight === '6.666' && $result->channel_combined_total === '666'));
    }

    #[Test]
    public function it_sums_farm_store_channel_totals_by_accounting_class(): void
    {
        $product_one = Product::factory()->create(['accounting_class' => 'test one']);
        $product_two = Product::factory()->create(['accounting_class' => 'test two']);

        $order = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'type_id' => 4]);
        OrderItem::factory(2)->create(['order_id' => $order->id, 'product_id' => $product_one->id, 'subtotal' => 111, 'weight' => 1.111]);
        OrderItem::factory(2)->create(['order_id' => $order->id, 'product_id' => $product_two->id, 'subtotal' => 333, 'weight' => 3.333]);

        $results = (new IncomeAnalysisRevenueByAccountingClass)->handle([], true);

        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'test one' && $result->channel_farm_store_weight === '2.222' && $result->channel_farm_store_total === '222' && $result->channel_combined_weight === '2.222' && $result->channel_combined_total === '222'));
        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'test two' && $result->channel_farm_store_weight === '6.666' && $result->channel_farm_store_total === '666' && $result->channel_combined_weight === '6.666' && $result->channel_combined_total === '666'));
    }

    #[Test]
    public function it_sums_farmers_market_channel_totals_by_accounting_class(): void
    {
        $product_one = Product::factory()->create(['accounting_class' => 'test one']);
        $product_two = Product::factory()->create(['accounting_class' => 'test two']);

        $order = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'type_id' => 5]);
        OrderItem::factory(2)->create(['order_id' => $order->id, 'product_id' => $product_one->id, 'subtotal' => 111, 'weight' => 1.111]);
        OrderItem::factory(2)->create(['order_id' => $order->id, 'product_id' => $product_two->id, 'subtotal' => 333, 'weight' => 3.333]);

        $results = (new IncomeAnalysisRevenueByAccountingClass)->handle([], true);

        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'test one' && $result->channel_farmers_market_weight === '2.222' && $result->channel_farmers_market_total === '222' && $result->channel_combined_weight === '2.222' && $result->channel_combined_total === '222'));
        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'test two' && $result->channel_farmers_market_weight === '6.666' && $result->channel_farmers_market_total === '666' && $result->channel_combined_weight === '6.666' && $result->channel_combined_total === '666'));
    }

    #[Test]
    public function it_sums_affiliate_channel_totals_by_accounting_class(): void
    {
        $product_one = Product::factory()->create(['accounting_class' => 'test one']);
        $product_two = Product::factory()->create(['accounting_class' => 'test two']);

        $order = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'type_id' => 6]);
        OrderItem::factory(2)->create(['order_id' => $order->id, 'product_id' => $product_one->id, 'subtotal' => 111, 'weight' => 1.111]);
        OrderItem::factory(2)->create(['order_id' => $order->id, 'product_id' => $product_two->id, 'subtotal' => 333, 'weight' => 3.333]);

        $results = (new IncomeAnalysisRevenueByAccountingClass)->handle([], true);

        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'test one' && $result->channel_affiliate_weight === '2.222' && $result->channel_affiliate_total === '222' && $result->channel_combined_weight === '2.222' && $result->channel_combined_total === '222'));
        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'test two' && $result->channel_affiliate_weight === '6.666' && $result->channel_affiliate_total === '666' && $result->channel_combined_weight === '6.666' && $result->channel_combined_total === '666'));
    }

    #[Test]
    public function it_sums_buying_clubs_channel_totals_by_accounting_class(): void
    {
        $product_one = Product::factory()->create(['accounting_class' => 'test one']);
        $product_two = Product::factory()->create(['accounting_class' => 'test two']);

        $order = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'type_id' => 7]);
        OrderItem::factory(2)->create(['order_id' => $order->id, 'product_id' => $product_one->id, 'subtotal' => 111, 'weight' => 1.111]);
        OrderItem::factory(2)->create(['order_id' => $order->id, 'product_id' => $product_two->id, 'subtotal' => 333, 'weight' => 3.333]);

        $results = (new IncomeAnalysisRevenueByAccountingClass)->handle([], true);

        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'test one' && $result->channel_buying_clubs_weight === '2.222' && $result->channel_buying_clubs_total === '222' && $result->channel_combined_weight === '2.222' && $result->channel_combined_total === '222'));
        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'test two' && $result->channel_buying_clubs_weight === '6.666' && $result->channel_buying_clubs_total === '666' && $result->channel_combined_weight === '6.666' && $result->channel_combined_total === '666'));
    }

    #[Test]
    public function it_sums_home_delivery_channel_totals_by_accounting_class(): void
    {
        $product_one = Product::factory()->create(['accounting_class' => 'test one']);
        $product_two = Product::factory()->create(['accounting_class' => 'test two']);

        $order = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'type_id' => 8]);
        OrderItem::factory(2)->create(['order_id' => $order->id, 'product_id' => $product_one->id, 'subtotal' => 111, 'weight' => 1.111]);
        OrderItem::factory(2)->create(['order_id' => $order->id, 'product_id' => $product_two->id, 'subtotal' => 333, 'weight' => 3.333]);

        $results = (new IncomeAnalysisRevenueByAccountingClass)->handle([], true);

        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'test one' && $result->channel_home_delivery_weight === '2.222' && $result->channel_home_delivery_total === '222' && $result->channel_combined_weight === '2.222' && $result->channel_combined_total === '222'));
        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'test two' && $result->channel_home_delivery_weight === '6.666' && $result->channel_home_delivery_total === '666' && $result->channel_combined_weight === '6.666' && $result->channel_combined_total === '666'));
    }

    #[Test]
    public function it_sums_shipping_channel_totals_by_accounting_class(): void
    {
        $product_one = Product::factory()->create(['accounting_class' => 'test one']);
        $product_two = Product::factory()->create(['accounting_class' => 'test two']);

        $order = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'type_id' => 9]);
        OrderItem::factory(2)->create(['order_id' => $order->id, 'product_id' => $product_one->id, 'subtotal' => 111, 'weight' => 1.111]);
        OrderItem::factory(2)->create(['order_id' => $order->id, 'product_id' => $product_two->id, 'subtotal' => 333, 'weight' => 3.333]);

        $results = (new IncomeAnalysisRevenueByAccountingClass)->handle([], true);

        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'test one' && $result->channel_shipping_weight === '2.222' && $result->channel_shipping_total === '222' && $result->channel_combined_weight === '2.222' && $result->channel_combined_total === '222'));
        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'test two' && $result->channel_shipping_weight === '6.666' && $result->channel_shipping_total === '666' && $result->channel_combined_weight === '6.666' && $result->channel_combined_total === '666'));
    }

    #[Test]
    public function it_sums_pos_channel_totals_by_accounting_class(): void
    {
        $product_one = Product::factory()->create(['accounting_class' => 'test one']);
        $product_two = Product::factory()->create(['accounting_class' => 'test two']);

        $order = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'type_id' => 10]);
        OrderItem::factory(2)->create(['order_id' => $order->id, 'product_id' => $product_one->id, 'subtotal' => 111, 'weight' => 1.111]);
        OrderItem::factory(2)->create(['order_id' => $order->id, 'product_id' => $product_two->id, 'subtotal' => 333, 'weight' => 3.333]);

        $results = (new IncomeAnalysisRevenueByAccountingClass)->handle([], true);

        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'test one' && $result->channel_pos_weight === '2.222' && $result->channel_pos_total === '222' && $result->channel_combined_weight === '2.222' && $result->channel_combined_total === '222'));
        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'test two' && $result->channel_pos_weight === '6.666' && $result->channel_pos_total === '666' && $result->channel_combined_weight === '6.666' && $result->channel_combined_total === '666'));
    }

    #[Test]
    public function it_filters_out_non_confirmed_orders_by_default(): void
    {
        $product_one = Product::factory()->create(['accounting_class' => 'test one']);
        $product_two = Product::factory()->create(['accounting_class' => 'test two']);

        $order = Order::factory()->create(['confirmed' => false, 'canceled' => false, 'status_id' => OrderStatus::confirmed()]);
        OrderItem::factory(2)->create(['order_id' => $order->id, 'product_id' => $product_one->id, 'subtotal' => 111, 'weight' => 1.111]);
        OrderItem::factory(2)->create(['order_id' => $order->id, 'product_id' => $product_two->id, 'subtotal' => 333, 'weight' => 3.333]);

        $results = (new IncomeAnalysisRevenueByAccountingClass)->handle([]);

        $this->assertTrue($results->doesntContain(fn($result) => $result->accounting_class === 'test one' && $result->total === '222' && $result->weight === '2.222'));
        $this->assertTrue($results->doesntContain(fn($result) => $result->accounting_class === 'test two' && $result->total === '666' && $result->weight === '6.666'));
    }

    #[Test]
    public function it_filters_out_canceled_orders_by_default(): void
    {
        $product_one = Product::factory()->create(['accounting_class' => 'test one']);
        $product_two = Product::factory()->create(['accounting_class' => 'test two']);

        $order = Order::factory()->create(['confirmed' => true, 'canceled' => true, 'status_id' => OrderStatus::confirmed()]);
        OrderItem::factory(2)->create(['order_id' => $order->id, 'product_id' => $product_one->id, 'subtotal' => 111, 'weight' => 1.111]);
        OrderItem::factory(2)->create(['order_id' => $order->id, 'product_id' => $product_two->id, 'subtotal' => 333, 'weight' => 3.333]);

        $results = (new IncomeAnalysisRevenueByAccountingClass)->handle([]);

        $this->assertTrue($results->doesntContain(fn($result) => $result->accounting_class === 'test one' && $result->total === '222' && $result->weight === '2.222'));
        $this->assertTrue($results->doesntContain(fn($result) => $result->accounting_class === 'test two' && $result->total === '666' && $result->weight === '6.666'));
    }

    #[Test]
    public function it_can_filter_by_order_status(): void
    {
        $product_one = Product::factory()->create(['accounting_class' => 'test one']);
        $product_two = Product::factory()->create(['accounting_class' => 'test two']);

        $order = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'status_id' => OrderStatus::confirmed()]);
        OrderItem::factory(2)->create(['order_id' => $order->id, 'product_id' => $product_one->id, 'subtotal' => 111, 'weight' => 1.111]);
        OrderItem::factory(2)->create(['order_id' => $order->id, 'product_id' => $product_two->id, 'subtotal' => 333, 'weight' => 3.333]);

        $results = (new IncomeAnalysisRevenueByAccountingClass)->handle([]);

        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'test one' && $result->total === '222' && $result->weight === '2.222'));
        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'test two' && $result->total === '666' && $result->weight === '6.666'));

        $results = (new IncomeAnalysisRevenueByAccountingClass)->handle(['order_status' => [OrderStatus::confirmed()]]);

        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'test one' && $result->total === '222' && $result->weight === '2.222'));
        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'test two' && $result->total === '666' && $result->weight === '6.666'));

        $results = (new IncomeAnalysisRevenueByAccountingClass)->handle(['order_status' => [OrderStatus::canceled()]]);

        $this->assertTrue($results->doesntContain(fn($result) => $result->accounting_class === 'test one' && $result->total === '222' && $result->weight === '2.222'));
        $this->assertTrue($results->doesntContain(fn($result) => $result->accounting_class === 'test two' && $result->total === '666' && $result->weight === '6.666'));
    }

    #[Test]
    public function it_can_filter_by_payment_date(): void
    {
        Carbon::setTestNow(now());

        $product_one = Product::factory()->create(['accounting_class' => 'test one']);
        $product_two = Product::factory()->create(['accounting_class' => 'test two']);

        $order = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'payment_date' => today()->addHour()]);
        OrderItem::factory(2)->create(['order_id' => $order->id, 'product_id' => $product_one->id, 'subtotal' => 111, 'weight' => 1.111]);
        OrderItem::factory(2)->create(['order_id' => $order->id, 'product_id' => $product_two->id, 'subtotal' => 333, 'weight' => 3.333]);

        $results = (new IncomeAnalysisRevenueByAccountingClass)->handle([]);

        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'test one' && $result->total === '222' && $result->weight === '2.222'));
        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'test two' && $result->total === '666' && $result->weight === '6.666'));

        $results = (new IncomeAnalysisRevenueByAccountingClass)->handle(['date_type' => 'payment_date', 'date_range' => ['start' => today()->format('M jS Y')]]);

        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'test one' && $result->total === '222' && $result->weight === '2.222'));
        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'test two' && $result->total === '666' && $result->weight === '6.666'));

        $results = (new IncomeAnalysisRevenueByAccountingClass)->handle(['date_type' => 'payment_date', 'date_range' => ['end' => today()->format('M jS Y')]]);

        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'test one' && $result->total === '222' && $result->weight === '2.222'));
        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'test two' && $result->total === '666' && $result->weight === '6.666'));

        $results = (new IncomeAnalysisRevenueByAccountingClass)->handle(['date_type' => 'payment_date', 'date_range' => ['start' => today()->format('M jS Y'), 'end' => today()->format('M jS Y')]]);

        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'test one' && $result->total === '222' && $result->weight === '2.222'));
        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'test two' && $result->total === '666' && $result->weight === '6.666'));

        $results = (new IncomeAnalysisRevenueByAccountingClass)->handle(['date_type' => 'payment_date', 'date_range' => ['start' => today()->addDay()->format('M jS Y')]]);

        $this->assertTrue($results->doesntContain(fn($result) => $result->accounting_class === 'test one' && $result->total === '222' && $result->weight === '2.222'));
        $this->assertTrue($results->doesntContain(fn($result) => $result->accounting_class === 'test two' && $result->total === '666' && $result->weight === '6.666'));

        $results = (new IncomeAnalysisRevenueByAccountingClass)->handle(['date_type' => 'payment_date', 'date_range' => ['end' => today()->subDay()->format('M jS Y')]]);

        $this->assertTrue($results->doesntContain(fn($result) => $result->accounting_class === 'test one' && $result->total === '222' && $result->weight === '2.222'));
        $this->assertTrue($results->doesntContain(fn($result) => $result->accounting_class === 'test two' && $result->total === '666' && $result->weight === '6.666'));

        Carbon::setTestNow();
    }

    #[Test]
    public function it_can_filter_by_pickup_date(): void
    {
        Carbon::setTestNow(now());

        $product_one = Product::factory()->create(['accounting_class' => 'test one']);
        $product_two = Product::factory()->create(['accounting_class' => 'test two']);

        $order = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'pickup_date' => today()]);
        OrderItem::factory(2)->create(['order_id' => $order->id, 'product_id' => $product_one->id, 'subtotal' => 111, 'weight' => 1.111]);
        OrderItem::factory(2)->create(['order_id' => $order->id, 'product_id' => $product_two->id, 'subtotal' => 333, 'weight' => 3.333]);

        $results = (new IncomeAnalysisRevenueByAccountingClass)->handle([]);

        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'test one' && $result->total === '222' && $result->weight === '2.222'));
        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'test two' && $result->total === '666' && $result->weight === '6.666'));

        $results = (new IncomeAnalysisRevenueByAccountingClass)->handle(['date_type' => 'pickup_date', 'date_range' => ['start' => today()]]);

        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'test one' && $result->total === '222' && $result->weight === '2.222'));
        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'test two' && $result->total === '666' && $result->weight === '6.666'));

        $results = (new IncomeAnalysisRevenueByAccountingClass)->handle(['date_type' => 'pickup_date', 'date_range' => ['end' => today()]]);

        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'test one' && $result->total === '222' && $result->weight === '2.222'));
        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'test two' && $result->total === '666' && $result->weight === '6.666'));

        $results = (new IncomeAnalysisRevenueByAccountingClass)->handle(['date_type' => 'pickup_date', 'date_range' => ['start' => today()->addDay()]]);

        $this->assertTrue($results->doesntContain(fn($result) => $result->accounting_class === 'test one' && $result->total === '222' && $result->weight === '2.222'));
        $this->assertTrue($results->doesntContain(fn($result) => $result->accounting_class === 'test two' && $result->total === '666' && $result->weight === '6.666'));

        $results = (new IncomeAnalysisRevenueByAccountingClass)->handle(['date_type' => 'pickup_date', 'date_range' => ['end' => today()->subDay()]]);

        $this->assertTrue($results->doesntContain(fn($result) => $result->accounting_class === 'test one' && $result->total === '222' && $result->weight === '2.222'));
        $this->assertTrue($results->doesntContain(fn($result) => $result->accounting_class === 'test two' && $result->total === '666' && $result->weight === '6.666'));

        Carbon::setTestNow();
    }

    #[Test]
    public function it_can_filter_by_export_status(): void
    {
        $product_one = Product::factory()->create(['accounting_class' => 'test one']);
        $product_two = Product::factory()->create(['accounting_class' => 'test two']);

        $order = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'exported' => true]);
        OrderItem::factory(2)->create(['order_id' => $order->id, 'product_id' => $product_one->id, 'subtotal' => 111, 'weight' => 1.111]);
        OrderItem::factory(2)->create(['order_id' => $order->id, 'product_id' => $product_two->id, 'subtotal' => 333, 'weight' => 3.333]);

        $results = (new IncomeAnalysisRevenueByAccountingClass)->handle([]);

        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'test one' && $result->total === '222' && $result->weight === '2.222'));
        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'test two' && $result->total === '666' && $result->weight === '6.666'));

        $results = (new IncomeAnalysisRevenueByAccountingClass)->handle(['exported' => true]);

        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'test one' && $result->total === '222' && $result->weight === '2.222'));
        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'test two' && $result->total === '666' && $result->weight === '6.666'));

        $results = (new IncomeAnalysisRevenueByAccountingClass)->handle(['exported' => false]);

        $this->assertTrue($results->doesntContain(fn($result) => $result->accounting_class === 'test one' && $result->total === '222' && $result->weight === '2.222'));
        $this->assertTrue($results->doesntContain(fn($result) => $result->accounting_class === 'test two' && $result->total === '666' && $result->weight === '6.666'));
    }

    #[Test]
    public function it_can_filter_by_sales_channel(): void
    {
        $product_one = Product::factory()->create(['accounting_class' => 'test one']);
        $product_two = Product::factory()->create(['accounting_class' => 'test two']);

        $order = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'type_id' => 1]);
        OrderItem::factory(2)->create(['order_id' => $order->id, 'product_id' => $product_one->id, 'subtotal' => 111, 'weight' => 1.111]);
        OrderItem::factory(2)->create(['order_id' => $order->id, 'product_id' => $product_two->id, 'subtotal' => 333, 'weight' => 3.333]);

        $results = (new IncomeAnalysisRevenueByAccountingClass)->handle([]);

        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'test one' && $result->total === '222' && $result->weight === '2.222'));
        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'test two' && $result->total === '666' && $result->weight === '6.666'));

        $results = (new IncomeAnalysisRevenueByAccountingClass)->handle(['order_type_id' => [1]]);

        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'test one' && $result->total === '222' && $result->weight === '2.222'));
        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'test two' && $result->total === '666' && $result->weight === '6.666'));

        $results = (new IncomeAnalysisRevenueByAccountingClass)->handle(['order_type_id' => [2]]);

        $this->assertTrue($results->doesntContain(fn($result) => $result->accounting_class === 'test one' && $result->total === '222' && $result->weight === '2.222'));
        $this->assertTrue($results->doesntContain(fn($result) => $result->accounting_class === 'test two' && $result->total === '666' && $result->weight === '6.666'));
    }

    #[Test]
    public function it_can_filter_by_paid_status(): void
    {
        $product_one = Product::factory()->create(['accounting_class' => 'test one']);
        $product_two = Product::factory()->create(['accounting_class' => 'test two']);

        $order = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'paid' => 1]);
        OrderItem::factory(2)->create(['order_id' => $order->id, 'product_id' => $product_one->id, 'subtotal' => 111, 'weight' => 1.111]);
        OrderItem::factory(2)->create(['order_id' => $order->id, 'product_id' => $product_two->id, 'subtotal' => 333, 'weight' => 3.333]);

        $results = (new IncomeAnalysisRevenueByAccountingClass)->handle([]);

        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'test one' && $result->total === '222' && $result->weight === '2.222'));
        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'test two' && $result->total === '666' && $result->weight === '6.666'));

        $results = (new IncomeAnalysisRevenueByAccountingClass)->handle(['paid' => true]);

        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'test one' && $result->total === '222' && $result->weight === '2.222'));
        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'test two' && $result->total === '666' && $result->weight === '6.666'));

        $results = (new IncomeAnalysisRevenueByAccountingClass)->handle(['paid' => false]);

        $this->assertTrue($results->doesntContain(fn($result) => $result->accounting_class === 'test one' && $result->total === '222' && $result->weight === '2.222'));
        $this->assertTrue($results->doesntContain(fn($result) => $result->accounting_class === 'test two' && $result->total === '666' && $result->weight === '6.666'));
    }

    #[Test]
    public function it_can_filter_by_payment_method(): void
    {
        $product_one = Product::factory()->create(['accounting_class' => 'test one']);
        $product_two = Product::factory()->create(['accounting_class' => 'test two']);

        $payment = Payment::factory()->create();

        $order = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'payment_id' => $payment->id]);
        OrderItem::factory(2)->create(['order_id' => $order->id, 'product_id' => $product_one->id, 'subtotal' => 111, 'weight' => 1.111]);
        OrderItem::factory(2)->create(['order_id' => $order->id, 'product_id' => $product_two->id, 'subtotal' => 333, 'weight' => 3.333]);

        $results = (new IncomeAnalysisRevenueByAccountingClass)->handle([]);

        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'test one' && $result->total === '222' && $result->weight === '2.222'));
        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'test two' && $result->total === '666' && $result->weight === '6.666'));

        $results = (new IncomeAnalysisRevenueByAccountingClass)->handle(['payment_id' => $payment->id]);

        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'test one' && $result->total === '222' && $result->weight === '2.222'));
        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'test two' && $result->total === '666' && $result->weight === '6.666'));

        $results = (new IncomeAnalysisRevenueByAccountingClass)->handle(['payment_id' => 123]);

        $this->assertTrue($results->doesntContain(fn($result) => $result->accounting_class === 'test one' && $result->total === '222' && $result->weight === '2.222'));
        $this->assertTrue($results->doesntContain(fn($result) => $result->accounting_class === 'test two' && $result->total === '666' && $result->weight === '6.666'));
    }

}
