<?php

namespace Tests\Unit\Repositories\Reports\MarketingReview;

use App\Repositories\Reports\MarketingReview\CustomerValueCohort;
use App\Repositories\Reports\MarketingReview\MarketingReview;
use App\Repositories\Reports\MarketingReview\OrdersAndAOV;
use App\Repositories\Reports\MarketingReview\Snapshot;
use App\Repositories\Reports\MarketingReview\Subscriptions;
use App\Repositories\Reports\MarketingReview\UserToCustomerConversion;
use Illuminate\Support\Carbon;
use Mockery\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class MarketingReviewTest extends TenantTestCase
{
    #[Test]
    public function it_returns_the_expected_results(): void
    {
        $anchor_date = today();

        $thirty_day_start_of_month = $anchor_date->copy()->startOfMonth()->subMonthsNoOverflow();
        $thirty_day_end_of_month = $anchor_date->copy()->subMonthsNoOverflow()->endOfMonth();

        $ninety_day_start_of_month = $anchor_date->copy()->startOfMonth()->subMonthsNoOverflow(3);
        $ninety_day_end_of_month = $anchor_date->copy()->subMonthsNoOverflow(3)->endOfMonth();

        $snapshot_results = [
            'end_date' => $thirty_day_end_of_month,
            'active_customers' => 12,
            'average_customer_order_count' => 12.34,
            'average_order_value' => 345,
        ];

        $this->mock(Snapshot::class, function (MockInterface $mock) use ($thirty_day_end_of_month, $snapshot_results) {
            $mock->shouldReceive('handle')->once()
                ->with(\Mockery::on(fn(Carbon $arg) => $arg->format('Y-m-d') === $thirty_day_end_of_month->format('Y-m-d')))
                ->andReturn($snapshot_results);
        });

        $thirty_day_user_to_customer_cvr_results = [
            'register_start_date' => $thirty_day_start_of_month,
            'register_end_date' => $thirty_day_end_of_month,
            'new_user_count' => 12,
            'new_customer_count' => 34,
            'conversion_rate' => 0.71
        ];

        $ninety_day_user_to_customer_cvr_results = [
            'register_start_date' => $ninety_day_start_of_month,
            'register_end_date' => $ninety_day_end_of_month,
            'new_user_count' => 22,
            'new_customer_count' => 44,
            'conversion_rate' => 0.81
        ];

        $this->mock(UserToCustomerConversion::class, function (MockInterface $mock) use (
            $thirty_day_start_of_month, $thirty_day_end_of_month, $thirty_day_user_to_customer_cvr_results,
            $ninety_day_start_of_month, $ninety_day_end_of_month, $ninety_day_user_to_customer_cvr_results
        ) {
            $mock->shouldReceive('handle')->once()
                ->with(
                    \Mockery::on(fn(Carbon $arg) => $arg->format('Y-m-d') === $thirty_day_start_of_month->format('Y-m-d')),
                    \Mockery::on(fn(Carbon $arg) => $arg->format('Y-m-d') === $thirty_day_end_of_month->format('Y-m-d'))
                )
                ->andReturn($thirty_day_user_to_customer_cvr_results);

            $mock->shouldReceive('handle')->once()
                ->with(
                    \Mockery::on(fn(Carbon $arg) => $arg->format('Y-m-d') === $ninety_day_start_of_month->format('Y-m-d')),
                    \Mockery::on(fn(Carbon $arg) => $arg->format('Y-m-d') === $ninety_day_end_of_month->format('Y-m-d'))
                )
                ->andReturn($ninety_day_user_to_customer_cvr_results);
        });

        $orders_and_aov_results = [
            'delivery_start_date' => $thirty_day_start_of_month,
            'delivery_end_date' => $thirty_day_end_of_month,
            'order_count' => 34,
            'average_order_value' => 45,
            'first_time_order_average_order_value' => 567
        ];

        $this->mock(OrdersAndAOV::class, function (MockInterface $mock) use (
            $thirty_day_start_of_month, $thirty_day_end_of_month, $orders_and_aov_results
        ) {
            $mock->shouldReceive('handle')->once()
                ->with(
                    \Mockery::on(fn(Carbon $arg) => $arg->format('Y-m-d') === $thirty_day_start_of_month->format('Y-m-d')),
                    \Mockery::on(fn(Carbon $arg) => $arg->format('Y-m-d') === $thirty_day_end_of_month->format('Y-m-d'))
                )
                ->andReturn($orders_and_aov_results);
        });

        $subscription_results = [
            'start_date' => $thirty_day_start_of_month,
            'end_date' => $thirty_day_end_of_month,
            'total_subscriber_count' => 12,
            'new_subscription_count' => 34,
            'churn_count' => 56,
            'total_order_count' => 90,
            'total_order_value' => 12,
            'subscription_order_count' => 34,
            'subscription_order_value' => 56,
            'subscription_conversion_rate' => 11.33,
            'subscriber_engagement_rate' => 30.12,
            'churn_rate' => 4.52
        ];

        $this->mock(Subscriptions::class, function (MockInterface $mock) use (
            $thirty_day_start_of_month, $thirty_day_end_of_month, $subscription_results
        ) {
            $mock->shouldReceive('handle')->once()
                ->with(
                    \Mockery::on(fn(Carbon $arg) => $arg->format('Y-m-d') === $thirty_day_start_of_month->format('Y-m-d')),
                    \Mockery::on(fn(Carbon $arg) => $arg->format('Y-m-d') === $thirty_day_end_of_month->format('Y-m-d'))
                )
                ->andReturn($subscription_results);
        });

        $ninety_customer_cohort_results = [
            'first_order_confirmed_start_date' => $ninety_day_start_of_month,
            'first_order_confirmed_end_date' => $ninety_day_end_of_month,
            'cohort_size' => 12,
            'average_order_value' => 34,
            'average_customer_order_count' => 5.67,
            'average_customer_value' => 890
        ];

        $twelve_month_start = $anchor_date->copy()->startOfMonth()->subMonthsNoOverflow(13);
        $twelve_month_end = $anchor_date->copy()->subMonthsNoOverflow(13)->endOfMonth();
        $twelve_month_customer_cohort_results = [
            'first_order_confirmed_start_date' => $twelve_month_start,
            'first_order_confirmed_end_date' => $twelve_month_end,
            'cohort_size' => 22,
            'average_order_value' => 44,
            'average_customer_order_count' => 6.67,
            'average_customer_value' => 990
        ];

        $twenty_four_month_start = $anchor_date->copy()->startOfMonth()->subMonthsNoOverflow(25);
        $twenty_four_month_end = $anchor_date->copy()->subMonthsNoOverflow(25)->endOfMonth();
        $twenty_four_month_customer_cohort_results = [
            'first_order_confirmed_start_date' => $twenty_four_month_start,
            'first_order_confirmed_end_date' => $twenty_four_month_end,
            'cohort_size' => 32,
            'average_order_value' => 54,
            'average_customer_order_count' => 7.67,
            'average_customer_value' => 1090
        ];

        $this->mock(CustomerValueCohort::class, function (MockInterface $mock) use (
            $ninety_day_start_of_month, $ninety_day_end_of_month, $ninety_customer_cohort_results,
            $twelve_month_start, $twelve_month_end, $twelve_month_customer_cohort_results,
            $twenty_four_month_start, $twenty_four_month_end, $twenty_four_month_customer_cohort_results,
        ) {
            $mock->shouldReceive('handle')->once()
                ->with(
                    \Mockery::on(fn(Carbon $arg) => $arg->format('Y-m-d') === $ninety_day_start_of_month->format('Y-m-d')),
                    \Mockery::on(fn(Carbon $arg) => $arg->format('Y-m-d') === $ninety_day_end_of_month->format('Y-m-d'))
                )
                ->andReturn($ninety_customer_cohort_results);

            $mock->shouldReceive('handle')->once()
                ->with(
                    \Mockery::on(fn(Carbon $arg) => $arg->format('Y-m-d') === $twelve_month_start->format('Y-m-d')),
                    \Mockery::on(fn(Carbon $arg) => $arg->format('Y-m-d') === $twelve_month_end->format('Y-m-d'))
                )
                ->andReturn($twelve_month_customer_cohort_results);

            $mock->shouldReceive('handle')->once()
                ->with(
                    \Mockery::on(fn(Carbon $arg) => $arg->format('Y-m-d') === $twenty_four_month_start->format('Y-m-d')),
                    \Mockery::on(fn(Carbon $arg) => $arg->format('Y-m-d') === $twenty_four_month_end->format('Y-m-d'))
                )
                ->andReturn($twenty_four_month_customer_cohort_results);
        });

        $results = (new MarketingReview)->handle($anchor_date);

        $this->assertEquals([
            'review_date' => $thirty_day_end_of_month,
            'snapshot' => $snapshot_results,
            '30_day_user_to_customer_cvr' => $thirty_day_user_to_customer_cvr_results,
            '90_day_user_to_customer_cvr' => $ninety_day_user_to_customer_cvr_results,
            'orders_and_aov' => $orders_and_aov_results,
            'subscriptions' => $subscription_results,
            'customer_90_day_value_cohort' => $ninety_customer_cohort_results,
            'customer_12_month_value_cohort' => $twelve_month_customer_cohort_results,
            'customer_24_month_value_cohort' => $twenty_four_month_customer_cohort_results,
        ], $results);
    }
}
