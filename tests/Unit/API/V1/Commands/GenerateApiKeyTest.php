<?php

namespace Tests\Unit\API\V1\Commands;

use App\Models\ApiKey;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class GenerateApiKeyTest extends TestCase
{
    #[Test]
    public function it_can_generate_an_api_key(): void
    {
        $this->artisan("api:generate-key")
            ->assertExitCode(0);

        $this->assertDatabaseHas(ApiKey::class, [
            'name' => 'Test Key'
        ]);
    }
}
