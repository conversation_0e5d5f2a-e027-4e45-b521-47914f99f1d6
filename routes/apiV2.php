<?php

use App\API\V2\Controllers\CurrentUserController;
use App\API\V2\Controllers\CustomerController;
use App\API\V2\Controllers\DeliveryMethodController;
use App\API\V2\Controllers\HeartbeatController;
use App\API\V2\Controllers\OrderController;
use App\API\V2\Controllers\SubscriptionController;
use App\API\V2\Controllers\WebhookController;
use App\API\V2\Controllers\SalesChannelController;

Route::apiResource('customers', CustomerController::class)->only(['index', 'show']);

Route::apiResource('delivery-methods', DeliveryMethodController::class)->only(['index', 'show']);

Route::get('heartbeat', HeartbeatController::class)->name('heartbeat.show');

Route::apiResource('orders', OrderController::class)->only(['index', 'show']);

Route::apiResource('sales-channels', SalesChannelController::class)->only(['index']);

Route::apiResource('subscriptions', SubscriptionController::class)->only(['index', 'show']);

Route::get('users/current', CurrentUserController::class)->name('users.current.show');

Route::apiResource('webhooks', WebhookController::class)->only(['store', 'destroy']);
