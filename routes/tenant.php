<?php

declare(strict_types=1);

use App\API\V1\Middleware\Authenticate;
use App\Http\Controllers\Theme\NewsletterController;
use App\Http\Controllers\Theme\Pages\PageStyleController;
use App\Http\Controllers\Theme\ThemeStylesController;
use App\Http\Controllers\Theme\ThemeStyleVariablesController;
use App\Http\Controllers\Webhooks\TwilioNoReplyWebhookController;
use App\Http\Middleware\EnableJsonResourceWrapping;
use App\Http\Middleware\Theme\IdentifyGuestShopper;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Tenant Routes
|--------------------------------------------------------------------------
|
| Here you can register the tenant routes for your application.
| These routes are loaded by the TenantRouteServiceProvider.
|
| Feel free to customize them however you want. Good luck!
|
*/

Route::prefix('/api/v1')
    ->middleware([
        Authenticate::class,
        'throttle:100,1',
        EnableJsonResourceWrapping::class,
    ])
    ->group(base_path('app/API/V1/routes.php'));

Route::prefix('api/v2')
    ->as('api.v2.')
    ->middleware([
        'auth:sanctum',
        'throttle:100,1',
        \App\Http\Middleware\EnableJsonResourceWrapping::class,
        \Illuminate\Routing\Middleware\SubstituteBindings::class,
    ])
    ->group(base_path('routes/apiV2.php'));

Route::middleware(['web', 'tenant', EnableJsonResourceWrapping::class])->group(function () {

    Route::get('/twilio/webhook/no-reply', TwilioNoReplyWebhookController::class)
        ->name('twilio.webhook.no-reply');

    require base_path('routes/api.php');
    require base_path('routes/admin.php');
    require base_path('app/PickupManager/PickupManagerRoutes.php');

    Route::middleware(['css', 'tenant', IdentifyGuestShopper::class])
        ->group(function () {
            Route::get('/theme/theme.css', [ThemeStylesController::class, 'get']);
            Route::get('/theme/theme-variables.css', ThemeStyleVariablesController::class)->name('css.theme-variables.show');
            Route::get('/theme/pages/{page}/page.css', PageStyleController::class)->name('theme.pages.styles.show');
        });

    Route::middleware('webhook')
        ->group(base_path('routes/webhooks.php'));

    Route::middleware('webCsrfExempt')
        ->group(function () {
            Route::post('/newsletter', [NewsletterController::class, 'store'])->name('theme.newsletter.store');
        });

    Route::middleware(['detour'])
        ->group(base_path('routes/theme.php'));
});
