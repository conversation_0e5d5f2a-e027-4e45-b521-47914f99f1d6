.checkoutHeader {
	text-align: center;
	padding: (@white-space / 8) 0;
	font-size: @font-size-h3;
	background-color: #fafafa;
	border-bottom: solid 1px #eee;
}

.checkoutHeader__container {
	max-width: @site-width;
	padding: 0 (@white-space / 2);
	margin: 0 auto;
	color: inherit;
}

.checkoutHeader__brand {
	padding: 3px;
	display: inline-block;
	border-radius: 3px;
	vertical-align: middle;
	position: absolute;
	left: (@white-space / 2);
	top: (@white-space / 2);
}

.checkoutHeader__logo {
	max-height: 150px - (@white-space / 2);
	max-width: 248px;
	padding: 0;
}

.checkoutHeader__left {
	display: inline-block;
	width: 25%;
	text-align: left;
	vertical-align: middle;
	a {
		color: inherit;
	}
}

.checkoutHeader__center {
	display: inline-block;
	width: 50%;
	text-align: center;
	vertical-align: middle;
}

.checkoutHeader__right {
	display: inline-block;
	width: 25%;
	text-align: right;
	vertical-align: middle;
}

.checkoutHeader__center--mobile {
	display: none;
}

.checkout_stepContainer {
	max-width: 600px;
	width: 100%;
	margin: 0 auto;
}

.confirmation__step-title {
	font-weight: bolder;
	margin-bottom: (@white-space / 10);
	font-size: 1.10em;
}

.checkout_stepHeader {
	font-weight: bolder;
	margin-bottom: (@white-space / 4);
	padding-bottom: (@white-space / 8);
	font-size: 1.10em;
	border-bottom: solid 1px #EEE;
}

.checkoutSteps {
	margin-bottom: 0;
	font-size: 14px;
	padding: (@white-space / 10) (@white-space / 12);
	color: #aaa;
	border-radius: 0px;
	a {
		color: #aaa;
	}
	> li {
		vertical-align: middle;
	    + li:before {
	    	padding: 0 (@white-space / 8);
	    	display: inline-block;
	    	font-size: 11px;
	    	// font-weight: lighter;
	    	font-family: 'Font Awesome 5 Free';
	    	content: "/";
	    	vertical-align: middle;
	    }
  	}
}

.checkoutPage_formFields {
	width: 74%;
	padding-right: 30px;
	display: inline-block;
	vertical-align: top;
}

.checkoutPage_summaryContainer {
	width: 25%;
	display: inline-block;
	vertical-align: top;
}

.list-checkout {
	li {
		margin-bottom: 6px;
	}
}

.checkout_notes {
	padding: @white-space / 4;
	margin: @white-space / 4 0 0 0;
	background-color: #fafafa;
}

.checkout_summaryTable {
	border: none;
	border-collapse: collapse;
	width: 100%;
	tr {
		border-bottom: solid 1px #fafafa;
		padding: (@white-space / 10) 0;
	}
	tr:last-of-type {
		border-bottom: none;
	}
	td {
		padding: (@white-space / 10) 0;
	}
}

.checkout_orderTotal td {
	border-top: solid 1px #eee;
	font-weight: bolder;
	font-size: 1.25em;
	padding: (@white-space / 4) 0 0 0;
}

.checkoutPage__form {
	position: relative;
}

.checkout__paymentOptions {
	margin: 0 -12px (@white-space / 4) 0;
	text-align: center;
}

.checkout__paymentOption {
  	display: inline-block;
  	padding: 0 12px 0 0;
}

.checkout__paymentOption--1 {
	width: 100%;
}

.checkout__paymentOption--2 {
	width: 50%;
}

.checkout__paymentOption--3 {
	width: 33.33333%;
}

.checkout__paymentOptionTitle {
	display: inline-block;
	vertical-align: middle;
	color: #5d5d5d;
	vertical-align: middle;
	transition: all ease-in-out 0.25s;
}

.checkout__paymentOptionTitleSpacer {
	display: block;
	font-size: 13px;
	color: #999;
}

.checkout__paymentOptionIcon {
	display: block !important;
	margin: 0 auto 3px 0;
	font-size: 1.5em !important;
	color: #6d6d6d;
}

.checkout__paymentOptionLabel {
	background-color: #fafafa;
	width: 100%;
	height: 100%;
	padding: 12px 6px;
	border-radius: 6px;
	border: solid 1px #eee;
  	text-align: center;
  	vertical-align: middle;
  	transition: all ease-in-out 0.25s;
  	input[type="radio"] {
  		vertical-align: middle;
  		margin: 3px auto;
  		display: block;
  	}
}

.checkout__paymentOptionLabel.active {
	background-color: #f7f7f7;
	border: solid 1px #bbb;
	box-shadow: 1px 1px 10px 1px rgba(141,141,141,0.20);
	.checkout__paymentOptionTitle {
		color: #3d3d3d;
	}
	.checkout__paymentOptionIcon {
		color: #4d4d4d;
	}
}

.checkout__paymentOptionLabel:hover {
	cursor: pointer;
}

.fa-payment-card:before {
	content: "\f09d";
}

.fa-payment-pickup:before {
	content: "\f041";
}

.fa-payment-invoice:before {
	content: "\f0f6";
}

.couponCode {
	max-width: 250px;
}

@media (max-width: 1070px) {

	.checkoutHeader {
		text-align: center;
		padding: @white-space / 6 0;
		font-size: @font-size-h3;
	}

	.checkoutHeader__left {
		display: none;
	}

	.checkoutHeader__center {
		display: block;
		width: 100%;
	}

	.checkoutHeader__right {
		display: none;
	}

	.checkoutHeader__center--mobile {
		display: block;
		font-size: 14px;
		color: inherit;
		a {
			color: inherit;
		}

	}

	.checkoutPage_formFields {
		width: 60%;
		padding-right: 30px;
		display: inline-block;
		vertical-align: top;
	}

	.checkoutPage_summaryContainer {
		width: 40%;
		display: inline-block;
		vertical-align: top;
	}

}

@media (max-width: @screen-xs-max) {
	.checkoutPage_formFields {
		width: 100%;
		padding-right: 0px;
		display: block;
	}

	.checkoutPage_summaryContainer {
		width: 100%;
		display: block;
	}

	.checkout__paymentOptionIcon {
		display: inline-block !important;
		margin: 0 3px 0 0;
		font-size: 1em !important;
	}

	.checkout__paymentOptions {
		margin-right: 0px;
	}

	.checkout__paymentOption {
	  	display: block;
	  	width: 100%;
	  	padding: 0 0 0 0;
	}

	.checkout__paymentOptionTitle {
		display: inline-block;
	}

	.checkout__paymentOptionTitleSpacer {
		display: none;
	}

	.checkout__paymentOptionLabel {
		padding: 12px;
  		text-align: left;
  		margin-bottom: 12px;
  		display: flex;
	  	align-items: center;
	  	justify-content: space-between;
	  	> span {
	  		flex: 0 0 auto;
	  	}
	  	input[type="radio"] {
	  		flex: 0 0 auto;
	  		vertical-align: middle;
	  		margin: 0;
	  		display: inline-block;
	  	}
	}
}

@media (max-width: 334px) {
	.exp-month-field {
		display: inline-block;
		margin-bottom: @form-group-margin-bottom;
	}
}

.checkoutPage_scheduleType .checkbox {
    display: flex;
    align-items: flex-start;
}

.checkoutPage_scheduleType .checkbox label {
    padding-left: 0.25rem;
}

.checkoutPage_reorderFrequencyOptions {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    gap: 0.25rem;
    position: relative;
    > div {
        position: relative;
    }
    input[type=radio] {
        flex: 0 0 auto;
        position: absolute;
        opacity: 0;
    }
    input[type=radio] + label {
        opacity: 0.5;
    }
    input[type=radio]:checked + label {
        opacity: 1;
        border-color: var(--brand_color);
        background-color: var(--brand_color);
        color: var(--brand_color_inverted);
    }
    input[type=radio]:focus + label {
        opacity: 1;
        border-color: var(--brand_color);
        background-color: var(--brand_color);
        color: var(--brand_color_inverted);
        outline: solid 2px var(--brand_color);
        outline-offset: 2px;
    }
    label {
        display: flex;
        align-items: center;
        justify-content: center;
        border: solid 1px #ddd;
        border-radius: 0.25rem;
        min-height: 1.25rem;
        padding: 0.5rem 0.25rem;
        cursor: pointer;
        line-height: 1;
    }
}

@media screen and (max-width: 950px) {
    .checkoutPage_reorderFrequencyOptions {
        grid-template-columns: 1fr 1fr;
    }
}
