.mobileNav {
	display: none;
	list-style-type: none;
	margin: 0;
	padding: 0;
	text-align: center;
	width: 100%;
	max-width: 100%;
	background-color: transparent;
	> li {
		text-align: center;
		display: inline-block;
		width: 33.3333333%;
		padding: 0;
		> a {
			display: inline-block;
			width: 100%;
			vertical-align: middle;
			padding: (@white-space / 6) (@white-space / 8);
			font-size: 12px !important;
		}
		> a:hover {
			text-decoration: none;
			background-color: darken(#f8f8f8, 10%);
		}
		> a > i {
			display: block;
			margin-bottom: (@white-space / 8);
		}
	}
}

@media (max-width: @mobile-width) {
	.mobileNav {
		display: flex;
	}
}

.mobileNav__search {
	display: block;
	padding: (@white-space / 2);
}

// Mobile Nav List

.mobileNav__list {
	list-style-type: none;
	margin: 0;
	padding: 0;
	width: auto;
	text-align: left;
	> li {
		display: block;
		border-bottom: solid 1px #EEE;
		> a {
			display: block;
			padding: (@white-space / 4);
		}
		> a:hover, a:focus {
			text-decoration: none;
			background-color: #f3f3f3;
		}
	}
	> li.menu-item > a, li.dropdown > a {
		color: #3d3d3d !important;
	}
	> li:last-of-type {
		border-bottom: none;
	}
}

.mobileNav__list .dropdown-menu {
	position: relative;
	float: none;
	padding: 0;
	> li {
		display: block;
		border-bottom: solid 1px #EEE;
		> a {
			display: block;
			padding: (@white-space / 4) (@white-space / 4) (@white-space / 4) (@white-space / 2);
		}
	}
	> li:last-of-type {
		border-bottom: none;
	}
}

@media (max-width: @mobile-width) {
	.caret {
		display: none;
	}
}	


