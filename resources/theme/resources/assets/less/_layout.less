.pageContainer {
	max-width: @site-width;
	width: 100%;
	margin: (@white-space / 2) auto;
	padding: 0 (@white-space / 2);
}

.checkoutContainer {
	max-width: @site-width;
	width: 100%;
	margin: (@white-space / 2) auto;
	padding: 0 (@white-space / 2);
}

.pageContainer(@max-width: @site-width) {
	max-width: @max-width;
	width: 100%;
	margin: (@white-space / 2) auto;
	padding: 0 (@white-space / 2);
}

.centerThis() {
	max-width: @site-width;
	width: 100%;
	margin: 0 auto;
	padding: 0 (@white-space / 2);
}

.centerBlock {
	max-width: (@site-width / 2);
	margin: 0 auto;
}

.block-left {
	margin: 0 auto 0 0;
}

.block-center {
	margin: 0 auto;
}

.block-right {
	margin: 0 0 0 auto;
}

body.modal-visible {
	overflow-y: hidden;
}
