.scaleUp-enter-active, .scaleUp-leave-active {
  transition: transform 0.3s ease-in-out;
  transform-origin: 50% 100%;
}

.scaleUp-enter {
  transform: scale(0.3);
}

.scaleUp-leave-to {
  transform: scale(0.3);
}


.slideUp-enter-active, .slideUp-leave-active {
  transition: transform 0.3s ease-in-out;
}

.slideUp-enter {
  transform: translate(0, 100%);
}

.slideUp-leave-to {
  transform: translate(0 , 100%);
}


.slideRight-enter-active, .slideRight-leave-active {
  transition: transform 0.3s ease-in-out;
}

.slideRight-enter {
  transform: translate(-100%, 0);
}

.slideRight-leave-to {
  transform: translate(-100%, 0);
}


.fade-enter-active, .fade-leave-active {
  transition: opacity 0.2s ease-in-out;
}

.fade-enter {
	opacity: 0;
}

.fade-leave-to {
	opacity: 0;
}