.cookbook{
	text-align: left;
	font-size: 1;
}

.cookbook__container {
	display: inline-block;
	vertical-align: top;
	max-width: 33%;
	padding: 20px 12px;
	text-align: left;
	margin-left: -4px;
}

.cookbook__heading {
	font-size: 16px;
	margin: 6px 0 20px 0;
	padding: 0;
	a {
		color: #5d5d5d;
	}
}

.cookbook__subheading {
	font-size: 0.9em;
	color: @gray-light;
	padding: 0.25em 0 0 0;
	a {
		color: @gray-light;
		border-bottom: dotted 1px @gray-light;
	}
	a:hover {
		text-decoration: none;
	}
}

.cookbook__body {
	margin: 0;
	line-height: 1.5;
	font-size: 13px;

	img {
		max-width: 100%;
		display: inline-block;
		border: none;
	}

	table, iframe {
		max-width: 100%;
	}
}

.cookbook__read-more {
	display: inline-block;
	font-size: 16px;
	padding: 10px 0;
	font-weight: bold;
}

@media (max-width: 768px) {
	.cookbook__container {
		max-width: 50%;
	}
}

@media (max-width: 375px) {
	.cookbook__container {
		display: block;
		max-width: 100%;
		border-bottom: solid 1px #eee;
		margin-left: 0;
	}
}