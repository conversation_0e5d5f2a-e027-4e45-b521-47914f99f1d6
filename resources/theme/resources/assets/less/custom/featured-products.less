.featuredProducts {
	display: flex;
	flex-wrap: wrap;
	justify-content: space-around;
	max-width: 1200px;
	margin: 0 auto;
	> div {
		margin: 0 15px 35px 15px;
	}
}

.featuredProduct__container {
	width: 25%;
	text-align: center;
	@media (max-width: 768px) {
	  width: 100%;
	}
}


.featuredProduct__heading {
	font-size: 16px;
	margin: 0;
	padding: 0;
	color: @gray;
	a {
		color: @gray;
	}
	a:hover {
		text-decoration: none;
	}
	@media (max-width: @screen-sm-max) {
	  	font-size: 14px;
	}
}

.featuredProduct__subheading {
	font-size: 0.9em;
	color: @gray-light;
	padding: 0.25em 0 0.25em 0;
	a {
		color: @gray-light;
		border-bottom: dotted 1px @gray-light;
	}
	a:hover {
		text-decoration: none;
	}
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}