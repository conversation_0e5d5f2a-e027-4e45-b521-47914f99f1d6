.post {
	margin-bottom: 40px;
	padding: 40px 0;
	display: flex;
	border-bottom: solid 1px #eee;
}

.post__heading {
	font-size: 24px;
	margin: 0;
	padding: 0;
	@media (max-width: @screen-sm-max) {
    	font-size: 1.5em;
  }
}

.post__subheading {
	font-size: 0.9em;
	color: @gray-light;
	padding: 0.25em 0 0 0;
	a {
		color: @gray-light;
		border-bottom: dotted 1px @gray-light;
	}
	a:hover {
		text-decoration: none;
	}
}

.post__cover-photo {
	width: 40%;
	height: auto;
	padding: 0 40px 0 0;
	margin: 0;
	img {
		max-width: 100%;
		width: auto;
		max-height: 450px;
	}
}

.post__details {
	flex: 1 0 auto;
	width: 750px;
	width: 60%;
}
	
.post__body {
	margin: 1em 0;
	line-height: 1.8em;
	font-size: 19px;

	img {
		max-width: 100%;
		display: inline-block;
		border: none;
	}

	table {
		max-width: 100%;
	}

	iframe {
		max-width: 100%;
	}

	p {
		line-height: 1.8em;
		font-size: 19px;
		font-family: @font-family-serif;
		padding: 0.5em 0;
	}
}

@media (max-width: 768px) {
	.post {
		display: block;
	}
	.post__cover-photo {
		width: 100%;

		padding: 0 0 20px 0;
		flex: 1 0 auto;
		img {
			max-width: 100%;
			width: auto;
			height: auto;
		}
	}
	.post__details {
		flex: 1 0 auto;
		max-width: 100%;
		width: 100%;
	}
}	

a.post__read-more {
	margin: 3px 0 0 0;
	display: inline-block;
	font-size: 17px;
}

a.post__read-more:hover {
	text-decoration: none;
}

.post__section {
	margin: 4em auto;
}

.post__list-wide {
	list-style-type: none;
	padding: 0;

	li {
		display: block;
		border-bottom: solid 1px #ddd;
		margin-bottom: 1em;
		padding: 1em 0;
		font-size: 20px;
	}
}

.post__author {
	text-align: right;
	font-size: 14px;
	padding-top: 0em;
	color: @gray;
	a {
		color: @gray;
		border-bottom: dotted 1px @gray;
	}
	a:hover {
		text-decoration: none;
	}
}

.post__recent {
	margin-top: 4em;
	padding-top: 2em;
	border-top: solid 1px @gray-lighter;
}

.post__recent-container {
	@media (max-width: @screen-sm-max) {
	  margin-bottom: 2em;
	  padding-bottom: 2em;
	  border-bottom: solid 1px @gray-lighter;
	}
}

.post--related-heading {
	font-size: 1.2em;
	margin: 0;
	padding: 0;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	color: @gray;
	a {
		color: @gray;
	}
	a:hover {
		text-decoration: none;
	}
	@media (max-width: @screen-sm-max) {
	  font-size: 1em;
	}
}

.post--related-subheading {
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.post--related-cover-photo {
	margin-bottom: 1em;
	height: auto;
	overflow: hidden;
	max-height: 500px;
	max-width: 500px;
	@media (max-width: @screen-xs-max) {
		max-height: 350px;
		max-width: 100%;
		display: block;
	}
}

.post--cover-photo-cropped {
	position: relative;
	height: 400px;
	@media (max-width: @screen-xs-max) {
		height: auto;
	}
	img {
	position: absolute;
	width: 100%;
	max-width: 100%;
	display: block;
	clip: rect(0px,750px,400px,0px);
	margin: 0;
		@media (max-width: @screen-xs-max) {
	    	clip: auto;
	    	position: relative;
	  	}
	}
}