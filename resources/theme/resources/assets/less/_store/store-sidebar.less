.storeIndex__sideBar--style1 {
	display: inline-block;
	vertical-align: top;
	height: auto;
	border-right: solid 1px #f3f3f3;
	padding-right: (@white-space / 4);
	flex: 0 1 255px;
	-ms-flex: 0 1 255px;
	width: 255px;
	min-width: 255px;
}

.storeIndex__sideBar--style2 {
	display: block;
	vertical-align: middle;
	width: 100%;
	min-width: 100%;
	background-color: #f8f8f8;
}

.storeIndex__sideBar--visibility {
	display: inline-block;
}

.storeIndex__sideBarList--style1 {
	list-style-type: none;
	margin: 0 0 (@white-space / 4) 0;
	padding: 0 0 (@white-space / 6) 0;
	border-bottom: solid 1px #f3f3f3;
	> li {
		display: block;
		margin-bottom: (@white-space / 6);
	}
	> li > a {
		display: block;
	}
	.dropdown-menu {
		position: relative;
		float: none;
		box-shadow: none;
		border: none;
		background-color: transparent;
		a {
			color: inherit;
		}
	}
}

.storeIndex__sideBarList--style2 {
	list-style-type: none;
	margin: 0;
	padding: 0;
	text-align: center;
	border-radius: 0;
	> li {
		display: inline-block;
		vertical-align: middle;
	}
	> li > a {
		padding: (@white-space / 6) (@white-space / 6);
		display: block;
		font-size: 15px;
	}
}

.mobileStoreMenuToggle {
	display: none;
}

@media (max-width: @mobile-width) {
	.storeIndex__sideBar {
		display: none;
		border-right: none;
		padding-right: 0;
		width: 100%;
	}

	.storeIndex__sideBar--style1 {
		padding: @white-space / 2;
	}

	.storeIndex__sideBarList--style2 {
		text-align: left;
		> li {
			display: block;
		}
		> li a {
			padding: (@white-space / 4) (@white-space / 6);
			display: block;
			font-size: 18px;
		}
	}

	.storeIndex__sideBarList--style1 {
		text-align: left;
		> li {
			display: block;
		}
		> li a {
			padding: (@white-space / 6) 0;
			display: block;
			font-size: 18px;
		}
	}

	.storeIndex__sideBar.show {
		display: block;
	}

	.mobileStoreMenuToggle {
		display: block;
	}
}	