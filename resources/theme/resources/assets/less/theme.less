@import "_bootstrap/bootstrap";
@import "variables.less";

// node_modules imports through less-loader
@import "../../../../../node_modules/balloon-css/balloon.css";
// css-only tooltips - used instead of bootstrap tooltips so that we have fewer dependencies on bootstrap in case we move away from it eventually (plus bootstrap tooltips require JavaScript, and this is css-only)

@import "_layout.less";
@import "_siteFooter.less";
@import "_socialShareButtons";
@import "_locations";
@import "_orderStatus";
@import "_utilities";
@import "animations";

@import "custom/menus.less";
@import "custom/top-menu.less";
@import "custom/featured-products.less";
// @import "custom/header/block.less";
// @import "custom/header/flex.less";
@import "custom/footer.less";
@import "custom/pages.less";
// @import "custom/confirmation.less";
@import "custom/banner.less";
@import "custom/messages.less";
@import "custom/locations.less";
@import "custom/post.less";
@import "custom/cookbook.less";
// @import "custom/recipe.less";
@import "custom/vendors.less";
@import "custom/protocols.less";
// @import "custom/store.less";
@import "custom/order.less";
@import "custom/product.less";
@import "custom/forms.less";
@import "custom/utilities.less";
@import "custom/info-boxes.less";
@import "custom/maps.less";
@import "custom/flexbox.less";
@import "custom/button.less";
@import "custom/tooltip.less";

// Widgets
@import "widgets/_banner.less";
@import "widgets/_featured-products.less";
@import "widgets/_featured-posts.less";
@import "widgets/_featured-recipes.less";
@import "widgets/_text.less";
@import "widgets/_text-header.less";
@import "widgets/_newsletter.less";
@import "widgets/_how-it-works.less";
@import "widgets/_contact-form.less";
@import "widgets/_contact-details.less";
@import "widgets/_link-list.less";
@import "widgets/_divider.less";
@import "widgets/_spacer.less";
@import "widgets/_product-protocols.less";
@import "widgets/_vendors.less";
@import "widgets/_photo-grid";
@import "widgets/_cta-button";
@import "widgets/_delivery-check-widget";
@import "widgets/_banner-protocols";
@import "widgets/_registration-form";
@import "widgets/_update-delivery-option.less";
@import "widgets/_team.less";
@import "widgets/_image_widget.less";
@import "widgets/_image_with_text.less";
@import "widgets/_testimonails.less";
@import "widgets/_photo-gallery.less";

@import "_admin-dashboard";
@import "_siteHeader/_siteHeader";

@import "_forms";

// Recipes
@import "_recipe";
@import "_related-recipe";

// Cart
@import "_cart/cart-page";
@import "_cart/cart-items";

@import "_checkout.less";

// Navigation
@import "_navigation/main-navigation";
@import "_navigation/store-navigation";
@import "_navigation/mobile-navigation";
@import "_navigation/back-to-top-link";

// Pages
@import "pages/contact.less";
@import "pages/forgot-password.less";
@import "pages/password-reset.less";
@import "pages/registration.less";
@import "pages/login.less";
@import "pages/vendors.less";
@import "pages/protocols.less";

// Customer
@import "_customer-profile.less";

// Store
@import "_store/index.less";
@import "_store/product-tags.less";
@import "_store/store-sidebar.less";
@import "_store/store-search.less";
@import "_store/collection-banner.less";

@import "_terms-page.less";

@import "_location-finder.less";

@import "_typography.less";


.btn-clear {
  border: none;
  background-color: transparent;
  color: currentColor;
  padding: 1em 1em;
  // text-align: left;
  i {
    opacity: 0.85;
    margin-left: 0.25em;
    font-size: 0.9em;
  }
}

.announcement_bar {
  font-size: 14px;
  text-align: center;
  padding: 0.5em;
  width: 100%;
  background-color: #FFF;
}

.guardModal {
  position: fixed;
  z-index: 9999;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.9);
}

.guardModal__close {
  height: 50px;
  width: 50px;
  position: fixed;
  top: 50px;
  right: 50px;
  background-color: transparent;
  border: solid 1px #FFF;
  color: #FFF;
  border-radius: 50%;
  line-height: 50px;
  text-align: center;
  font-size: 1em;
}

.guardModal__body {
  padding: 2em;
  max-width: 475px;
  background-color: #FFF;
  border-radius: 6px;
  text-align: center;

  form {
    margin-top: 1em;
  }
}

.fulfillmentOption_label {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  width: 100%;

  &:hover {
    background-color: #eee;
    cursor: pointer;
  }
}

.fulfillmentOption__input {
  flex: 0 0 auto;
  padding: 1.25rem;
}

.fulfillmentOption__info {
  flex: 1 1 auto;
  padding: 0.75rem;
}

.cartItems__unitDescription {
  display: none;
}

.starRating {
  label {
    display: block;
  }
}

.has-error label::after {
  content: ' (required)'
}

.underline {
  text-decoration: underline;
}

.underline:hover {
  text-decoration: none;
}

.fade-transition {
  opacity: 1;
  transition: all 0.3s ease;
}

.fade-enter {
  opacity: 0.1;
}

.fade-leave {
  opacity: 0.1;
}

.slide-transition {
  opacity: 1;
  transition: all 1.3s ease;
  transform: translateX(0);
  width: 32%;
  display: inline-block;
}

.slide-enter {
  transform: translateX(-33%);
}

.slide-leave {
  transform: translateX(33%);
}

.alert a {
  color: inherit;
  border-bottom-style: dotted;
  border-bottom-width: 1px;
}

.alert a:hover {
  text-decoration: none;
  color: inherit;
  border-bottom-style: solid;
}

.alert-light {
  background-color: #fafafa;
  border-color: @gray-lighter;
  position: relative;
}

.alert-close {
  position: absolute;
  top: -7px;
  right: -6px;
  font-size: 22px;
  color: @gray-light;
  line-height: 1;

  a {
    border-bottom: none;
  }

  a:hover {
    border-bottom: none;
    color: inherit;
  }

  &:hover {
    color: darken(@gray-light, 20%);
  }
}

.nowrap {
  display: inline-block;
}

.tabList {
  list-style-type: none;
  padding: 0;
  display: -ms-flexbox;
  display: flex;
  flex-wrap: wrap;
  max-width: 100%;

  > li {
    flex: 1 1 auto;
    -ms-flex: 1 1 auto;
    border-bottom: solid 2px #EEE;
    display: inline-block;

    > a {
      padding: 5px 1px;
      display: block;
    }
  }

  > li.active {
    border-color: #bbb;
  }
}

.validation-error {
  padding: (@white-space / 12);
}

.validation-error::before {
  content: "\f071";
  font-family: 'FontAwesome';
  margin: 0 (@white-space / 8);
}

.btn-break {
  white-space: normal;
}

.pageWidgets {
  text-align: center;
  max-width: 100%;
  width: 100%;
}

.pageWidget__slot {
  text-align: left;
  height: 100%;
}

.pageWidget__row {
  width: 100%;
}

.pageWidget__rowInnerContainer {
  max-width: @site-width;
  width: 100%;
  margin: 0 auto;
  display: table;
  table-layout: fixed;
}

.pageWidget__slot--full-width {
  width: 100%;
}

.pageWidget__slot--half-width {
  display: inline-block;
  vertical-align: top;
  min-width: 50%;
  width: 50%;
  margin: 0 auto;
  display: table-cell;
}

.pageWidget__slot--left {
  margin: 0 auto 0 0;
}

.pageWidget__slot--center {
  margin: 0 auto 0 auto;
}

.pageWidget__slot--right {
  margin: 0 0 0 auto;
}


@media (max-width: @mobile-width) {
  .pageWidget__container {
    display: block;
  }

  .pageWidget__slot--full-width, .pageWidget__slot--half-width {
    width: 100%;
    min-width: 100%;
  }

  .pageWidget__slot--1 {
    > section {
      padding-bottom: (@white-space / 4);
    }
  }

  .pageWidget__slot--2 {
    > section {
      padding-top: (@white-space / 4);
    }
  }

  .pageWidgets {
    text-align: center;
    display: block;

    > div, > section {
      text-align: left;
    }
  }

  .pageWidget__slot {
    display: block;
  }
}

[v-cloak] {
  display: none;
}

.btn, .cta {
  transition: all 0.1s ease-in-out;
}

img, iframe {
  max-width: 100%;
}

td {
  vertical-align: middle !important;
}

.panel-hover:hover {
  background-color: darken(@panel-default-heading-bg, 5%);
  cursor: pointer;
}

.storeStatus__container > span {
  border-bottom: dotted 1px #aaa;
}

.storeStatus__container > span:hover {
  cursor: pointer;
}
