<div
        class="testimonialsWidget testimonialsWidget--{{ $widget->id}}"
        id="testimonialsWidget{{ $widget->id }}"
        data-widget="{{ $widget->id}}"
>

    @if($widget->setting('header_show', true))
        <header class="testimonialsWidget__header text-center">
            <h2 data-widget="{{ $widget->id}}" data-element="headingText">
                {!! $widget->setting('header') !!}
            </h2>
        </header>
    @endif

    <div class="testimonialsWidget__testimonials">
        @foreach($widget->setting('items', []) as $testimonial)
            <figure
                    class="testimonialsWidget__testimonial"
                    data-widget="{{ $widget->id }}"
                    data-element="TestimonialEditor"
                    data-item="{{ $loop->index }}"
            >
                <div class="testimonial__bodyContainer">
                    <blockquote class="testimonial__body">
                        {{ $testimonial->body ?? null }}
                    </blockquote>
                </div>
                <figcaption class="testimonialAttribution">
                    @if(isset($testimonial->photo_src) && $testimonial->photo_src)
                        <img
                                class="testimonial__photo"
                                src="{{ \App\Models\Media::s3ToCloudfront($testimonial->photo_src) }}"
                                alt="{{ $testimonial->attribution ?? null }}"
                        >
                    @endif
                    <span class="testimonialAttribution__text">{{ $testimonial->attribution ?? null }}</span>
                </figcaption>
            </figure>
        @endforeach
    </div>
</div>
