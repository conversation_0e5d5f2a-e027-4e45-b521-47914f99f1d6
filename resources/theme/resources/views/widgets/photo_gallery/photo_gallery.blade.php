@if(is_array($widget->setting('items')) && count($widget->setting('items')))
    <section
            class="photoGalleryWidget photoGalleryWidget--{{ $widget->id }}"
            id="photoGalleryWidget--{{ $widget->id }}"
            data-widget="{{ $widget->id}}"
            data-element="photos"
    >
        @if($widget->setting('header_show', true))
            <header class="photoGalleryWidget__header text-center">
                <h2 data-widget="{{ $widget->id}}" data-element="headingText">
                    {{ $widget->setting('header') }}
                </h2>
            </header>
        @endif

        <div class="photoGalleryWidget__photoList">
            @foreach($widget->setting('items') as $item)
                <a class="photoGalleryWidget__photoLink {{ $item->orientation ?? 'landscape'}}"
                   href="{{ ( !empty($item->url) && $item->url !== "/") ? url($item->url) : $item->src ?? '' }}"
                   target="_blank"
                   data-widget="{{ $widget->id }}" data-element="PhotoEditor" data-item="{{ $loop->index }}">
                    <img src="{{ \App\Models\Media::s3ToCloudfront($item->src ?? '') }}" alt="{{ $item?->caption ?? '' }}"
                         class="photoGalleryWidget__photo">
                </a>
            @endforeach
        </div>
    </section>
@endif
