@extends('theme::_layouts.main', [
    'pageTitle' => 'Update Your Password'
])

@section('content')
    <section class="pageContainer customerProfile">
        <div class="customerProfile__container">
            <div class="customerProfile__navigationContainer">
                @include('theme::customers.partials.navigation')
            </div>
            <section class="customerProfile__contentContainer">
                <h1 class="customerProfile__heading h1">{{ __('messages.change_password') }}</h1>
                <form action="/account/password" method="POST">
                    @csrf
                    @method('PUT')
                    <!-- Current_password Form Field -->
                    <div class="form-group">
                        <label for="current_password">@lang('messages.password.current')</label>
                        {{ html()->password('current_password')->id('current_password')->class('form-control') }}
                    </div>
                    <hr>
                    <!-- Password Form Field -->
                    <div class="form-group">
                        <label for="password">@lang('messages.password.new')</label>
                        {{ html()->password('password')->id('password')->class('form-control') }}
                    </div>

                    <!-- Password_confirmation Form Field -->
                    <div class="form-group">
                        <label for="password_confirmation">@lang('messages.password.confirm')</label>
                        {{ html()->password('password_confirmation')->id('password_confirmation')->class('form-control') }}
                    </div>

                    <div class="form-group">
                        <input type="submit" class="btn btn-danger" value="{{ ucwords(__('save changes')) }}">
                    </div>
                </form>
            </section>
        </div>
    </section>
@stop()
