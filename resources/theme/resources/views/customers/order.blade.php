@php
    /** @var \App\Models\Order $order_history */
@endphp

@extends('theme::_layouts.main', [
    'pageTitle' => 'Order #'.$order_history->id
])

@php
    /** @var \App\Models\Order $order_history */
    /** @var \App\Models\User $customer */
@endphp

@section('content')
    @if ($order_history->canBeModified())
        <livewire:theme.order-show/>
    @else
        <section class="pageContainer customerProfile">
            <div class="customerProfile__container">
                <div class="customerProfile__navigationContainer">
                    @include('theme::customers.partials.navigation')
                </div>
                <section class="customerProfile__contentContainer">
                    <div class="tw-reset">
                        <div class="">
                            <div class="tw-border-b tw-border-gray-200 tw-pb-4 sm:tw-flex sm:tw-items-center">
                                <div class="sm:tw-flex-auto">
                                    <div class="tw-flex tw-items-center tw-space-x-4">
                                        <h1 class="tw-m-0 tw-text-xl tw-font-semibold tw-leading-6 tw-text-gray-900">
                                            @if($order_history->isFromBlueprint())
                                                Subscribe & Save
                                            @endif Order #{{ $order_history->id }}
                                        </h1>

                                        @php $status = $order_history->status(); @endphp

                                        @if($status === 'Canceled')
                                            <span class="tw-inline-flex tw-items-center tw-rounded-md tw-bg-gray-50 tw-px-2 tw-py-1 tw-text-xs tw-font-medium tw-text-gray-700 tw-ring-1 tw-ring-inset tw-ring-gray-600/20">{{ $status }}</span>
                                        @elseif($status === 'New')
                                            <span class="tw-inline-flex tw-items-center tw-rounded-md tw-bg-theme-action-color/10 tw-px-2 tw-py-1 tw-text-xs tw-font-medium tw-text-theme-action-color tw-ring-1 tw-ring-inset tw-ring-theme-action-color/30">{{ $status }}</span>
                                        @else
                                            <span class="tw-inline-flex tw-items-center tw-rounded-md tw-bg-theme-brand-color/10 tw-px-2 tw-py-1 tw-text-xs tw-font-medium tw-text-theme-brand-color tw-ring-1 tw-ring-inset tw-ring-theme-brand-color/30">{{ $status }}</span>
                                        @endif
                                    </div>
                                    <p class="tw-m-0 tw-mt-2 tw-text-sm tw-text-gray-700">Order placed on
                                        <time datetime="{{ $order_history->confirmed_date }}">{{ $order_history->confirmed_date->format('M jS, Y') }}</time>
                                    </p>
                                </div>
                                @if( ! $order_history->customer->hasRecurringOrder())
                                    <div class="tw-mt-4 sm:tw-ml-16 sm:tw-mt-0 sm:tw-flex-none">
                                        <div class="tw-flex tw-space-x-4 tw-items-center">
                                            <form action="{{ route('customer.orders.reorder', [$order_history->id]) }}" method="POST">
                                                @csrf
                                                <input type="hidden" name="reorder" value="{{ $order_history->id }}">
                                                <button type="submit" class="tw-block tw-rounded-md tw-bg-theme-action-color tw-px-3 tw-py-2 tw-text-center tw-text-sm tw-font-semibold tw-text-white tw-shadow-sm hover:tw-bg-theme-action-color/70 focus-visible:tw-outline focus-visible:tw-outline-2 focus-visible:tw-outline-offset-2 focus-visible:tw-outline-theme-action-color">Reorder</button>

                                            </form>
                                            @if($order_history->orderWindowIsOpen() && !$order_history->is_paid)
                                                <form action="{{ route('customer.orders.destroy', [$order_history->id]) }}" method="POST">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="tw-block tw-rounded-md tw-bg-theme-action-color tw-px-3 tw-py-2 tw-text-center tw-text-sm tw-font-semibold tw-text-white tw-shadow-sm hover:tw-bg-theme-action-color/70 focus-visible:tw-outline focus-visible:tw-outline-2 focus-visible:tw-outline-offset-2 focus-visible:tw-outline-theme-action-color">Cancel</button>
                                                </form>
                                            @endif
                                        </div>
                                    </div>
                                @endif

                            </div>
                            <div class="tw-mt-8 tw-flex tw-flex-col sm:tw-flex-row sm:tw-space-x-8">
                                <div class="sm:tw-w-1/3">
                                    <p class="tw-m-0 tw-text-base tw-text-gray-900">@if ($order_history->pickup->isVirtual())
                                            Virtual
                                        @else
                                            {{ $order_history->pickup->isDeliveryZone() ? 'Home Delivery' : 'Pickup' }}
                                        @endif</p>
                                    <p class="tw-m-0 tw-mt-1 tw-text-sm tw-text-gray-500">on {{ $order_history->pickup_date?->format('D, M jS, Y') ?? 'TBA' }}</p>
                                </div>
                                <div class="tw-mt-4 sm:tw-mt-0 sm:tw-flex-1">
                                    @if($order_history->pickup->isDeliveryZone())
                                        <address>
                                            <span class="tw-block tw-text-base tw-text-gray-900">{{ $order_history->customer->full_name }}</span>
                                            <span class="tw-mt-1 tw-block tw-text-sm tw-text-gray-500">{{ $order_history->shipping_street }} @if ($order_history->shipping_street_2)
                                                    , {{ $order_history->shipping_street_2 }}
                                                @endif</span>
                                            <span class="tw-mt-1 tw-block tw-text-sm tw-text-gray-500">{{ $order_history->shipping_city }}, {{ $order_history->shipping_state }} {{ $order_history->shipping_zip }}</span>
                                        </address>
                                    @endif
                                    @if($order_history->pickup->isPickup())
                                        <address>
                                            <span class="tw-block tw-text-base tw-text-gray-900">{{ $order_history->pickup->display_name ?? $order_history->pickup->title }}</span>
                                            <span class="tw-mt-1 tw-block tw-text-sm tw-text-gray-500">{{ $order_history->pickup->street }} @if ($order_history->pickup->street_2)
                                                    , {{ $order_history->pickup->street_2 }}
                                                @endif</span>
                                            <span class="tw-mt-1 tw-block tw-text-sm tw-text-gray-500">{{ $order_history->pickup->city }}, {{ $order_history->pickup->state }} {{ $order_history->pickup->zip }}</span>
                                        </address>
                                    @endif
                                </div>
                            </div>
                            <div class="tw--mx-4 tw-mt-8 tw-flow-root sm:tw-mx-0">
                                <table class="tw-min-w-full">
                                    <colgroup>
                                        <col class="tw-w-full sm:tw-w-1/2">
                                        <col class="sm:tw-w-1/6">
                                        <col class="sm:tw-w-1/6">
                                        <col class="sm:tw-w-1/6">
                                    </colgroup>
                                    <thead class="tw-border-b tw-border-gray-300 tw-text-gray-900">
                                    <tr>
                                        <th scope="col" class="tw-py-3.5 tw-pl-4 tw-pr-3 tw-text-left tw-text-sm tw-font-semibold tw-text-gray-900 sm:tw-pl-0">Product</th>
                                        <th scope="col" class="tw-hidden tw-px-3 tw-py-3.5 tw-text-right tw-text-sm tw-font-semibold tw-text-gray-900 sm:tw-table-cell">Quantity</th>
                                        <th scope="col" class="tw-hidden tw-px-3 tw-py-3.5 tw-text-right tw-text-sm tw-font-semibold tw-text-gray-900 sm:tw-table-cell">Price</th>
                                        <th scope="col" class="tw-hidden tw-px-3 tw-py-3.5 tw-text-right tw-text-sm tw-font-semibold tw-text-gray-900 sm:tw-table-cell">Weight</th>
                                        <th scope="col" class="tw-py-3.5 tw-pl-3 tw-pr-4 tw-text-right tw-text-sm tw-font-semibold tw-text-gray-900 sm:tw-pr-0">Subtotal</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    @foreach($order_history->items as $item)
                                        @php /** @var \App\Models\OrderItem $item */ @endphp
                                        <tr class="tw-border-b tw-border-gray-200">
                                            <td class="tw-max-w-0 tw-py-5 tw-pl-4 tw-pr-3 tw-text-sm sm:tw-pl-0">
                                                @if($item->isUnfulfilled())
                                                    <span class="tw-mb-2 tw-inline-flex tw-items-center tw-rounded-md tw-bg-gray-50 tw-px-2 tw-py-1 tw-text-xs tw-font-medium tw-text-gray-600 tw-ring-1 tw-ring-inset tw-ring-gray-500/10">
                                                        @if($item->stock_status === 'short')
                                                            Short stock
                                                        @else
                                                            Out of stock
                                                        @endif
                                                    </span>
                                                @endif
                                                <div class="tw-font-medium tw-text-gray-900">
                                                    {{ $item->title }}
                                                </div>
                                                @if ($item->product['unit_description'])
                                                    <div class="tw-mt-1 tw-truncate tw-text-gray-500">{{ $item->product['unit_description'] }}</div>
                                                @endif
                                                @if($item->giftCard && $order_history->is_paid)
                                                    <div class="tw-mt-1 tw-truncate tw-text-gray-500">{{ $item->giftCard->code }}</div>
                                                @endif
                                            </td>
                                            <td class="tw-hidden tw-px-3 tw-py-5 tw-text-right tw-text-sm tw-text-gray-500 sm:tw-table-cell">
                                                @if($item->isUnfulfilled())
                                                    {{ $item->fulfilled_qty }} of {{ $item->qty }}
                                                @else
                                                    {{ $item->qty }}
                                                @endif
                                            </td>
                                            <td class="tw-hidden tw-px-3 tw-py-5 tw-text-right tw-text-sm tw-text-gray-500 sm:tw-table-cell">&#36;{{ money($item->unit_price) }}{{ $item->present()->unitOfIssue }}</td>
                                            <td class="tw-hidden tw-px-3 tw-py-5 tw-text-right tw-text-sm tw-text-gray-500 sm:tw-table-cell">{{ $item->weight > 0 ? weight($item->weight) : 'N/A'}}</td>
                                            <td class="tw-py-5 tw-pl-3 tw-pr-4 tw-text-right tw-text-sm tw-text-gray-500 sm:tw-pr-0">&#36;{{ $item->subtotal_formatted }}</td>
                                        </tr>
                                    @endforeach

                                    @foreach($order_history->fees as $fee)
                                        <tr class="tw-border-b tw-border-gray-200">
                                            <td class="tw-max-w-0 tw-py-5 tw-pl-4 tw-pr-3 tw-text-sm sm:tw-pl-0">
                                                <div class="tw-font-medium tw-text-gray-900">{{ $fee->title }}</div>
                                            </td>
                                            <td class="tw-hidden tw-px-3 tw-py-5 tw-text-right tw-text-sm tw-text-gray-500 sm:tw-table-cell">{{ $fee->qty }}</td>
                                            <td class="tw-hidden tw-px-3 tw-py-5 tw-text-right tw-text-sm tw-text-gray-500 sm:tw-table-cell">&#36;{{ money($fee->amount) }}</td>
                                            <td class="tw-hidden tw-px-3 tw-py-5 tw-text-right tw-text-sm tw-text-gray-500 sm:tw-table-cell"></td>
                                            <td class="tw-py-5 tw-pl-3 tw-pr-4 tw-text-right tw-text-sm tw-text-gray-500 sm:tw-pr-0">&#36;{{ money($fee->subtotal) }}</td>
                                        </tr>
                                    @endforeach

                                    <!-- More projects... -->
                                    </tbody>
                                    <tfoot>
                                    <tr>
                                        <th scope="row" colspan="4" class="tw-hidden tw-pl-4 tw-pr-3 tw-pt-6 tw-text-right tw-text-sm tw-font-normal tw-text-gray-500 sm:tw-table-cell sm:tw-pl-0">Subtotal</th>
                                        <th scope="row" class="tw-pl-4 tw-pr-3 tw-pt-6 tw-text-left tw-text-sm tw-font-normal tw-text-gray-500 sm:tw-hidden">Subtotal</th>
                                        <td class="tw-pl-3 tw-pr-4 tw-pt-6 tw-text-right tw-text-sm tw-text-gray-500 sm:tw-pr-0">&#36;{{ money($order_history->subtotal) }}</td>
                                    </tr>

                                    @if($order_history->delivery_fee > 0)
                                        <tr>
                                            <th scope="row" colspan="4" class="tw-hidden tw-pl-4 tw-pr-3 tw-pt-6 tw-text-right tw-text-sm tw-font-normal tw-text-gray-500 sm:tw-table-cell sm:tw-pl-0">{{ getMessage('delivery_fee_title') }}</th>
                                            <th scope="row" class="tw-pl-4 tw-pr-3 tw-pt-6 tw-text-left tw-text-sm tw-font-normal tw-text-gray-500 sm:tw-hidden">{{ getMessage('delivery_fee_title') }}</th>
                                            <td class="tw-pl-3 tw-pr-4 tw-pt-6 tw-text-right tw-text-sm tw-text-gray-500 sm:tw-pr-0">&#36;{{ money($order_history->delivery_fee) }}</td>
                                        </tr>
                                    @endif
                                    @if($order_history->fees_subtotal > 0)
                                        <tr>
                                            <th scope="row" colspan="4" class="tw-hidden tw-pl-4 tw-pr-3 tw-pt-4 tw-text-right tw-text-sm tw-font-normal tw-text-gray-500 sm:tw-table-cell sm:tw-pl-0">Fees</th>
                                            <th scope="row" class="tw-pl-4 tw-pr-3 tw-pt-4 tw-text-left tw-text-sm tw-font-normal tw-text-gray-500 sm:tw-hidden">Fees</th>
                                            <td class="tw-pl-3 tw-pr-4 tw-pt-4 tw-text-right tw-text-sm tw-text-gray-500 sm:tw-pr-0">&#36;{{ money($order_history->fees_subtotal - ($order_history->delivery_fee ?? 0)) }}</td>
                                        </tr>
                                    @endif
                                    @if($order_history->tax > 0)
                                        <tr>
                                            <th scope="row" colspan="4" class="tw-hidden tw-pl-4 tw-pr-3 tw-pt-4 tw-text-right tw-text-sm tw-font-normal tw-text-gray-500 sm:tw-table-cell sm:tw-pl-0">Taxes</th>
                                            <th scope="row" class="tw-pl-4 tw-pr-3 tw-pt-4 tw-text-left tw-text-sm tw-font-normal tw-text-gray-500 sm:tw-hidden">Taxes</th>
                                            <td class="tw-pl-3 tw-pr-4 tw-pt-4 tw-text-right tw-text-sm tw-text-gray-500 sm:tw-pr-0">&#36;{{ money($order_history->tax) }}</td>
                                        </tr>
                                    @endif
                                    @if($order_history->order_discount > 0)
                                        <tr>
                                            <th scope="row" colspan="4" class="tw-hidden tw-pl-4 tw-pr-3 tw-pt-4 tw-text-right tw-text-sm tw-font-normal tw-text-gray-500 sm:tw-table-cell sm:tw-pl-0">Discounts</th>
                                            <th scope="row" class="tw-pl-4 tw-pr-3 tw-pt-4 tw-text-left tw-text-sm tw-font-normal tw-text-gray-500 sm:tw-hidden">Discounts</th>
                                            <td class="tw-pl-3 tw-pr-4 tw-pt-4 tw-text-right tw-text-sm tw-text-gray-500 sm:tw-pr-0">(&#36;{{ money($order_history->order_discount) }})</td>
                                        </tr>
                                    @endif
                                    @if($order_history->credit_applied > 0)
                                        <tr>
                                            <th scope="row" colspan="4" class="tw-hidden tw-pl-4 tw-pr-3 tw-pt-4 tw-text-right tw-text-sm tw-font-normal tw-text-gray-500 sm:tw-table-cell sm:tw-pl-0">Credits</th>
                                            <th scope="row" class="tw-pl-4 tw-pr-3 tw-pt-4 tw-text-left tw-text-sm tw-font-normal tw-text-gray-500 sm:tw-hidden">Credits</th>
                                            <td class="tw-pl-3 tw-pr-4 tw-pt-4 tw-text-right tw-text-sm tw-text-gray-500 sm:tw-pr-0">(&#36;{{ money($order_history->credit_applied) }})</td>
                                        </tr>
                                    @endif
                                    @if($order_history->coupon_subtotal > 0)
                                        <tr>
                                            <th scope="row" colspan="4" class="tw-hidden tw-pl-4 tw-pr-3 tw-pt-4 tw-text-right tw-text-sm tw-font-normal tw-text-gray-500 sm:tw-table-cell sm:tw-pl-0">Coupons</th>
                                            <th scope="row" class="tw-pl-4 tw-pr-3 tw-pt-4 tw-text-left tw-text-sm tw-font-normal tw-text-gray-500 sm:tw-hidden">Coupons</th>
                                            <td class="tw-pl-3 tw-pr-4 tw-pt-4 tw-text-right tw-text-sm tw-text-gray-500 sm:tw-pr-0">(&#36;{{ money($order_history->coupon_subtotal) }})</td>
                                        </tr>
                                    @endif


                                    <tr>
                                        <th scope="row" colspan="4" class="tw-hidden tw-pl-4 tw-pr-3 tw-pt-4 tw-text-right tw-text-sm tw-font-normal tw-text-gray-500 sm:tw-table-cell sm:tw-pl-0">Total</th>
                                        <th scope="row" class="tw-pl-4 tw-pr-3 tw-pt-4 tw-text-left tw-text-sm tw-font-normal tw-text-gray-500 sm:tw-hidden">Total</th>
                                        <td class="tw-pl-3 tw-pr-4 tw-pt-4 tw-text-right tw-text-sm tw-font-normal tw-text-gray-500 sm:tw-pr-0">&#36;{{ money($order_history->total) }}</td>
                                    </tr>
                                    <tr>
                                        <th scope="row" colspan="4" class="tw-hidden tw-pl-4 tw-pr-3 tw-pt-4 tw-text-right tw-text-sm tw-font-normal tw-text-gray-500 sm:tw-table-cell sm:tw-pl-0">Paid</th>
                                        <th scope="row" class="tw-pl-4 tw-pr-3 tw-pt-4 tw-text-left tw-text-sm tw-font-normal tw-text-gray-500 sm:tw-hidden">Paid</th>
                                        <td class="tw-pl-3 tw-pr-4 tw-pt-4 tw-text-right tw-text-sm tw-font-normal tw-text-gray-500 sm:tw-pr-0">&#36;{{ money($order_history->payments_subtotal) }}</td>
                                    </tr>
                                    <tr>
                                        <th scope="row" colspan="4" class="tw-hidden tw-pl-4 tw-pr-3 tw-pt-4 tw-text-right tw-text-sm tw-font-semibold tw-text-gray-900 sm:tw-table-cell sm:tw-pl-0">Total Due</th>
                                        <th scope="row" class="tw-pl-4 tw-pr-3 tw-pt-4 tw-text-left tw-text-sm tw-font-semibold tw-text-gray-900 sm:tw-hidden">Total Due</th>
                                        <td class="tw-pl-3 tw-pr-4 tw-pt-4 tw-text-right tw-text-sm tw-font-semibold tw-text-gray-900 sm:tw-pr-0">&#36;{{ money($order_history->total_due) }}</td>
                                    </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </section>
    @endif
@endsection

