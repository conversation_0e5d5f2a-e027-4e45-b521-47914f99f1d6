@extends('theme::_layouts.main', [
    'pageTitle' => 'Update Your Profile'
])

@section('content')
    <section class="pageContainer customerProfile">
        <div class="customerProfile__container">
            @include('theme::customers.partials.navigation')
            <section class="customerProfile__contentContainer">
                <h1 class="customerProfile__heading h1">{{ __('messages.your_account') }}</h1>
                <ul class="info-boxes">
                    <li>
                        <div class="info-box-heading">{{ ucwords(__('store credit')) }}</div>
                        &#36;{{ money($customer->credit) }}
                    </li>
                </ul>
                <form action="/account" method="POST">
                    @csrf
                    @method('PUT')
                    <div class="form-group">
                        <label for="email">
                            {{ ucwords(__('email')) }}
                            <div style="font-size: 13px; color: #999;">
                                {{ __('messages.email_sub_message') }}
                            </div>
                        </label>
                        <input type="email" name="email" value="{{ $customer->email }}" class="form-control" id="email">
                    </div>

                    <!-- First_name Form Field -->
                    <div class="form-group">
                        <label for="first_name">{{ ucwords(__('first name')) }}</label>
                        <input type="text" name="first_name" value="{{ $customer->first_name }}" class="form-control" id="first_name">
                    </div>

                    <!-- Last_name Form Field -->
                    <div class="form-group">
                        <label for="last_name">{{ ucwords(__('last name')) }}</label>
                        <input type="text" name="last_name" value="{{ $customer->last_name }}" class="form-control" id="last_name">
                    </div>

                    <!-- Phone Form Field -->
                    <div class="form-group">
                        <label for="phone">{{ ucwords(__('phone')) }}</label>
                        <input
                                type="tel"
                                pattern="[0-9]{10}"
                                name="phone"
                                value="{{ $customer->phone }}"
                                class="form-control"
                                id="phone"
                                placeholder="xxx-xxx-xxxx"
                        >
                    </div>

                    <div class="form-group">
                        <label for="email_alt">
                            {{ ucwords(__('alternative email')) }}
                            <div style="font-size: 13px; color: #999;">
                                {{ __('messages.alt_email_sub_message') }}
                            </div>
                        </label>
                        <input type="text" name="email_alt" class="form-control" value="{{ old('email_alt', $customer->email_alt) }}"/>
                    </div>

                    <div class="form-group">
                        <input type="submit" class="btn btn-danger" value="{{ ucwords(__('save changes')) }}">
                    </div>
                </form>


                <div class="panel panel-default">
                    <div class="text-right">
                        <button
                                type="submit"
                                class="btn btn-light btn-sm btn-link"
                                x-on:click="$dispatch('legacy-modal-opened', { id: 'deleteCustomerModal' })"
                        >
                            Delete Account
                        </button>
                    </div>
                </div>
            </section>
        </div>
    </section>

    <x-theme::legacy-modal id="deleteCustomerModal">
        <form action="{{ route('customer.destroy') }}" method="POST" class="tw-relative tw-w-full tw-mx-auto tw-transform tw-rounded-lg tw-bg-white tw-text-left tw-shadow-xl sm:tw-my-8 sm:tw-w-full sm:tw-max-w-2xl">
            @csrf
            @method('DELETE')
            <div class="tw-rounded-t-lg tw-bg-white tw-px-4 tw-pb-4 tw-pt-5 sm:tw-p-6 sm:tw-pb-4">
                <div class="sm:tw-flex sm:tw-items-start">
                    <div class="tw-mx-auto tw-flex tw-h-12 tw-w-12 tw-flex-shrink-0 tw-items-center tw-justify-center tw-rounded-full tw-bg-theme-action-color/25 sm:tw-mx-0 sm:tw-h-10 sm:tw-w-10">
                        <svg class="tw-h-6 tw-w-6 tw-text-theme-action-color" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z"/>
                        </svg>
                    </div>
                    <div class="tw-mt-3 tw-text-center sm:tw-ml-4 sm:tw-mt-0 sm:tw-text-left">
                        <h3 class="tw-m-0 tw-text-lg tw-font-semibold tw-leading-6 tw-text-gray-900" id="modal-title">Delete Account</h3>
                        <p class="tw-m-0 tw-mt-1 tw-text-sm tw-text-gray-500">
                            Are you sure?
                        </p>

                        <p class="tw-m-0 tw-mt-4 tw-text-base">
                            Please confirm that you would like to delete your account. Any open orders you have will also be canceled.
                        </p>
                    </div>
                </div>
            </div>
            <div class="tw-rounded-b-lg tw-bg-gray-50 tw-px-4 tw-py-3 sm:tw-px-6 sm:tw-flex sm:tw-flex-row-reverse">
                <button type="submit" class="tw-inline-flex tw-w-full tw-justify-center tw-rounded-md tw-bg-theme-action-color tw-px-3 tw-py-2 tw-text-sm tw-font-semibold tw-text-white tw-shadow-sm hover:tw-bg-theme-action-color/70 focus-visible:tw-outline focus-visible:tw-outline-2 focus-visible:tw-outline-offset-2 focus-visible:tw-outline-theme-action-color sm:tw-ml-3 sm:tw-w-auto">
                    Delete Account
                </button>
                <button type="button" x-on:click="close" class="tw-mt-3 tw-inline-flex tw-w-full tw-justify-center tw-rounded-md tw-bg-white tw-px-3 tw-py-2 tw-text-sm tw-font-semibold tw-text-gray-900 tw-shadow-sm tw-ring-1 tw-ring-inset tw-ring-gray-300 hover:tw-bg-gray-50 sm:tw-mt-0 sm:tw-w-auto">
                    Never mind
                </button>
            </div>
        </form>
    </x-theme::legacy-modal>
@endsection
