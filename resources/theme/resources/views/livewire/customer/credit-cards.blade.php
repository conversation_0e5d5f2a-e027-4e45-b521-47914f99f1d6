@php
    /** @var \Illuminate\Support\Collection<\App\Billing\Gateway\PaymentMethod> $cards */
    /** @var string|null $default_card_id */
@endphp

<div class="tw-space-y-4">
    @foreach($cards as $card)
        @php /** @var \App\Billing\Gateway\PaymentMethod $card */ @endphp
        <div class="tw-px-4 tw-py-3 tw-flex tw-border tw-border-gray-200 tw-rounded-lg">
            <div class="tw-w-full tw-flex tw-justify-between">
                <div class="tw-flex tw-space-x-4">
                    <div class="tw-border tw-border-gray-200 tw-rounded-lg tw-flex tw-items-center tw-justify-center">
                        @if($card->brand === 'visa')
                            <svg class="tw-w-16 tw-h-10" version="1.1" viewBox="0 0 64 64" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                                                    <g>
                                                        <g>
                                                            <g>
                                                                <g>
                                                                    <polygon fill="#3C58BF" points="23.6,41 26.8,23 31.8,23 28.7,41     "/>
                                                                </g>
                                                            </g>
                                                            <g>
                                                                <g>
                                                                    <polygon fill="#293688" points="23.6,41 27.7,23 31.8,23 28.7,41     "/>
                                                                </g>
                                                            </g>
                                                            <g>
                                                                <g>
                                                                    <path d="M46.8,23.2c-1-0.4-2.6-0.8-4.6-0.8c-5,0-8.6,2.5-8.6,6.1c0,2.7,2.5,4.1,4.5,5c2,0.9,2.6,1.5,2.6,2.3      c0,1.2-1.6,1.8-3,1.8c-2,0-3.1-0.3-4.8-1l-0.7-0.3l-0.7,4.1c1.2,0.5,3.4,1,5.7,1c5.3,0,8.8-2.5,8.8-6.3c0-2.1-1.3-3.7-4.3-5      c-1.8-0.9-2.9-1.4-2.9-2.3c0-0.8,0.9-1.6,2.9-1.6c1.7,0,2.9,0.3,3.8,0.7l0.5,0.2L46.8,23.2L46.8,23.2z" fill="#3C58BF"/>
                                                                </g>
                                                            </g>
                                                            <g>
                                                                <g>
                                                                    <path d="M46.8,23.2c-1-0.4-2.6-0.8-4.6-0.8c-5,0-7.7,2.5-7.7,6.1c0,2.7,1.6,4.1,3.6,5c2,0.9,2.6,1.5,2.6,2.3      c0,1.2-1.6,1.8-3,1.8c-2,0-3.1-0.3-4.8-1l-0.7-0.3l-0.7,4.1c1.2,0.5,3.4,1,5.7,1c5.3,0,8.8-2.5,8.8-6.3c0-2.1-1.3-3.7-4.3-5      c-1.8-0.9-2.9-1.4-2.9-2.3c0-0.8,0.9-1.6,2.9-1.6c1.7,0,2.9,0.3,3.8,0.7l0.5,0.2L46.8,23.2L46.8,23.2z" fill="#293688"/>
                                                                </g>
                                                            </g>
                                                            <g>
                                                                <g>
                                                                    <path d="M55.4,23c-1.2,0-2.1,0.1-2.6,1.3L45.3,41h5.4l1-3h6.4l0.6,3h4.8l-4.2-18H55.4z M53.1,35      c0.3-0.9,2-5.3,2-5.3c0,0,0.4-1.1,0.7-1.8l0.3,1.7c0,0,1,4.5,1.2,5.5H53.1L53.1,35z" fill="#3C58BF"/>
                                                                </g>
                                                            </g>
                                                            <g>
                                                                <g>
                                                                    <path d="M56.6,23c-1.2,0-2.1,0.1-2.6,1.3L45.3,41h5.4l1-3h6.4l0.6,3h4.8l-4.2-18H56.6z M53.1,35      c0.4-1,2-5.3,2-5.3c0,0,0.4-1.1,0.7-1.8l0.3,1.7c0,0,1,4.5,1.2,5.5H53.1L53.1,35z" fill="#293688"/>
                                                                </g>
                                                            </g>
                                                            <g>
                                                                <g>
                                                                    <path d="M14.4,35.6L13.9,33c-0.9-3-3.8-6.3-7-7.9l4.5,16h5.4l8.1-18h-5.4L14.4,35.6z" fill="#3C58BF"/>
                                                                </g>
                                                            </g>
                                                            <g>
                                                                <g>
                                                                    <path d="M14.4,35.6L13.9,33c-0.9-3-3.8-6.3-7-7.9l4.5,16h5.4l8.1-18h-4.4L14.4,35.6z" fill="#293688"/>
                                                                </g>
                                                            </g>
                                                            <g>
                                                                <g>
                                                                    <path d="M0.5,23l0.9,0.2c6.4,1.5,10.8,5.3,12.5,9.8l-1.8-8.5c-0.3-1.2-1.2-1.5-2.3-1.5H0.5z" fill="#FFBC00"/>
                                                                </g>
                                                            </g>
                                                            <g>
                                                                <g>
                                                                    <path d="M0.5,23L0.5,23c6.4,1.5,11.7,5.4,13.4,9.9l-1.7-7.1c-0.3-1.2-1.3-1.9-2.4-1.9L0.5,23z" fill="#F7981D"/>
                                                                </g>
                                                            </g>
                                                            <g>
                                                                <g>
                                                                    <path d="M0.5,23L0.5,23c6.4,1.5,11.7,5.4,13.4,9.9L12.7,29c-0.3-1.2-0.7-2.4-2.1-2.9L0.5,23z" fill="#ED7C00"/>
                                                                </g>
                                                            </g>
                                                        </g>
                                                        <g>
                                                            <path d="M19.4,35L16,31.6l-1.6,3.8l-0.4-2.5c-0.9-3-3.8-6.3-7-7.9l4.5,16h5.4L19.4,35z" fill="#051244"/>
                                                        </g>
                                                        <g>
                                                            <polygon fill="#051244" points="28.7,41 24.4,36.6 23.6,41 28.7,41   "/>
                                                        </g>
                                                        <g>
                                                            <path d="M40.2,34.8L40.2,34.8c0.4,0.4,0.6,0.7,0.5,1.1c0,1.2-1.6,1.8-3,1.8c-2,0-3.1-0.3-4.8-1l-0.7-0.3l-0.7,4.1    c1.2,0.5,3.4,1,5.7,1c3.2,0,5.8-0.9,7.3-2.5L40.2,34.8z" fill="#051244"/>
                                                        </g>
                                                        <g>
                                                            <path d="M46,41h4.7l1-3h6.4l0.6,3h4.8l-1.7-7.3l-6-5.8l0.3,1.6c0,0,1,4.5,1.2,5.5h-4.2c0.4-1,2-5.3,2-5.3    c0,0,0.4-1.1,0.7-1.8" fill="#051244"/>
                                                        </g>
                                                    </g>
                                                </svg>
                        @elseif($card->brand === 'mastercard')
                            <svg class="tw-w-16 tw-h-5" id="svg2985" style="enable-background:new 0 0 564.5 366.9;" version="1.1" viewBox="0 0 564.5 366.9" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:svg="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><style type="text/css">
                                    .st0 {
                                        fill: #F79F1A;
                                    }

                                    .st1 {
                                        fill: #EA001B;
                                    }

                                    .st2 {
                                        fill: #FF5F01;
                                    }
                                </style>
                                <g id="g3125" transform="matrix(1.2152823,0,0,1.2152823,-67.025868,-37.711623)">
                                    <g id="g3115">
                                        <g id="g3110">
                                            <path class="st0" d="M519.7,182.2c0,79.5-64.3,143.9-143.6,143.9s-143.6-64.4-143.6-143.9S296.7,38.3,376,38.3     S519.7,102.7,519.7,182.2L519.7,182.2z" id="path2997"/>
                                            <path class="st1" d="M342.4,182.2c0,79.5-64.3,143.9-143.6,143.9S55.2,261.7,55.2,182.2S119.5,38.3,198.8,38.3     S342.4,102.7,342.4,182.2L342.4,182.2z" id="path2995"/>
                                            <path class="st2" d="M287.4,68.9c-33.5,26.3-55,67.3-55,113.3s21.5,87,55,113.3     c33.5-26.3,55-67.3,55-113.3S320.9,95.3,287.4,68.9z" id="path2999"/>
                                        </g>
                                        <g id="g3043" transform="matrix(1.2704171,0,0,1.2704171,507.53053,240.70315)">
                                            <g id="text3045">
                                                <path class="st0" d="M9.3,14.1v-4H8.3l-1.2,2.8L5.9,10H4.8v4h0.7v-3l1.1,2.6      h0.8L8.6,11v3.1H9.3z M2.6,14.1v-3.4H4V10H0.6v0.7h1.4v3.4H2.6z" id="path3057"/>
                                            </g>
                                            <g id="text3047"/>
                                        </g>
                                    </g>
                                </g>
                                                </svg>
                        @elseif($card->brand === 'amex')
                            <svg class="tw-w-16 tw-h-10" enable-background="new 0 0 40 40" version="1.1" viewBox="0 0 40 40" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><g id="E-Com">
                                    <g id="CVC_5_"/>
                                    <g id="Mastercard_5_"/>
                                    <g id="Visa_6_"/>
                                    <g id="Discover"/>
                                    <g id="Amex_3_">
                                        <g id="Amex">
                                            <g>
                                                <path clip-rule="evenodd" d="M34,9.5H6c-1.1,0-2,0.9-2,2v17c0,1.1,0.9,2,2,2h28      c1.1,0,2-0.9,2-2v-17C36,10.4,35.1,9.5,34,9.5z" fill="#3498D8" fill-rule="evenodd"/>
                                            </g>
                                        </g>
                                        <g id="Amex_1_">
                                            <g>
                                                <path clip-rule="evenodd" d="M10.7,20.3h1.6l-0.8-2L10.7,20.3z M33,16.5h-4.1l-1,1.1      l-0.9-1.1h-8.7l-0.8,1.8l-0.8-1.8h-3.5v0.8l-0.4-0.8h-3l-2.9,7h3.5l0.4-1.1h1l0.4,1.1h3.9v-0.8l0.3,0.8h2l0.3-0.9v0.9h8l1-1.1      l0.9,1.1l4.1,0L30.1,20L33,16.5z M20.9,22.5h-1.1l0-3.9l-1.7,3.9h-1l-1.7-3.9v3.9h-2.3l-0.4-1.1h-2.4l-0.4,1.1H8.6l2.1-5h1.7      l1.9,4.7v-4.7h1.9l1.5,3.4l1.4-3.4h1.9V22.5z M30.8,22.5h-1.5L28,20.8l-1.5,1.7h-4.5v-5h4.6l1.4,1.6l1.5-1.6h1.4L28.7,20      L30.8,22.5z M23.1,18.5v0.9h2.5v1h-2.5v1h2.8l1.3-1.5L26,18.5H23.1z" fill="#FFFFFF" fill-rule="evenodd"/>
                                            </g>
                                        </g>
                                    </g>
                                    <g id="Bitcoin_3_"/>
                                    <g id="Google_Wallet_5_"/>
                                    <g id="PayPal_3_"/>
                                    <g id="Square_Payment_1_"/>
                                    <g id="Shop_5_"/>
                                    <g id="Postage"/>
                                    <g id="Package_7_"/>
                                    <g id="Discount_3_"/>
                                    <g id="Earth_3_"/>
                                    <g id="Barcode_3_"/>
                                    <g id="Cart_Plus_6_"/>
                                    <g id="Cart_Minus_6_"/>
                                    <g id="Cart_4_"/>
                                    <g id="Receipt_5_"/>
                                    <g id="Truck_9_"/>
                                    <g id="Calculator_6_"/>
                                    <g id="Euro_Symbol"/>
                                    <g id="Cent_Symbol"/>
                                    <g id="Dollar_Symbol"/>
                                    <g id="Pound_Symbol"/>
                                    <g id="Bank_5_"/>
                                    <g id="Wallet_3_"/>
                                    <g id="Coins_6_"/>
                                    <g id="Bills_6_"/>
                                    <g id="Dollar_Alt"/>
                                    <g id="Dollar"/>
                                </g>
                                <g id="Lockup"/>
                                                </svg>
                        @elseif($card->brand === 'discover')
                            <svg class="tw-w-16 tw-h-10" style="enable-background:new 0 0 512 512;" version="1.1" viewBox="0 0 512 512" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><g id="_x39_3-discover_x2C__Credit_card">
                                    <g>
                                        <path d="M434.334,81H77.667C57.424,81,41,97.424,41,117.667v276.665C41,414.576,57.424,431,77.667,431H431    c20.243,0,40-16.424,40-36.668V117.667C471,97.424,454.576,81,434.334,81z" style="fill:#F9F9F9;"/>
                                        <path d="M432.771,228.354c9.243-1.909,14.284-8.097,14.284-17.722c0-21.771-23.146-18.639-40.408-18.639l-0.001,62.639h12.223    v-25.055h1.681l16.958,25.055h14.973L432.771,228.354z M422.457,221.021h-3.589v-19.02h3.742c7.715,0,11.917,3.207,11.917,9.243    C434.527,217.66,430.325,221.021,422.457,221.021z"/>
                                        <polygon points="363.943,192.07 398.549,192.07 398.549,202.764 376.168,202.764 376.168,216.668 397.784,216.668     397.784,227.208 376.168,227.208 376.168,244.167 398.549,244.167 398.549,254.708 363.943,254.708   "/>
                                        <polygon points="311.465,192.07 328.194,234.236 345.153,192.07 358.521,192.07 331.403,256.389 324.834,256.389 298.097,192.07       "/>
                                        <path d="M268.764,189.779c18.868,0,34.07,15.277,34.07,34.069c0,18.868-15.279,34.07-34.07,34.07    c-18.868,0-34.069-15.279-34.069-34.07C234.694,204.98,249.974,189.779,268.764,189.779z" style="fill:#FF6F00;"/>
                                        <path d="M231.104,194.438v14.514c-15.353-15.354-35.749-3.589-35.749,14.514c0,19.098,21.006,29.411,35.749,14.667v14.514    c-22.687,10.923-48.354-4.354-48.354-29.181C182.75,199.632,208.035,182.98,231.104,194.438z"/>
                                        <path d="M156.855,245.083c8.708,0,17.111-11.687-2.521-18.638c-11.457-4.202-15.43-8.708-15.43-17.34    c0-17.723,23.374-23.986,37.965-10.922l-6.417,8.248c-7.944-8.86-19.021-4.736-19.021,1.912c0,3.36,2.063,5.269,9.396,7.866    c13.903,5.042,18.028,9.55,18.028,19.555c0,22.536-29.639,28.571-43.236,8.632l7.869-7.562    C146.313,242.257,151.049,245.083,156.855,245.083L156.855,245.083z"/>
                                        <rect height="62.638" width="12.223" x="118.66" y="192.07"/>
                                        <g>
                                            <path d="M471,394.332V292.98c-29.646,18.515-201.373,106.348-369.99,138.008h330.455     C451.545,430.739,471,414.421,471,394.332z" style="fill:#FF6F00;"/>
                                        </g>
                                        <path d="M79.321,192.07H61.445v62.638h17.875c23.604,0,33.687-17.11,33.687-31.242C113.008,205.056,99.258,192.07,79.321,192.07z     M93.604,239.278c-3.743,3.361-8.861,4.889-16.729,4.889h-3.209v-41.403h3.209c7.868,0,12.757,1.299,16.729,5.041    c4.354,3.821,6.799,9.625,6.799,15.66C100.404,229.5,97.958,235.305,93.604,239.278z"/>
                                    </g>
                                </g>
                                <g id="Layer_1"/>
                                                </svg>
                        @else
                            <svg class="tw-w-16 tw-h-10" id="Layer_1" style="enable-background:new 0 0 512 512;" version="1.1" viewBox="0 0 512 512" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><g>
                                    <path d="M452,96H60c-15.5,0-27.9,12.5-28,28l0,0v0.3v263.4v0.3l0,0c0.2,15.5,12.5,28,28,28h392c15.6,0,28-12.7,28-28.3v0V124.3v0   C480,108.7,467.6,96,452,96z M77.1,128h357.7c6.9,0,12.1,5.1,13.1,12v20H64v-20.3C65,132.9,70.3,128,77.1,128z M434.9,384H77.1   c-6.9,0-12.1-4.9-13.1-11.7V256h384v116C447,378.9,441.7,384,434.9,384z"/>
                                    <rect height="16" width="192" x="96" y="304"/>
                                    <rect height="16" width="96" x="96" y="336"/>
                                    <rect height="48" width="64" x="352" y="304"/>
                                </g></svg>
                        @endif
                    </div>
                    <div>
                        <p class="tw-m-0 tw-text-sm tw-font-medium tw-text-gray-900">{{ \Illuminate\Support\Str::of($card->brand)->studly() }} ending in {{ $card->last_four }}</p>
                        <p class="tw-m-0 tw-mt-0.5 tw-text-xs tw-text-gray-600">Exp. date {{ \Illuminate\Support\Str::of($card->exp_month)->padLeft(2, '0') }}/{{ $card->exp_year }}</p>
                    </div>
                </div>
                <div class="tw-flex tw-space-x-4">
                    <div>
                        @if($card->id === $default_source_id)
                            <div class="tw-flex-none tw-rounded-md tw-bg-gray-900 tw-px-2 tw-py-1 tw-text-xs tw-font-medium tw-text-white tw-ring-1 tw-ring-inset tw-ring-gray-800/20 hover:tw-bg-gray-800">
                                Default
                            </div>
                        @else
                            <button type="button" wire:loading.attr="disabled" wire:target="setAsDefault" wire:click="setAsDefault('{{ $card->id }}')" class="tw-rounded-md tw-bg-white tw-px-2 tw-py-1 tw-text-xs tw-text-gray-700 tw-ring-1 tw-ring-inset tw-ring-gray-300 tw-shadow-sm hover:tw-bg-gray-50">

                                <span wire:loading.remove wire:target="setAsDefault('{{ $card->id }}')">
                                    Set as default
                                </span>
                                <span wire:loading wire:target="setAsDefault('{{ $card->id }}')">
                                    <svg class="tw-animate-spin tw-h-5 tw-w-5 tw-text-gray-200" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle class="tw-opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="tw-opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                </span>
                            </button>
                        @endif
                    </div>
                    <div>
                        <button type="button" wire:loading.attr="disabled" wire:target="removeCard" wire:click="removeCard('{{ $card->id }}')" class="tw-rounded-md tw-bg-white tw-px-2 tw-py-1.5 tw-text-sm tw-font-semibold tw-text-gray-900 tw-shadow-sm tw-ring-1 tw-ring-inset tw-ring-gray-300 hover:tw-bg-gray-50">
                            <span wire:loading.remove wire:target="removeCard('{{ $card->id }}')">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="tw-size-5 tw-flex-none tw-text-gray-400">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"/>
                                </svg>
                            </span>
                            <span wire:loading wire:target="removeCard('{{ $card->id }}')">
                                <svg class="tw-animate-spin tw-h-5 tw-w-5 tw-text-gray-200" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="tw-opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="tw-opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                            </span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    @endforeach
</div>

