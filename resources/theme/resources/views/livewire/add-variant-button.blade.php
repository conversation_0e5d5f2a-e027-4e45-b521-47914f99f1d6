@php
    /** @var App\Models\Product $product */
    /** @var string $style */
    /** @var string $variant_location */
    /** @var string $variant_layout */
    /** @var bool|null $rounded */

	$text_class = 'tw-text-theme-brand-color';

    $product_is_out_of_stock = $product->isOutOfStock();
    $global_in_stock = ! $product_is_out_of_stock;
@endphp

<div>
    @if($variant_layout === 'popper')
        <div
                x-data="popper(
                    '#variantButtonTrigger_{{ $product->id }}',
                    '#variantDropdown_{{ $product->id }}'
                )"
                x-on:click.away="close"
        >
            <div class="tw-relative variantsTableContainer" :class="open ? 'open' : ''">
                <button
                        class="tw-inline-flex tw-items-center tw-justify-center tw-gap-x-1 tw-w-full @if($cta_classes ?? false) {{ $cta_classes }} @else tw-px-3 tw-py-2 tw-rounded-none tw-rounded-b-lg tw-text-sm tw-font-semibold @endif btn {{ $style }}"
                        id="variantButtonTrigger_{{ $product->id }}"
                        type="button"
                        x-on:click="open = !open"
                >
                    Select Option
                    <svg class="tw-h-5 tw-w-5" :style="open ? 'transform: rotate(-90deg)' : ''" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M5.22 8.22a.75.75 0 0 1 1.06 0L10 11.94l3.72-3.72a.75.75 0 1 1 1.06 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L5.22 9.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd"/>
                    </svg>

                </button>

                <div style="display: none;" :style=" ! open ? 'display: none;' : 'display: block;'" class="tw-absolute tw-shadow-lg tw-z-20 tw-flex tw-w-full tw-left-1/2 tw--translate-x-1/2 md:tw-w-screen md:tw-max-w-max " id="variantDropdown_{{ $product->id }}">
                    <form class="tw-mt-2 tw-w-full tw-max-w-none tw-flex-auto tw-overflow-hidden tw-rounded-lg tw-bg-white tw-text-sm tw-leading-6 tw-ring-1 tw-ring-gray-900/5 md:tw-w-screen md:tw-max-w-md">
                        <div class="tw-px-3 tw-pt-1 tw-pb-2">
                            <div class="tw-flow-root">
                                <div class="tw--mx-4 tw--my-2 sm:tw--mx-6 lg:tw--mx-8">
                                    <div class="tw-inline-block tw-min-w-full tw-py-2 tw-align-middle sm:tw-px-6 lg:tw-px-8">
                                        <table class="tw-min-w-full tw-w-full tw-divide-y tw-divide-gray-300">
                                            <thead>
                                            <tr>
                                                <th scope="col" class="tw-pl-3 tw-overflow-x-hidden tw-pr-3 tw-text-left tw-text-xs tw-font-semibold tw-text-gray-900">
                                                    Product
                                                </th>
                                                <th scope="col" class="tw-pl-3 tw-py-2 tw-text-right tw-text-xs tw-font-semibold tw-text-gray-500">Price</th>
                                                <th scope="col" class="tw-relative tw-py-2 tw-pl-3 tw-text-right tw-text-xs tw-font-semibold tw-text-theme-brand-color tw-pr-3">
                                                    Savings
                                                </th>
                                            </tr>
                                            </thead>
                                            <tbody class="tw-divide-y tw-divide-gray-200">
                                            <tr class=" @if($product_is_out_of_stock) tw-bg-gray-50 @endif ">
                                                <td class="tw-py-3 tw-pl-3 tw-pr-3 tw-text-xs tw-text-gray-900 ">
                                                    <div class="tw-table tw-table-fixed tw-w-full">
                                                        <label for="product_{{ $product->id }}" class="tw-text-xs tw-m-0 tw-flex tw-items-center @if($product_is_out_of_stock) tw-cursor-not-allowed tw-italic tw-text-gray-500 tw-font-normal @else tw-cursor-pointer @endif">
                                                            <input
                                                                    id="product_{{ $product->id }}"
                                                                    type="radio"
                                                                    name="product_id"
                                                                    wire:model="selected_variant_id"
                                                                    value="{{ $product->id }}"
                                                                    @if($product_is_out_of_stock) disabled @else checked @endif
                                                                    class="tw-mt-0 tw-mr-1.5"
                                                            />
                                                            <span class="tw-w-full tw-max-w-full tw-whitespace-nowrap tw-text-ellipsis tw-overflow-x-hidden variantTitle" title="{{ $product->title }}">@if($product_is_out_of_stock)
                                                                    Out -
                                                                @endif{{ $product->title }}</span>
                                                        </label>
                                                    </div>
                                                </td>
                                                @if($product->isPricedByWeight() && setting('show_price_per_pound', true))
                                                    <td class="tw-w-1/5 tw-whitespace-nowrap tw-pl-2 tw-py-3 tw-text-right tw-text-sm @if( ! $product_is_out_of_stock) tw-font-semibold tw-text-gray-900 @endif">
                                                        &#36;{{ money($product->getUnitPrice()) }}
                                                        <span class="tw-text-xs tw-font-normal">/{{ __("messages.uom." . setting('weight_uom', 'pounds')) }}.</span>
                                                    </td>
                                                    <td class="tw-w-1/5 tw-relative tw-whitespace-nowrap tw-py-3 tw-pl-3 tw-pr-3 tw-text-right tw-text-sm tw-font-semibold tw-text-theme-brand-color">
                                                        @if($product->isOnSale())
                                                            &#36;{{ money($product->getUnitSavings()) }}
                                                            <span class="tw-text-xs tw-font-normal">/{{ __("messages.uom." . setting('weight_uom', 'pounds')) }}.</span>
                                                        @else
                                                            ---
                                                        @endif
                                                    </td>
                                                @else
                                                    <td class="tw-w-1/5 tw-whitespace-nowrap tw-pl-2 tw-py-3 tw-text-right tw-text-sm @if( ! $product_is_out_of_stock) tw-font-semibold tw-text-gray-900 @endif">
                                                        &#36;{{ money($product->getPrice()) }}
                                                    </td>
                                                    <td class="tw-w-1/5 tw-relative tw-whitespace-nowrap tw-py-3 tw-pl-3 tw-pr-3 tw-text-right tw-text-sm tw-font-semibold tw-text-theme-brand-color">
                                                        {{ $product->isOnSale() ? '&#36;' . money($product->getSavings()) : '---' }}
                                                    </td>
                                                @endif
                                            </tr>
                                            @foreach ($product->variants->filter(fn($variant) => $variant->visible) as $variant)
                                                @php
                                                    /** @var \App\Models\Product $variant */
                                                    $variant_is_out_of_stock = $variant->isOutOfStock();

                                                    if ( ! $global_in_stock && ! $variant_is_out_of_stock) {
                                                        $global_in_stock = true;
                                                    }
                                                @endphp
                                                <tr class="@if($variant->isOutOfStock()) tw-bg-gray-50 @endif ">
                                                    <td class="tw-py-3 tw-pl-3 tw-pr-3 tw-text-xs tw-text-gray-900">
                                                        <div class="tw-table tw-table-fixed tw-w-full">
                                                            <label for="variant_{{ $variant->id }}" class="tw-text-xs tw-m-0 tw-flex tw-items-center @if($variant_is_out_of_stock) tw-cursor-not-allowed tw-italic tw-text-gray-500 tw-font-normal @else tw-font-semibold tw-cursor-pointer @endif">
                                                                <input
                                                                        id="variant_{{ $variant->id }}"
                                                                        type="radio"
                                                                        name="product_id"
                                                                        wire:model="selected_variant_id"
                                                                        value="{{ $variant->id }}"
                                                                        @if($variant_is_out_of_stock) disabled @else checked @endif
                                                                        class="tw-mt-0 tw-mr-1.5"
                                                                />
                                                                <span class="tw-w-full tw-max-w-full tw-whitespace-nowrap tw-text-ellipsis tw-overflow-x-hidden variantTitle" title="{{ $variant->title }}">@if($variant_is_out_of_stock)
                                                                        Out -
                                                                    @endif{{ $variant->title }}</span>
                                                            </label>
                                                        </div>
                                                    </td>

                                                    @if($variant->isPricedByWeight() && setting('show_price_per_pound', true))
                                                        <td class="tw-w-1/5 tw-whitespace-nowrap tw-pl-2 tw-py-3 tw-text-right tw-text-sm @if( ! $variant_is_out_of_stock) tw-font-semibold tw-text-gray-900 @endif">
                                                            &#36;{{ money($variant->getUnitPrice()) }}
                                                            <span class="tw-text-xs tw-font-normal">/{{ __("messages.uom." . setting('weight_uom', 'pounds')) }}.</span>
                                                        </td>
                                                        <td class="tw-w-1/5 tw-relative tw-whitespace-nowrap tw-py-3 tw-pl-3 tw-pr-3 tw-text-right tw-text-sm tw-text-theme-brand-color @if( ! $variant_is_out_of_stock) tw-font-semibold @endif">
                                                            @if($variant->isOnSale())
                                                                &#36;{{ money($variant->getUnitSavings()) }}
                                                                <span class="tw-text-xs tw-font-normal">/{{ __("messages.uom." . setting('weight_uom', 'pounds')) }}.
                                                                </span>
                                                            @else
                                                                ---
                                                            @endif
                                                        </td>
                                                    @else
                                                        <td class="tw-w-1/5 tw-whitespace-nowrap tw-pl-2 tw-py-3 tw-text-right tw-text-sm @if( ! $variant_is_out_of_stock) tw-font-semibold tw-text-gray-900 @endif">
                                                            &#36;{{ money($variant->getPrice()) }}
                                                        </td>
                                                        <td class="tw-w-1/5 tw-relative tw-whitespace-nowrap tw-py-3 tw-pl-3 tw-pr-3 tw-text-right tw-text-sm tw-text-theme-brand-color @if( ! $variant_is_out_of_stock) tw-font-semibold @endif">
                                                            {{ $variant->isOnSale() ? '&#36;' . money($variant->getSavings()) : '---' }}
                                                        </td>
                                                    @endif
                                                </tr>
                                            @endforeach

                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <div class="variantAddToCartContainer">
                                @if ($global_in_stock)
                                    @if(auth()->guest())
                                        <a href="{{ route('register') }}" class="tw-w-full">
                                            <button type="button" class="tw-w-full tw-px-3 tw-py-2 tw-text-sm tw-font-semibold btn {{ $style }} productListing__addToCartButton--grid">{{ $product->cartActionLabel() }}</button>
                                        </a>
                                    @elseif($confirm_delivery_method)
                                        <div class="productListing__addToCartContainer--grid">
                                            <button type="button" x-data x-on:click="$dispatch('open-modal-confirm-delivery-method', { product_id: {{ $product->id }}, metadata: {{ json_encode($metadata) }} })" class="tw-block tw-w-full @if($cta_classes ?? false) {{ $cta_classes }} @else tw-px-3 tw-py-2 tw-rounded-none tw-rounded-b-lg tw-text-sm tw-font-semibold @endif btn {{ $style }} productListing__addToCartButton--grid">
                                                {{ $product->cartActionLabel() }}
                                            </button>
                                        </div>
                                    @else
                                        <button
                                                class="tw-w-full tw-px-3 tw-py-2 tw-text-sm tw-font-semibold btn {{ $style }} productListing__addToCartButton--grid"
                                                type="button"
                                                wire:click="add"
                                                wire:loading.attr="disabled"
                                                wire:target="add"
                                        >
                                            <span wire:loading.remove wire:target="add">
                                                @if($has_subscription)
                                                    <span></span>
                                                    {{ $cta_label ?? $product->orderActionLabel(true) }}
                                                @elseif($has_order)
                                                    {{ $cta_label ?? $product->orderActionLabel() }}
                                                @else
                                                    {{ $cta_label ?? $product->cartActionLabel() }}
                                                @endif
                                            </span>
                                            <span wire:loading wire:target="add" style="display:none;">Adding...</span>
                                        </button>
                                    @endif
                                @else
                                    <button class="tw-w-full tw-px-3 tw-py-2 tw-text-sm tw-font-semibold btn {{ $style }}" type="button" disabled>
                                        Sold Out
                                    </button>
                                @endif
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    @else
        <div class="tw-rounded-md ">
            <div class="tw-m-0 tw-pb-2 tw-group tw-grid tw-grid-cols-5 tw-pl-4 tw-pr-6">
                <span class="tw-col-span-3 tw-text-gray-900 tw-text-sm tw-w-full tw-max-w-full tw-whitespace-nowrap tw-text-ellipsis tw-overflow-x-hidden">
                    Product
                </span>
                <span class="tw-ml-6 tw-pl-1 tw-text-sm tw-text-gray-900 md:tw-ml-0 md:tw-pl-0 tw-text-right">
                    Price
                </span>
                <span class="tw-ml-6 tw-pl-1 tw-text-sm tw-text-theme-brand-color md:tw-ml-0 md:tw-pl-0 tw-text-right">
                    Savings
                </span>
            </div>

            <fieldset aria-label="Variant Options" class="tw-relative tw--space-y-px tw-rounded-md tw-bg-white">
                <label aria-label="{{ $product->title }}" aria-description="{{ $product->title }}" class="tw-m-0 tw-group tw-cursor-pointer tw-border tw-border-gray-200 tw-p-4 first:tw-rounded-tl-md first:tw-rounded-tr-md last:tw-rounded-bl-md last:tw-rounded-br-md focus:tw-outline-none has-[:checked]:tw-relative has-[:checked]:tw-border-theme-brand-color/50 has-[:disabled]:tw-bg-gray-100 has-[:disabled]:tw-cursor-not-allowed tw-grid tw-grid-cols-5 tw-pl-4 tw-pr-6">
                    <span class="tw-flex tw-items-center tw-gap-3 tw-text-sm tw-col-span-3">
                        <input name="product_id"
                               type="radio"
                               wire:model="selected_variant_id"
                               value="{{ $product->id }}"
                               @if($product_is_out_of_stock) disabled @else checked @endif
                               class="tw-m-0 tw-relative tw-size-4 tw-appearance-none tw-rounded-full tw-border tw-border-gray-300 tw-bg-white before:tw-absolute before:tw-inset-1 before:tw-rounded-full before:tw-bg-white checked:tw-border-theme-brand-color/80 checked:tw-bg-theme-brand-color focus:tw-outline focus:tw-outline-2 focus:tw-outline-offset-2 focus:tw-outline-theme-brand-color focus-visible:tw-outline focus-visible:tw-outline-2 focus-visible:tw-outline-offset-2 focus-visible:tw-outline-theme-brand-color forced-colors:tw-appearance-auto forced-colors:before:tw-hidden not(:checked)]:before:tw-hidden"
                        >
                        <span class="tw-font-normal tw-text-gray-700 group-has-[:checked]:tw-text-gray-900 group-has-[:checked]:tw-font-semibold">
                            <span class="tw-w-full tw-max-w-full tw-whitespace-nowrap tw-text-ellipsis tw-overflow-x-hidden" title="{{ $product->title }}">
                                @if($product_is_out_of_stock)
                                    Out -
                                @endif{{ $product->title }}</span>
                        </span>
                    </span>
                    @if($product->isPricedByWeight() && setting('show_price_per_pound', true))
                        <span class="tw-ml-6 tw-pl-1 tw-text-sm md:tw-ml-0 md:tw-pl-0 tw-text-right">
                            <span class="tw-font-normal tw-text-gray-500 group-has-[:checked]:tw-font-semibold group-has-[:checked]:tw-text-gray-700">
                                &#36;{{ money($product->getUnitPrice()) }}
                                <span class="tw-text-xs tw-font-normal">/{{ __("messages.uom." . setting('weight_uom', 'pounds')) }}.</span>
                            </span>
                        </span>
                        <span class="tw-font-normal tw-ml-6 tw-pl-1 tw-text-sm tw-text-gray-500 md:tw-ml-0 md:tw-pl-0 tw-text-right">
                            @if($product->isOnSale())
                                &#36;{{ money($product->getUnitSavings()) }}
                                <span class="tw-text-xs">/{{ __("messages.uom." . setting('weight_uom', 'pounds')) }}.</span>
                            @else
                                ---
                            @endif
                        </span>
                    @else
                        <span class="tw-ml-6 tw-pl-1 tw-text-sm md:tw-ml-0 md:tw-pl-0 tw-text-right">
                            <span class="tw-font-normal tw-text-gray-500 group-has-[:checked]:tw-font-semibold group-has-[:checked]:tw-text-gray-700">
                                &#36;{{ money($product->getPrice()) }}
                            </span>
                        </span>
                        <span class="tw-font-normal tw-ml-6 tw-pl-1 tw-text-sm @if($product->isOnSale()) tw-text-theme-brand-color @else tw-text-gray-500 @endif group-has-[:checked]:tw-font-semibold md:tw-ml-0 md:tw-pl-0 tw-text-right">
                            {{ $product->isOnSale() ? '&#36;' . money($product->getSavings()) : '---' }}
                        </span>
                    @endif
                </label>
                @foreach($product->variants->filter(fn($variant) => $variant->visible) as $variant)
                    @php
                        /** @var \App\Models\Product $variant */
                        $variant_is_out_of_stock = $variant->isOutOfStock();

                        if ( ! $global_in_stock && ! $variant_is_out_of_stock) {
                            $global_in_stock = true;
                        }

                    @endphp

                    <label aria-label="{{ $variant->title }}" aria-description="{{ $variant->title }}" class="tw-m-0 tw-group tw-cursor-pointer tw-border tw-border-gray-200 tw-p-4 first:tw-rounded-tl-md first:tw-rounded-tr-md last:tw-rounded-bl-md last:tw-rounded-br-md focus:tw-outline-none has-[:checked]:tw-relative has-[:checked]:tw-border-theme-brand-color/50 has-[:disabled]:tw-bg-gray-100 has-[:disabled]:tw-cursor-not-allowed tw-grid tw-grid-cols-5 tw-pl-4 tw-pr-6">
                        <span class="tw-flex tw-items-center tw-gap-3 tw-text-sm tw-col-span-3">
                            <input name="product_id"
                                   type="radio"
                                   wire:model="selected_variant_id"
                                   value="{{ $variant->id }}"
                                   @if($variant_is_out_of_stock) disabled @endif
                                   class="tw-m-0 tw-relative tw-size-4 tw-appearance-none tw-rounded-full tw-border tw-border-gray-300 tw-bg-white before:tw-absolute before:tw-inset-1 before:tw-rounded-full before:tw-bg-white checked:tw-border-theme-brand-color/80 checked:tw-bg-theme-brand-color focus:tw-outline focus:tw-outline-2 focus:tw-outline-offset-2 focus:tw-outline-theme-brand-color focus-visible:tw-outline focus-visible:tw-outline-2 focus-visible:tw-outline-offset-2 focus-visible:tw-outline-theme-brand-color forced-colors:tw-appearance-auto forced-colors:before:tw-hidden not(:checked)]:before:tw-hidden"
                            >
                            <span class="tw-font-normal tw-text-gray-700 group-has-[:checked]:tw-text-gray-900 group-has-[:checked]:tw-font-semibold">
                                <span class="tw-w-full tw-max-w-full tw-whitespace-nowrap tw-text-ellipsis tw-overflow-x-hidden" title="{{ $variant->title }}">
                                    @if($variant_is_out_of_stock)
                                        Out -
                                    @endif{{ $variant->title }}</span>
                            </span>
                        </span>
                        @if($variant->isPricedByWeight() && setting('show_price_per_pound', true))
                            <span class="tw-ml-6 tw-pl-1 tw-text-sm md:tw-ml-0 md:tw-pl-0 tw-text-right">
                                <span class="tw-font-normal tw-text-gray-500 group-has-[:checked]:tw-font-semibold group-has-[:checked]:tw-text-gray-700">
                                    &#36;{{ money($variant->getUnitPrice()) }}
                                    <span class="tw-text-xs tw-font-normal">/{{ __("messages.uom." . setting('weight_uom', 'pounds')) }}.</span>
                                </span>
                            </span>
                            <span class="tw-font-normal tw-ml-6 tw-pl-1 tw-text-sm @if($variant->isOnSale()) tw-text-theme-brand-color @else tw-text-gray-500 @endif group-has-[:checked]:tw-font-semibold md:tw-ml-0 md:tw-pl-0 tw-text-right">
                                @if($variant->isOnSale())
                                    &#36;{{ money($variant->getUnitSavings()) }}
                                    <span class="tw-text-xs">/{{ __("messages.uom." . setting('weight_uom', 'pounds')) }}.</span>
                                @else
                                    ---
                                @endif
                            </span>
                        @else
                            <span class="tw-ml-6 tw-pl-1 tw-text-sm md:tw-ml-0 md:tw-pl-0 tw-text-right">
                                <span class="tw-font-normal tw-text-gray-500 group-has-[:checked]:tw-font-semibold group-has-[:checked]:tw-text-gray-700">
                                    &#36;{{ money($variant->getPrice()) }}
                                </span>
                            </span>
                            <span class="tw-font-normal tw-ml-6 tw-pl-1 tw-text-sm @if($variant->isOnSale()) tw-text-theme-brand-color @else tw-text-gray-500 @endif group-has-[:checked]:tw-font-semibold md:tw-ml-0 md:tw-pl-0 tw-text-right">
                                {{ $variant->isOnSale() ? '&#36;' . money($variant->getSavings()) : '---' }}
                            </span>
                        @endif
                    </label>
                @endforeach
            </fieldset>

            <div class="tw-mt-3">
                @if($global_in_stock)
                    @if(auth()->guest())
                        <a href="{{ route('register') }}" class="tw-w-full">
                            <button type="button" class="tw-w-full tw-px-4 tw-py-3 tw-text-base tw-font-semibold tw-rounded-md {{ $style }}">{{ $product->cartActionLabel() }}</button>
                        </a>
                    @elseif($confirm_delivery_method)
                        <div class="productListing__addToCartContainer--grid">
                            <button type="button" x-data x-on:click="$dispatch('open-modal-confirm-delivery-method', { product_id: {{ $product->id }}, metadata: {{ json_encode($metadata) }} })" class="tw-block tw-w-full @if($cta_classes ?? false) {{ $cta_classes }} @else tw-px-3 tw-py-2 tw-rounded-none tw-rounded-b-lg tw-text-sm tw-font-semibold @endif btn {{ $style }} productListing__addToCartButton--grid">
                                {{ $product->cartActionLabel() }}
                            </button>
                        </div>
                    @else
                        <button
                                class="tw-w-full tw-px-4 tw-py-3 tw-text-base tw-font-semibold tw-rounded-md {{ $style }}"
                                type="button"
                                wire:click="add"
                                wire:loading.attr="disabled"
                                wire:target="add"
                        >
                            <span wire:loading.remove wire:target="add">
                                @if($has_subscription)
                                    <span></span>
                                    {{ $cta_label ?? $product->orderActionLabel(true) }}
                                @elseif($has_order)
                                    {{ $cta_label ?? $product->orderActionLabel() }}
                                @else
                                    {{ $cta_label ?? $product->cartActionLabel() }}
                                @endif
                            </span>
                            <span wire:loading wire:target="add" style="display:none;">Adding...</span>
                        </button>
                    @endif
                @else
                    <button class="tw-block tw-w-full tw-px-4 tw-py-3 tw-bg-gray-200 tw-text-gray-700 tw-rounded-md tw-text-base tw-font-semibold tw-cursor-not-allowed" type="button" disabled>
                        Sold Out
                    </button>
                @endif
            </div>
        </div>
    @endif
</div>
