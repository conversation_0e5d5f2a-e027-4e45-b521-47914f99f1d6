@php
    /**
     * @var \App\Models\Order $order
     */
    $delivery_method = $order->pickup;
@endphp

<div class="tw-h-full tw-bg-white tw-flex tw-flex-col tw-pointer-events-auto tw-w-screen tw-max-w-md">
    <div class="tw-flex-1 tw-overflow-hidden tw-flex tw-flex-col tw-bg-white tw-shadow-xl">
        <div class="tw-flex-1 tw-overflow-y-auto tw-py-6 tw-px-4 sm:tw-px-6">
            <div>
                <div class="tw-w-full tw-pb-1 tw-flex tw-items-center tw-justify-between tw-text-theme-brand-color">
                    <div class="tw-flex tw-items-baseline">
                        <h2 class="tw-m-0 tw-text-xl tw-font-body tw-font-medium" id="slide-over-title">Order #{{ $order->id }}</h2>
                        @if($has_subscription && $order->isFromBlueprint())
                            <span class="tw-ml-2 tw-inline-flex tw-items-center tw-rounded-md tw-bg-theme-brand-color/10 tw-px-2.5 tw-py-0.5 tw-text-sm tw-font-medium tw-text-theme-brand-color/80">
                                <svg class="tw--ml-0.5 tw-mr-1.5 tw-h-3 tw-w-3 tw-theme-brand-color/40" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 4.5c1.215 0 2.417.055 3.604.162a.68.68 0 01.615.597c.124 1.038.208 2.088.25 3.15l-1.689-1.69a.75.75 0 00-1.06 1.061l2.999 3a.75.75 0 001.06 0l3.001-3a.75.75 0 10-1.06-1.06l-1.748 1.747a41.31 41.31 0 00-.264-3.386 2.18 2.18 0 00-1.97-1.913 41.512 41.512 0 00-7.477 0 2.18 2.18 0 00-1.969 1.913 41.16 41.16 0 00-.16 ********** 0 101.495.12c.041-.52.093-1.038.154-1.552a.68.68 0 01.615-.597A40.012 40.012 0 0110 4.5zM5.281 9.22a.75.75 0 00-1.06 0l-3.001 3a.75.75 0 101.06 1.06l1.748-1.747c.042 1.141.13 2.27.264 3.386a2.18 2.18 0 001.97 1.913 41.533 41.533 0 007.477 0 2.18 2.18 0 001.969-1.913c.064-.534.117-1.071.16-1.61a.75.75 0 10-1.495-.12c-.041.52-.093 1.037-.154 1.552a.68.68 0 01-.615.597 40.013 40.013 0 01-7.208 0 .68.68 0 01-.615-.597 39.785 39.785 0 01-.25-3.15l1.689 1.69a.75.75 0 001.06-1.061l-2.999-3z" clip-rule="evenodd"/>
                                </svg>
                                Subscription
                            </span>
                        @endif
                    </div>
                    <div class="tw-ml-3 tw-flex tw-h-7 tw-items-center">
                        <button type="button" @click="open = false" class="tw--m-2 tw-p-2 tw-text-theme-brand-color/80 hover:tw-text-theme-brand-color/60">
                            <span class="tw-sr-only">Close panel</span>
                            <svg class="tw-h-6 tw-w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"/>
                            </svg>
                        </button>
                    </div>
                </div>

                @if($delivery_method->display_cart_shipping_calculator && $delivery_method->deliveryFeeHasCap())
                    <x-theme::free-shipping-calculator
                            :order="$order"
                            :delivery_method="$delivery_method"
                    />
                @endif
            </div>

            <div class="tw-mt-2 tw-space-y-2">
                <div class="tw-flex tw-items-center tw-text-sm tw-text-gray-500">
                    <svg class="tw-mr-1.5 tw-h-4 tw-w-4 tw-flex-shrink-0 tw-text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                        <path fill-rule="evenodd" d="M5.75 2a.75.75 0 01.75.75V4h7V2.75a.75.75 0 011.5 0V4h.25A2.75 2.75 0 0118 6.75v8.5A2.75 2.75 0 0115.25 18H4.75A2.75 2.75 0 012 15.25v-8.5A2.75 2.75 0 014.75 4H5V2.75A.75.75 0 015.75 2zm-1 5.5c-.69 0-1.25.56-1.25 1.25v6.5c0 .69.56 1.25 1.25 1.25h10.5c.69 0 1.25-.56 1.25-1.25v-6.5c0-.69-.56-1.25-1.25-1.25H4.75z" clip-rule="evenodd"/>
                    </svg>
                    {{ $delivery_method->isDeliveryZone() ? 'Delivery' : 'Pickup' }} on {{ $order->pickupDatetime()?->format('M jS') ?? 'TBA' }}
                </div>
                @if ($order->canBeModified())
                    <div class="tw-mt-2 tw-flex tw-items-center tw-text-sm tw-text-gray-500">
                        <svg class="tw-mr-1.5 tw-h-4 tw-w-4 tw-flex-shrink-0 tw-text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>

                        Edit until {{ $order->deadlineDatetime()->format('M d | h:iA') }}
                    </div>
                @endif
            </div>

            <div class="tw-mt-6 tw-flow-root">
                <livewire:theme.order-items :order="$order"/>
            </div>
        </div>
    </div>

    <div class="tw-bg-white">


        <div class="tw-relative tw-border-t tw-px-4 sm:tw-px-6 " x-data="{ is_showing_total_details: false }">
            <div
                    x-show="is_showing_total_details"
                    x-cloak
                    x-transition:enter="tw-transform tw-transition tw-ease-in-out tw-duration-500 sm:tw-duration-700"
                    x-transition:enter-start="tw-translate-y-full"
                    x-transition:enter-end="tw-translate-y-0"
                    x-transition:leave="tw-transform tw-transition tw-ease-in-out tw-duration-500 sm:tw-duration-700"
                    x-transition:leave-start="tw-translate-y-0"
                    x-transition:leave-end="tw-translate-y-full"
                    class="tw-pt-4"
            >
                <x-theme::order-summary :order="$order"/>
            </div>
            <div class="tw-relative tw-z-10 tw-bg-white tw-border-gray-200 tw-cursor-pointer tw-pb-6 hover:tw-text-gray-700">
                <div @click="is_showing_total_details = ! is_showing_total_details">
                    <div class="tw-pt-6 tw-flex tw-justify-between">
                        <div class="tw-flex tw-items-center tw-space-x-2 tw-text-base tw-font-medium tw-text-gray-900">
                            <p class="tw-m-0">Total</p>
                            <svg x-show=" ! is_showing_total_details" id="total-chevron-down" class="tw-w-5 tw-h-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clip-rule="evenodd"/>
                            </svg>
                            <svg x-show="is_showing_total_details" id="total-chevron-up" class="tw-w-5 tw-h-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M14.77 12.79a.75.75 0 01-1.06-.02L10 8.832 6.29 12.77a.75.75 0 11-1.08-1.04l4.25-4.5a.75.75 0 011.08 0l4.25 4.5a.75.75 0 01-.02 1.06z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                        <p class="tw-m-0">${{ money($order->total) }}</p>
                    </div>
                </div>

                <div class="tw-pt-6 tw-space-y-6">
                    <a href="{{ route('customer.orders.show', compact('order')) }}" class="tw-no-underline">
                        <button type="button" class="tw-w-full tw-flex tw-items-center tw-justify-center tw-rounded-md tw-border tw-border-transparent tw-bg-theme-action-color tw-px-6 tw-py-3 tw-text-base tw-font-medium tw-text-white tw-shadow-sm hover:tw-bg-theme-action-color/70">
                            Manage order
                        </button>
                    </a>
                    <div class="tw-mt-6 tw-flex tw-justify-center tw-text-center tw-text-sm tw-text-gray-500">
                        <p class="tw-m-0">
                            <button type="button" @click="open = false" class="tw-font-medium tw-text-theme-action-color hover:tw-text-theme-action-color/70">
                                {{ __('messages.cart.keep_shopping') }}
                                <span aria-hidden="true"> &rarr;</span>
                            </button>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

