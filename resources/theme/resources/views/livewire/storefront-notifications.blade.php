<div class="tw-reset">
    <div x-data="storefrontNotifications" x-on:storefront-notification-sent.window="event => add(event.detail.notification)" aria-live="assertive" class="tw-z-50 tw-pointer-events-none tw-fixed tw-inset-0 tw-flex tw-items-end tw-px-4 tw-py-6 sm:tw-items-start sm:tw-p-6">
        <div class="tw-flex tw-w-full tw-flex-col tw-items-center tw-space-y-4 sm:tw-items-end">
            <template x-for="notification in notifications" :key="notification.id">
                <div x-show="notification.visible"
                     x-transition:enter="tw-transform tw-ease-out tw-duration-300 tw-transition"
                     x-transition:enter-start="tw-translate-y-2 tw-opacity-0 sm:tw-translate-y-0 sm:tw-translate-x-2"
                     x-transition:enter-end="tw-translate-y-0 tw-opacity-100 sm:tw-translate-x-0"
                     x-transition:leave="tw-transition tw-ease-in tw-duration-100"
                     x-transition:leave-start="tw-opacity-100"
                     x-transition:leave-end="tw-opacity-0"
                     class="tw-pointer-events-auto tw-w-full tw-max-w-sm tw-overflow-hidden tw-rounded-lg tw-bg-white tw-shadow-xl tw-ring-1 tw-ring-black tw-ring-opacity-5"
                >
                    <div class="tw-p-4">
                        <div class="tw-flex tw-items-start">
                            <div class="tw-flex-shrink-0">
                                <svg x-show="notification.level === 'success'" class="tw-h-6 tw-w-6 tw-text-theme-brand-color" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <svg x-show="notification.level === 'error'" class="tw-h-6 tw-w-6 tw-text-red-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"></path>
                                </svg>

                            </div>
                            <div class="tw-ml-3 tw-w-0 tw-flex-1 tw-pt-0.5">
                                <p class="tw-m-0 tw-text-sm tw-font-medium tw-text-gray-900" x-text="notification.title"></p>
                                <p x-show="notification.message" class="tw-m-0 tw-mt-1 tw-text-sm tw-text-gray-500" x-text="notification.message"></p>
                            </div>
                            <div class="tw-ml-4 tw-flex tw-flex-shrink-0">
                                <button type="button" x-on:click="hide(notification.id)" class="tw-inline-flex tw-rounded-md tw-bg-white tw-text-gray-400 hover:tw-text-gray-500 focus:tw-outline-none focus:tw-ring-2 focus:tw-ring-keppel-500 focus:tw-ring-offset-2">
                                    <span class="tw-sr-only">Close</span>
                                    <svg class="tw-h-5 tw-w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                        <path d="M6.28 5.22a.75.75 0 00-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 101.06 1.06L10 11.06l3.72 3.72a.75.75 0 101.06-1.06L11.06 10l3.72-3.72a.75.75 0 00-1.06-1.06L10 8.94 6.28 5.22z"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
        </div>
    </div>
</div>

