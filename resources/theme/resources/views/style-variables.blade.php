:root {
--display-font-family: {{ app('theme')->setting('heading_font', 'Helvetica Neue, Helvetica, Arial, sans-serif') }};
--body-font-family: {{ app('theme')->setting('paragraph_font', 'Helvetica Neue, Helvetica, Arial, sans-serif') }};

--font-size: {{ app('theme')->setting('paragraph_font_size', '16px') }}

--logo-padding: {{ app('theme')->setting('logo_padding', '10') . 'px' }};
--logo-height: {{ app('theme')->setting('logo_height',  '100') . 'px' }};

--header-width: {{ app('theme')->setting('header_width', '1366px') }};
--header-border-position: {{ app('theme')->setting('header_border_position', 'bottom') }};
--header-border-size: {{ app('theme')->setting('header_border_size', '0px') }};
--header-bg-color: {{ themeColorForTailwind(app('theme')->setting('header_bg_color', '#EEEEEE')) }};
--header-border-color: {{ themeColorForTailwind(app('theme')->setting('header_border_color', 'transparent')) }};

--brand-color: {!! themeColorForTailwind(app('theme')->setting('brand_color', '#34B393')) !!};
--brand-color-inverted: {!! themeColorForTailwind(app('theme')->setting('brand_color_inverted', '#FFFFFF')) !!};

--background-color: {{ themeColorForTailwind(app('theme')->setting('body_bg', '#FFF')) }};
--text-color: {{ themeColorForTailwind(app('theme')->setting('body_text_color', '#3d3d3d')) }};
--link-color: {{ themeColorForTailwind(app('theme')->setting('link_color', '#3d3d3d')) }};

--action-color: {{ themeColorForTailwind(app('theme')->setting('action_color', '#34B393')) }};
--action-color-inverted: {{ themeColorForTailwind(app('theme')->setting('action_color_inverted', '#34B393')) }};

--announcement-bar-bg-color: {{ themeColorForTailwind(app('theme')->setting('announcement_bar_bg_color', '#FFF')) }};
--announcement-bar-text-color: {{ themeColorForTailwind(app('theme')->setting('announcement_bar_text_color', '#3d3d3d')) }};
--announcement-bar-link-color: {{ themeColorForTailwind(app('theme')->setting('announcement_bar_link_color', '#3d3d3d')) }};

--main-navigation-bg-color: {{ themeColorForTailwind(app('theme')->setting('main_navigation_bg', '#EEEEEE')) }};
--main-navigation-link-color: {{ themeColorForTailwind(app('theme')->setting('main_navigation_link_color', '#CF3D2E')) }};
--main-navigation-link-color-hover: {{ themeColorForTailwind(app('theme')->setting('main_navigation_link_color_hover', '#EEEEEE')) }};
--main-navigation-link-bg-color: {{ themeColorForTailwind(app('theme')->setting('main_navigation_link_bg_color', '#EEEEEE')) }};
--main-navigation-link-font-size: {{ app('theme')->setting('main_navigation_link_font_size', '14px') }};
--main-navigation-link-alignment: {{ app('theme')->setting('main_navigation_link_alignment', 'center') }};

--auxiliary-bg-color: {{ themeColorForTailwind(app('theme')->setting('auxiliary_bg_color', 'transparent')) }};
--auxiliary-border-color: {{ themeColorForTailwind(app('theme')->setting('auxiliary_border_color', 'transparent')) }};
--auxiliary-link-color: {{ themeColorForTailwind(app('theme')->setting('auxiliary_link_color', 'inherit')) }};

--order-status-bg-color: {{ themeColorForTailwind(app('theme')->setting('order_status_bg', '#f8f8f8')) }};
--order-status-color: {{ themeColorForTailwind(app('theme')->setting('order_status_color', '#777')) }};

--store-menu-bg_color: {{ themeColorForTailwind(app('theme')->setting('store_menu_bg', '#f8f8f8')) }};
--store-menu-color: {{ themeColorForTailwind(app('theme')->setting('store_menu_color', '#777')) }};

--footer-bg-color: {{ themeColorForTailwind(app('theme')->setting('footer_bg',  '#2d2d2d')) }};
--footer-color: {{ themeColorForTailwind(app('theme')->setting('footer_text_color',  '#777')) }};
--footer-link-color: {{ themeColorForTailwind(app('theme')->setting('footer_link_color',  '#FFFFFF')) }};
}

/*Custom Styles*/
{!! $theme->custom_css !!}
