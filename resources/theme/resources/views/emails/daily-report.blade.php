<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title></title>
    <style>
        table {
            width: 100%;
            max-width: 600px;
            margin: 0 auto;
            border-collapse: collapse;
            border: none;
            font-family: sans-serif;
        }
        table thead tr th {
            text-align: left;
        }
        table tbody tr {
            border-bottom: solid 1px #eee;
        }
        table tbody tr td {
            padding: 6px 6px;
            text-align: left;
            color: #4d4d4d;
            font-size: 13px;
        }
        
        hr {
            border: solid 2px #efefef;
            max-width: 600px;
            margin-top: 50px;
        }
    </style>
</head>
<body>
@if($inventory->count())
    <div style="padding: 20px 0; width: 100%; 
    text-align: center; font-family: 'Trebuchet MS', sans-serif; font-size: 22px; color: #555">
    Low Inventory</div>
    <table>
        <tbody>
        @foreach($inventory as $product)
            <tr>
                <td>{{ $product->title }}</td>
                <td>{{ $product->inventory }}</td>
            </tr>
        @endforeach
        </tbody>
    </table>
@endif

@if($orders->count())
<hr>
    <div style="padding: 20px 0; width: 100%;
    text-align: center; font-family: 'Trebuchet MS', sans-serif; font-size: 22px; color: #555">
    Orders</div>
    <table>
        <tbody>
        @foreach($orders as $order)
            <tr>
                <td class="text-center">{{ $order->customer_first_name }} {{ $order->customer_last_name }}</td>
                <td class="text-center">&#36;{{ money($order->total) }}</td>
            </tr>
        @endforeach
        </tbody>
    </table>

@endif

@if($users->count())
<hr>
    <div style="padding: 20px 0; width: 100%;
    text-align: center; font-family: 'Trebuchet MS', sans-serif; font-size: 22px; color: #555">
    Customers</div>
    <table>
        <tbody>
        @foreach($users as $user)
            <tr>
                <td class="text-center">{{ $user->full_name }}</td>
                <td class="text-center">{{ $user->email }}</td>
            </tr>
        @endforeach
        </tbody>
    </table>
@endif
</body>
</html>