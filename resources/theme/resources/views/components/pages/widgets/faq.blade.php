@props(['widget'])

@php
    $padding_top = match ($widget['settings']['padding']['top'] ?? '') {
        'sm' => 'tw-pt-6 lg:tw-pt-10',
        'md' => 'tw-pt-12 lg:tw-pt-16',
        'lg' => 'tw-pt-24 lg:tw-pt-32',
        'xl' => 'tw-pt-32 lg:tw-pt-48',
        default => '',
    };

    $padding_bottom = match ($widget['settings']['padding']['bottom'] ?? '') {
        'sm' => 'tw-pb-6 lg:tw-pb-10',
        'md' => 'tw-pb-12 lg:tw-pb-16',
        'lg' => 'tw-pb-24 lg:tw-pb-32',
        'xl' => 'tw-pb-32 lg:tw-pb-48',
        default => '',
    };

    $max_width = match ($widget['settings']['max_width'] ?? '') {
        'sm' => 'tw-max-w-lg',
        'md' => 'tw-max-w-4xl',
        'lg' => 'tw-max-w-6xl',
        'xl' => 'tw-max-w-7xl',
        default => '',
    };
@endphp

<div @if(!empty($widget['settings']['html_id'] ?? '')) id="{{ $widget['settings']['html_id'] }}" @endif class="tw-relative tw-w-full">
    <div class="tw-px-6 sm:tw-px-6 lg:tw-px-8 {{ $padding_top }} {{ $padding_bottom }}">
        <div class="tw-relative tw-mx-auto {{ $max_width }}">

            <div class="tw-mx-auto tw-max-w-2xl tw-text-center">
                <h2 class="tw-text-4xl tw-font-semibold tw-tracking-tight tw-text-gray-900 sm:tw-text-5xl">
                    {{ $widget['settings']['heading'] ?? 'Common Questions' }}
                </h2>
                <p class="tw-mt-6 tw-text-base/7 tw-text-gray-600">Have a different question and can’t find the answer you’re looking for?
                    <a href="{{ url('/customer-support') }}" class="tw-font-semibold tw-text-theme-brand-color hover:tw-text-theme-brand-color/80">Reach out to our support team</a> and we’ll get back to you as soon as we can.
                </p>
            </div>
            <div class="tw-mt-20">
                <dl class="tw-space-y-16 sm:tw-grid sm:tw-grid-cols-2 sm:tw-gap-x-6 sm:tw-gap-y-16 sm:tw-space-y-0 lg:tw-gap-x-10">
                    @foreach(($widget['settings']['faqs'] ?? []) as $question)
                        <div>
                            <dt class="tw-text-base/7 tw-font-bold tw-text-gray-900">
                                {{ $question['question'] }}
                            </dt>
                            <dd class="tw-mt-2 tw-text-base/7 tw-text-gray-600">
                                {!! $question['answer'] !!}
                            </dd>
                        </div>
                    @endforeach


                </dl>
            </div>


        </div>
    </div>
</div>
