@props(['post'])

@php /** @var \App\Models\Post $post */ @endphp

<article class="tw-flex tw-flex-col tw-items-start tw-justify-between">
    <div class="tw-relative tw-w-full">
        <a href="{{ route('blog.show', [$post->slug]) }}" class="tw-group">
            @if($post->cover_photo)
                <div class="tw-aspect-w-16 tw-aspect-h-9 tw-w-full tw-rounded-2xl tw-bg-gray-100 tw-object-cover sm:tw-aspect-w-2 sm:tw-aspect-h-1 lg:tw-aspect-w-3 lg:tw-aspect-h-2">
                    <img src="{{ \App\Models\Media::s3ToCloudfront($post->cover_photo) }}" alt="{{ $post->title }}" class="tw-w-full tw-object-cover tw-rounded-2xl">
                </div>
            @else
                <div class="tw-aspect-w-16 tw-aspect-h-9 tw-w-full tw-rounded-2xl tw-bg-theme-brand-color tw-flex tw-items-center tw-justify-center sm:tw-aspect-w-2 sm:tw-aspect-h-1 lg:tw-aspect-w-3 lg:tw-aspect-h-2">
                    <img src="{{ theme('logo_src') }}" alt="{{ $post->title }}" class="tw-object-cover tw-rounded-2xl tw-absolute tw-top-1/2 tw-left-1/2 tw-transform tw--translate-x-1/2 tw--translate-y-1/2 tw-h-auto tw-w-1/2">
                </div>
            @endif
            <div class="tw-absolute tw-inset-0 tw-rounded-2xl tw-ring-1 tw-ring-inset tw-ring-gray-900/10 tw-transition group-hover:tw-bg-gray-900/20"></div>
        </a>
    </div>
    <div class="tw-mt-4 tw-flex-1 tw-flex tw-flex-col tw-w-full tw-max-w-xl">
        <div class="tw-flex-1 tw-flex tw-flex-col tw-group tw-relative">
            <h3 class="tw-m-0 tw-mt-3 tw-text-xl tw-font-semibold tw-leading-6">
                <a href="{{ route('blog.show', [$post->slug]) }}" class="tw-text-gray-900 hover:tw-text-gray-600 tw-no-underline">
                    <span class="tw-absolute tw-inset-0"></span>
                    {{ $post->title }}
                </a>
            </h3>
            <p class="tw-m-0 tw-flex-1 tw-mt-5 tw-line-clamp-3 tw-text-sm tw-leading-6 tw-text-gray-600">{{ $post->summary ?: strip_tags($post->body) }}</p>
        </div>
        <div class="tw-relative tw-mt-4 tw-group tw-flex tw-items-center tw-gap-x-4">
            <a href="{{ route('blog.show', [$post->slug]) }}" class="tw-text-base tw-text-theme-link-color hover:tw-text-theme-link-color/90 tw-no-underline ">
                Read more
            </a>
        </div>
    </div>
</article>
