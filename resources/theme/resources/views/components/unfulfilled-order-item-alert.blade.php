@props(['order'])

@php
    /** @var \App\Models\Order $order */
@endphp

<div class="tw-rounded-md tw-bg-yellow-50 tw-px-4 tw-py-4 sm:tw-px-6 tw-mb-4">
    <div class="tw-flex">
        <div class="tw-flex-shrink-0">
            <svg class="tw-h-5 tw-w-5 tw-text-yellow-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fill-rule="evenodd" d="M8.485 2.495c.673-1.167 2.357-1.167 3.03 0l6.28 10.875c.673 1.167-.17 2.625-1.516 2.625H3.72c-1.347 0-2.189-1.458-1.515-2.625L8.485 2.495zM10 5a.75.75 0 01.75.75v3.5a.75.75 0 01-1.5 0v-3.5A.75.75 0 0110 5zm0 9a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"></path>
            </svg>
        </div>
        <div class="tw-ml-3">
            <h3 class="tw-m-0 tw-text-sm tw-font-medium tw-text-yellow-700">Action needed</h3>
            <div class="tw-mt-2 tw-text-sm tw-text-yellow-800">
                <p class="tw-m-0 tw-text-sm ">This order includes items that are
                    <span class="tw-font-semibold">OUT OF STOCK.</span>
                    @if(! $order->deadlineHasPassed())
                        Please take a moment to select substitutes and make any other updates to your order by
                        <span class="tw-font-semibold">{{ $order->deadlineDatetime()->format('D, F jS') }}.</span>
                    @endif</p>
            </div>
        </div>
    </div>
</div>
