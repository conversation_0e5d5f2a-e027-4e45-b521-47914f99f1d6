@props([
    'product',
    'order',
    'cta_label' => null,
    'cta_action' => null,
    'list_details' => [],
    'index' => 0
])

@php
    /** @var \App\Models\Product $product */
    /** @var \App\Models\Order|null $order */
    /** @var string $cta_label */
    /** @var string $cta_action */
    /** @var array $list_details */
    /** @var int $index */

    $product_details = [
        'currency' => 'USD',
        'value' => $product->getPrice() / 100,
        'items' => [
            array_merge([
                'item_id' => $product->id,
                'item_name' => $product->title,
                'index' => $index,
                'price' => $product->getPrice() / 100,
                'quantity' => 1
            ], $list_details)
        ]
    ];
@endphp

<section
        itemscope itemtype="https://schema.org/Product"
        itemid="{{ route('store.show', [$product->slug]) }}"
        id="product_{{ $product->slug }}"
        {{ $attributes->merge(['class' => 'tw-flex tw-flex-col tw-bg-white tw-rounded-lg tw-shadow-lg']) }}
        x-data="tracksProductViews(@js($product_details))"
        x-intersect.half.once="trackProductView"
>
    <div class="tw-relative ">
        <div class="tw-relative tw-w-full tw-group tw-rounded-t-lg">
            <a href="{{ route('store.show', [$product->slug]) }}"
               title="{{ $product->title }}"
               class="tw-relative tw-block productListing__photoLink--grid"
            >
                <div class="tw-aspect-h-4 tw-aspect-w-4 tw-w-full tw-group">
                    @if($product->mainPhoto?->path)
                        <img src="{{ \App\Models\Media::s3ToCloudfront($product->mainPhoto?->path) }}" alt="{{ $product->title }}" itemprop="image"
                             class="tw-transition-opacity group-hover:tw-opacity-75 tw-rounded-t-lg tw-h-full tw-w-full tw-object-cover tw-object-center">
                    @else
                        <div class="tw-h-full tw-w-full">
                            <img src="{{ theme('logo_src') }}" alt="{{ $product->title }}"
                                 class="tw-absolute tw-top-1/2 tw-left-1/2 tw-transform tw--translate-x-1/2 tw--translate-y-1/2 tw-w-1/2 ">
                            <div class="tw-h-full tw-w-full tw-object-cover tw-object-center tw-rounded-t-lg tw-bg-gray-900 tw-opacity-10 group-hover:tw-opacity-30"></div>
                        </div>
                    @endif
                </div>
                @if($product->calloutMessage())
                    <div class="tw-absolute tw-top-0 tw-right-0 tw-p-2">
                        <span class="tw-inline-flex tw-items-center tw-bg-white tw-gap-x-1.5 tw-rounded-md tw-px-3 tw-py-1.5 tw-text-sm tw-font-semibold tw-text-theme-action-color tw-shadow-md tw-ring-1 tw-ring-inset tw-ring-gray-200">
                            {{ $product->calloutMessage() }}
                        </span>
                    </div>
                @endif
                <div class="tw-hidden tw-absolute tw-left-0 tw-bottom-0 tw-w-full tw-p-4 lg:tw-block">
                    <button type="button" class="tw-relative tw-z-10 tw-w-full tw-rounded-md tw-bg-white tw-transition-opacity tw-bg-opacity-75 tw-px-4 tw-py-2 tw-text-sm tw-text-gray-900 tw-opacity-0 focus:tw-opacity-100 group-hover:tw-opacity-100">
                        View Details
                        <span class="tw-sr-only">, {{ $product->title }}</span>
                    </button>
                </div>
            </a>
        </div>
    </div>

    <div class="tw-relative tw-flex-1 tw-pt-4 tw-px-4 sm:tw-px-6 sm:tw-pt-5">
        @if($product->isOnSale())
            @php($show_unit_pricing = $product->isPricedByWeight() && setting('show_price_per_pound', true))

            <div class="tw-mb-1 tw--mt-2">
                @if($product->isGiftCard())
                    <p class="tw-m-0 tw-inline-block tw-text-xs tw-text-theme-action-color productListing__saleSavings- productListing__saleSavings--grid">
                        &#36;{{ money($product->getSavings()) }} bonus cash
                    </p>
                @else
                    <p class="tw-m-0 tw-leading-4 tw-inline-block tw-text-xs tw-text-theme-action-color productListing__saleSavings- productListing__saleSavings--grid">
                        @if($product->isBundle())
                            Bundle Savings
                        @else
                            Save
                        @endif
                        @if($show_unit_pricing)
                            &#36;{{ money($product->getUnitSavings()) }} /{{ __("messages.uom." . setting('weight_uom', 'pounds')) }}.
                        @else
                            &#36;{{ money($product->getSavings()) }}
                        @endif
                    </p>
                @endif
            </div>
        @endif
        <a href="{{ route('store.show', [$product->slug]) }}"
           title="{{ $product->title }}"
           class="tw-relative productListing__photoLink--grid"
        >
            <p class="tw-m-0 tw-leading-6 tw-font-medium tw-text-gray-700 hover:tw-text-gray-700" itemprop="name">
                {{ $product->title }}
            </p>
        </a>
        <div itemprop="description">
            @if ( ! empty($product->unit_description))
                <p class="tw-m-0 tw-mt-1 tw-leading-4 tw-text-sm tw-text-gray-500">{!! $product->unit_description !!}</p>
            @endif
            <meta itemprop="sku" content="{{ $product->sku }}"/>
        </div>

        @if ( ! empty($product->vendor_id))
            <div itemprop="brand" itemscope itemtype="http://schema.org/Brand" class="tw-sr-only">
                {!! $product->present()->vendorLink() !!}
            </div>
        @endif

        <a href="{{ route('store.show', [$product->slug]) }}"
           title="{{ $product->title }}"
           class="tw-mt-4 tw-block tw-text-sm tw-text-gray-700 lg:tw-hidden"
        >
            <span class="tw-m-0 tw-text-sm tw-font-medium ">View Details
                <span aria-hidden="true">→</span>
            </span>
        </a>
    </div>

    <div class="tw-flex tw-rounded-lg">
        <div class="tw-w-full tw-flex tw-flex-col">
            <div class="tw-flex-1">
                <div class="tw-flex tw-flex-col productListing__priceContainer--grid"
                     itemprop="offers"
                     itemscope
                     itemtype="https://schema.org/Offer"
                >
                    <!-- Start Price Section -->
                    <div class="tw-mt-4 productListing__price--grid">
                        @include('theme::store.grid.price')
                    </div>
                    <!-- End Price Section -->

                    <!-- Start CTA Section -->
                    <div class="productListing__addToCart--grid">
                        @include('theme::store._partials.add-product', [
                            'cta_classes' => 'tw-px-3 tw-py-2 tw-rounded-t-none tw-rounded-b-lg tw-text-sm tw-font-semibold',
                            'order' => $order,
                            'has_subscription' => $has_subscription,
                            'cta_label' => $cta_label,
                            'cta_action' => $cta_action,
                            'metadata' => $list_details
                        ])
                    </div>
                    <!-- End CTA Section -->
                </div>
            </div>
        </div>
    </div>
</section>
