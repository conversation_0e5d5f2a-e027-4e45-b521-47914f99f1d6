<!DOCTYPE html>
<html lang="en-US">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="robots" content="noindex">

    {!! getGoogleFontLinks() !!}

    <link rel="stylesheet" href="/theme/theme-variables.css?id={{ app('theme')->updated_at?->timestamp ?? Str::random(5) }}">

    @livewireStyles

    @vite([
        'resources/theme/resources/assets/css/tailwind-full.css',
        'resources/theme/resources/assets/less/theme.less',
        'resources/assets/js/theme/theme.js',
    ])

    <link rel="stylesheet" href="/theme/theme.css?id=v1.1_{{ $theme->updated_at->timestamp ?? Str::random(5) }}">

    @yield('head')
</head>

<body class="tw-reset">
<div class="tw-flex tw-w-full tw-items-center tw-justify-center">
    @yield('content')
</div>

@livewireScriptConfig
@stack('scripts')

</body>
</html>
