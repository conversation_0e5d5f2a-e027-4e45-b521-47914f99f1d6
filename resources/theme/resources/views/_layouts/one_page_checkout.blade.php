<!DOCTYPE html>
<html lang="en-US">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0"/>
    <meta name="robots" content="{{ app()->isProduction() ? ($robots ?? 'index,follow') : 'noindex, nofollow' }}">
    <link rel="canonical" href="{{ url()->current() }}"/>
    <meta name="generator" content="GrazeCart">

    @if(theme('favicon'))
        <link rel="icon" type="image/png" href="{{ theme('favicon') }}">
    @endif

    {!! setting('site_meta_tags') !!}

    @yield('pageMetaTags')

    {!! getGoogleFontLinks() !!}

    <link rel="stylesheet" href="/theme/theme-variables.css?id={{ app('theme')->updated_at?->timestamp ?? Str::random(5) }}">

    @livewireStyles

    @vite([
        'resources/theme/resources/assets/css/tailwind-full.css',
        'resources/theme/resources/assets/js/app.js',
    ])

    @yield('head')

    @include('theme::_partials.ga-tracking-script')

    @php($fb_pixel_id = config('services.facebook.pixel_id'))
    @includeWhen(!empty($fb_pixel_id), 'theme::_partials.fb-pixel', ['pixel' => $fb_pixel_id])

    {!! setting('header_scripts') !!}

    @inertiaHead
</head>

<body class="tw-reset tw-min-h-screen tw-font-body">
<div id="app" data-page="{{ json_encode($page) }}"></div>
<div id="modals"></div>

@livewireScriptConfig

<script src="https://js.stripe.com/v3/"></script>

</body>
</html>
