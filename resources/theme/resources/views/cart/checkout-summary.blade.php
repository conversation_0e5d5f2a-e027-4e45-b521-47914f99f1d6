@php
    /** @var \App\Contracts\Cartable $cart */
@endphp

<table class="checkout_summaryTable mb-3">
    <tr>
        <td class="text-left">Items:</td>
        <td class="text-right">&#36;{{ money($cart->cartSubtotal()) }}</td>
    </tr>
    <tr>
        <td class="text-left">Fees:</td>
        <td class="text-right">&#36;{{ money($cart->cartLocationFeeTotal() + $cart->cartDeliveryTotal()) }}</td>
    </tr>
    <tr>
        <td class="text-left">Tax:</td>
        <td class="text-right">&#36;{{ money($cart->cartTaxTotal()) }}</td>
    </tr>
    @foreach($cart->cartCoupons() as $discount)
        @php
            /** @var App\Cart\Coupon $discount */
        @endphp
        <tr class="checkout_summaryTable__coupon">
            <td class="text-left">Coupon:</td>
            <td class="text-right">
                <i class="fa fa-tag"></i> {{ $discount->name }}
                <div class="text-muted">-&#36;{{ money($discount->amount) }}</div>
            </td>
        </tr>
    @endforeach
{{--    @if($cart->order_discount)--}}
{{--        <tr>--}}
{{--            <td class="text-left">Discount:</td>--}}
{{--            <td class="text-right">--}}
{{--                -&#36;{{ money($cart->order_discount) }}--}}
{{--            </td>--}}
{{--        </tr>--}}
{{--    @endif--}}
    @if($cart->cartSubscriptionSavingsTotal() > 0)
        <tr>
            <td class="text-left">Subscription savings:</td>
            <td class="text-right">-&#36;{{ money($cart->cartSubscriptionSavingsTotal()) }}</td>
        </tr>
    @endif
    @if($cart->cartStoreCreditTotal() > 0)
        <tr>
            <td class="text-left">Credit:</td>
            <td class="text-right">-&#36;{{ money($cart->cartStoreCreditTotal()) }}</td>
        </tr>
    @endif

    <tr class="checkout_orderTotal">
        <td class="text-left">&ast;Order Total:</td>
        <td class="text-right">&#36;<span class="order__total">{{ money($cart->cartTotal()) }}</span></td>
    </tr>
</table>
