@php
    /** @var App\Models\Product $product */
    $product_type_id = $product->type();
    $active_tab = Request::get('tab', 'description');
@endphp
@extends('layouts.main', ['pageTitle' => $product->title])

{{--Toolbar--}}
@section('toolbar-breadcrumb')
    <li>
        <a href="{{ route('admin.products.index', session('products-filtered', [])) }}">Products</a>
    </li>
    <li>{{ $product->title }}</li>
@stop
@section('toolbar-buttons')
    @if(request('tab') === 'inventory')
        <button x-data class="btn btn-success flex-item mr-sm" x-on:click="$dispatch('inventory-settings-saved')">Save</button>
    @else
        <button class="btn btn-success flex-item mr-sm" @click="submitForm('productForm')">Save</button>
    @endif
    <div class="dropdown flex-item">
        <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
            <i class="fas fa-gear"></i> Actions <i class="fas fa-caret-down"></i>
        </button>
        <ul x-data class="dropdown-menu pull-right">
            <li><a href="#" @click="showModal('duplicateProductModal')">Duplicate</a></li>
            <li><a href="/admin/products/{{ $product->id }}/printed/product-label" target="_blank">Print Label</a></li>
            <li><a href="/store/product/{{ $product->slug }}" target="_blank">View in Store</a></li>
            <li>
                <a href="#" x-on:click="$dispatch('open-modal-delete-product-confirmation', { product_id: {{ $product->id }} })">Delete</a>
            </li>
        </ul>
    </div>
@stop

@section('content')
    @include('products.partials.delete-product-modal')
    @include('products.partials.duplicate-product-modal')

    <div class="row">
        <div class="content">
            <div class="relative pb-5 sm:pb-0">
                <div class="-ml-2 -mt-2 flex flex-wrap items-baseline justify-between">
                    <h1 class="ml-2 mt-2 text-2xl font-semibold leading-6 text-gray-900">{{ $product->title }}</h1>
                    <div class="flex items-center space-x-1">
                        <span class="inline-flex items-center rounded-md bg-keppel-100 px-2.5 py-0.5 text-sm font-semibold text-keppel-800">{{ \App\Support\Enums\ProductType::get($product_type_id) }}</span>
                        @if($product->isBundle())
                            <span class="inline-flex items-center rounded-md bg-blue-100 px-2.5 py-0.5 text-sm font-semibold text-blue-800">Bundle</span>
                        @endif
                    </div>
                </div>

                <div class="mt-5">
                    <!-- Dropdown menu on small screens -->
                    <div class="sm:hidden">
                        <label for="current-tab" class="hidden">Select a tab</label>
                        <select id="current-tab" onchange="setActiveTab(this.value)" name="current-tab" class="block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-keppel-500 focus:outline-none focus:ring-keppel-500 sm:text-sm">
                            <option value="description" @if($active_tab === 'description') selected @endif>Description</option>
                            @if($product_type_id === \App\Support\Enums\ProductType::PREORDER->value)
                                <option value="checkout" @if($active_tab === 'checkout') selected @endif>Checkout</option>
                            @endif
                            <option value="price" @if($active_tab === 'price') selected @endif>Price & Weight</option>
                            <option value="inventory" @if($active_tab === 'inventory') selected @endif>Inventory</option>
                            <option value="settings" @if($active_tab === 'settings') selected @endif>Settings</option>
                            {{--                            <option value="protocols" @if($active_tab === 'protocols') selected @endif>Protocols</option>--}}
                            <option value="photos" @if($active_tab === 'photos') selected @endif>Photos</option>
                            <option value="seo" @if($active_tab === 'seo') selected @endif>SEO</option>
                            <option value="template" @if($active_tab === 'template') selected @endif>Template</option>
                        </select>
                    </div>
                    <!-- Tabs at small breakpoint and up -->
                    <div class="hidden sm:block">
                        <nav class="-mb-px flex space-x-8">

                            <a href="{{ route('admin.products.edit', $product->id)}}?tab=description" class="whitespace-nowrap pb-4 px-1 border-b-2 font-medium text-sm {{ $active_tab === 'description' ? 'border-keppel-500 text-keppel-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"' }}" @if($active_tab === 'description') aria-current="page" @endif>Description</a>

                            @if($product_type_id === \App\Support\Enums\ProductType::PREORDER->value)
                                <a href="{{ route('admin.products.edit', $product->id)}}?tab=checkout" class="whitespace-nowrap pb-4 px-1 border-b-2 font-medium text-sm {{ $active_tab === 'checkout' ? 'border-keppel-500 text-keppel-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"' }}" @if($active_tab === 'checkout') aria-current="page" @endif>Checkout</a>
                            @endif

                            <a href="{{ route('admin.products.edit', $product->id)}}?tab=price" class="whitespace-nowrap pb-4 px-1 border-b-2 font-medium text-sm {{ $active_tab === 'price' ? 'border-keppel-500 text-keppel-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"' }}" @if($active_tab === 'price') aria-current="page" @endif>Price
                                & Weight</a>

                            <a href="{{ route('admin.products.edit', $product->id)}}?tab=inventory" class="whitespace-nowrap pb-4 px-1 border-b-2 font-medium text-sm {{ $active_tab === 'inventory' ? 'border-keppel-500 text-keppel-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"' }}" @if($active_tab === 'inventory') aria-current="page" @endif>Inventory</a>

                            <a href="{{ route('admin.products.edit', $product->id)}}?tab=settings" class="whitespace-nowrap pb-4 px-1 border-b-2 font-medium text-sm {{ $active_tab === 'settings' ? 'border-keppel-500 text-keppel-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"' }}" @if($active_tab === 'settings') aria-current="page" @endif>Settings</a>

                            {{--                            <a href="{{ route('admin.products.edit', $product->id)}}?tab=protocols" class="whitespace-nowrap pb-4 px-1 border-b-2 font-medium text-sm {{ $active_tab === 'protocols' ? 'border-keppel-500 text-keppel-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"' }}" @if($active_tab === 'protocols') aria-current="page" @endif>Protocols</a>--}}

                            <a href="{{ route('admin.products.edit', $product->id)}}?tab=photos" class="whitespace-nowrap pb-4 px-1 border-b-2 font-medium text-sm {{ $active_tab === 'photos' ? 'border-keppel-500 text-keppel-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"' }}" @if($active_tab === 'photos') aria-current="page" @endif>Photos</a>

                            <a href="{{ route('admin.products.edit', $product->id)}}?tab=seo" class="whitespace-nowrap pb-4 px-1 border-b-2 font-medium text-sm {{ $active_tab === 'seo' ? 'border-keppel-500 text-keppel-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"' }}" @if($active_tab === 'seo') aria-current="page" @endif>SEO</a>

                            <a href="{{ route('admin.products.edit', $product->id)}}?tab=template" class="whitespace-nowrap pb-4 px-1 border-b-2 font-medium text-sm {{ $active_tab === 'template' ? 'border-keppel-500 text-keppel-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"' }}" @if($active_tab === 'template') aria-current="page" @endif>Template</a>

                        </nav>
                    </div>
                </div>
            </div>
            @include("products.partials.{$active_tab}")
        </div>

        <div class="sidebar sidebar-1 sidebar-right">
            <collections id="{{ $product->id }}" model="products"></collections>

            <tags id="{{ $product->id }}" model="products"></tags>

            @if($product_type_id !== \App\Support\Enums\ProductType::PREORDER->value)
                <product-variants id="{{ $product->id }}" model="products"></product-variants>
            @endif
        </div>
    </div>

    <div>
        <media-browser></media-browser>
    </div>
@endsection

@section('modals')

    <livewire:admin.modals.product-inventory-history/>
    <livewire:admin.modals.delete-product-confirmation/>
    <livewire:admin.modals.add-product-to-bundle/>
    <livewire:admin.modals.edit-bundle-product/>
    <livewire:admin.modals.delete-bundle-product-confirmation/>
    <livewire:admin.modals.add-price-tier/>
    <livewire:admin.modals.edit-price-tier/>
@endsection

@section('scripts')
    @include('partials.check-for-unsaved-changes')
    <script>
        function setActiveTab(tab) {
            window.location = "{{ route('admin.products.edit', $product->id) }}?tab=" + tab;
        }
    </script>
@endsection
