@php use App\Support\Enums\ProductType; @endphp
@extends('layouts.main', ['pageTitle' => $product->title])

@php
    $active_tab = Request::get('tab', 'description');
@endphp

@section('styles')
    <link href='//fonts.googleapis.com/css?family=Inconsolata:400,500,600,700' rel='stylesheet' type='text/css'>
@endsection
{{--Toolbar--}}
@section('toolbar-breadcrumb')
    <li>
        <a href="{{ route('admin.products.index', session('products-filtered', [])) }}">Products</a>
    </li>
    <li><a href="{{ route('admin.gift-cards.index') }}">Gift Cards</a></li>
    <li>{{ $product->title }}</li>
@stop
@section('toolbar-buttons')
    <button class="btn btn-success flex-item mr-sm" @click="submitForm('productForm')">Save</button>

    <div class="dropdown flex-item">
        <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
            <i class="fas fa-gear"></i> Actions <i class="fas fa-caret-down"></i>
        </button>
        <ul class="dropdown-menu pull-right">
            <li><a href="#" @click="showModal('duplicateProductModal')">Duplicate</a></li>
            <li><a href="/admin/products/{{ $product->id }}/edit?print=true">Print Label</a></li>
            <li class="divider"></li>
            <li><a href="#" @click="showModal('deleteProductModal')">Delete</a></li>
        </ul>
    </div>
@stop

@section('content')
    @include('products.partials.delete-product-modal')
    @include('products.partials.duplicate-product-modal')

    <div class="row">
        <div class="content">
            <div class="-ml-2 -mt-2 flex flex-wrap items-baseline justify-between">
                <h1 class="ml-2 mt-2 text-2xl font-semibold leading-6 text-gray-900">{{ $product->title }}</h1>
                <span class="inline-flex items-center rounded-md bg-keppel-100 px-2.5 py-0.5 text-sm font-semibold text-keppel-800">{{ \App\Support\Enums\ProductType::get(ProductType::GIFT_CARD->value) }}</span>
            </div>

            <div class="mt-5">
                <!-- Dropdown menu on small screens -->
                <div class="sm:hidden">
                    <label for="current-tab" class="hidden">Select a tab</label>
                    <select id="current-tab" onchange="setActiveTab(this.value)" name="current-tab" class="block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-keppel-500 focus:outline-none focus:ring-keppel-500 sm:text-sm">
                        <option value="description" @if($active_tab === 'description') selected @endif>Details</option>
                        <option value="issued" @if($active_tab === 'issued') selected @endif>Issued Codes</option>
                    </select>
                </div>
                <!-- Tabs at small breakpoint and up -->
                <div class="hidden sm:block">
                    <nav class="-mb-px flex space-x-8">
                        <a href="{{ route('admin.gift-cards.edit', $product->id)}}?tab=description" class="whitespace-nowrap pb-4 px-1 border-b-2 font-medium text-sm {{ $active_tab === 'description' ? 'border-keppel-500 text-keppel-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"' }}" @if($active_tab === 'description') aria-current="page" @endif>Details</a>
                        <a href="{{ route('admin.gift-cards.edit', $product->id)}}?tab=issued" class="whitespace-nowrap pb-4 px-1 border-b-2 font-medium text-sm {{ $active_tab === 'issued' ? 'border-keppel-500 text-keppel-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"' }}" @if($active_tab === 'issued') aria-current="page" @endif>Issued
                            Codes</a>
                    </nav>
                </div>
            </div>
            @include("products.gift-cards.partials.".request()->get('tab', 'description'))
        </div>

        <div class="sidebar sidebar-1 sidebar-right">
            <div class="panel">
                <div class="panel-body">
                    <cover-photo
                            url="/admin/products/{{ $product->id }}/photo"
                            src="{{ $product->cover_photo }}"
                            field="cover_photo"
                            message="Add Product Photo"
                            :tags="['Product','Store']"
                    ></cover-photo>
                </div>
            </div>

            <collections id="{{ $product->id }}" model="products"></collections>

            <tags id="{{ $product->id }}" model="products"></tags>
        </div>
    </div>
@stop

@section('scripts')
    @include('partials.check-for-unsaved-changes')
    <script>
        function setActiveTab(tab) {
            window.location = "{{ route('admin.gift-cards.edit', $product->id) }}?tab=" + tab;
        }
    </script>
@endsection
