<div class="filterPanel" id="filterPanel">
    <div class="settingsPanel pa-0">
        <div class="settingsPanel__header flex align-items-m">
            <div class="flex-item-fill">Filter Products</div>
            <button
                    type="button"
                    class="btn btn-alt flex-item"
                    @click="hidePanel('filterPanel')"
            ><i class="fas fa-times fa-lg"></i></button>
        </div>
        <div class="settingsPanel__body">
            <div class="form-group">
                <label for="inventory_type">Packing Group</label>
                <x-form.packing-group-select
                        class="form-control select2"
                        name="inventory_type[]"
                        data-placeholder="Select packing groups"
                        tabindex="1"
                        multiple
                        :selected="request('inventory_type')"
                />
            </div>

            {{--Unit Of Issue--}}
            <div class="form-group">
                <label for="unit_of_issue">Unit Of Issue</label>
                <x-form.unit-of-issue-select
                        class="form-control"
                        name="unit_of_issue"
                        placeholder="All"
                        :selected="request('unit_of_issue')"
                />
            </div>

            {{--Tags--}}
            <div class="form-group">
                <label for="tags">Tags</label>
                <x-form.tag-select
                        class="form-control select2"
                        name="tags[]"
                        data-placeholder="Select some tags"
                        multiple
                        style="width: 100%"
                        :selected="request('tags')"
                />
            </div>

            {{--Vendor--}}
            <div class="form-group">
                <label for="vendor">Vendor</label>
                <x-form.vendor-select
                        class="form-control"
                        name="vendor_id"
                        tabindex="1"
                        placeholder="All"
                        :selected="request('vendor_id')"
                />
            </div>

            {{--Sale Status--}}
            <div class="form-group">
                <label for="sale-status">Sale Status</label>
                <x-form.sale-status-select
                        class="form-control"
                        name="sale"
                        placeholder="All"
                        :selected="request('sale')"
                />
            </div>

            {{--Track Inventory--}}
            <div class="form-group">
                <label for="track_inventory">Track Inventory</label>
                {{ html()->select('track_inventory', \App\Models\Product::TRACK_INVENTORY_OPTIONS, request('track_inventory'))->class('form-control')->placeholder('All') }}
            </div>

            {{--Collection_id--}}
            <div class="form-group">
                <label for="collection_id">Collection</label>
                <x-form.collection-select
                        class="form-control"
                        name="collection_id"
                        :selected="(int) request('collection_id')"
                        :include_all="true"
                />
            </div>

            {{--Include Deleted Products--}}
            <div class="checkbox form-group">
                <label>
                    <input type="checkbox" name="show_deleted" value="true"
                           @if(Request::get('show_deleted')) checked @endif> Show Deleted Products
                </label>
            </div>

            {{--Visibility--}}
            <div class="form-group">
                <label for="product_visibility">Visibility</label>
                <x-form.visibility-select
                        class="form-control"
                        name="product_visibility"
                        placeholder="All"
                        :selected="request('product_visibility')"
                />
            </div>

            {{--Taxable--}}
            <div class="form-group">
                <label for="taxable">Taxable</label>
                <select class="form-control" name="taxable">
                    <option value="">All</option>
                    {!! selectOptions(['1' => 'Yes', '0' => 'No'], request('taxable')) !!}
                </select>
            </div>
        </div>
        <div class="settingsPanel__footer flex align-items-m">
            <button type="submit" class="btn btn-alt mr-md" name="export" value="true"><i
                        class="fas fa-cloud-download"></i></button>
            <button type="submit" class="btn btn-action btn-block btn-lg" @click="submitForm('filterProductsForm')">
                Filter
            </button>
        </div>
    </div>
</div>
