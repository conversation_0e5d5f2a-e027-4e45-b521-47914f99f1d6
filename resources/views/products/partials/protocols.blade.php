<form action="{{ route('admin.products.update', $product->id) }}" method="POST" id="productForm">
    @csrf
    @method('put')
    <div class="panel panel-tabs">
        <div class="panel-body">
            <input type="hidden" name="protocols" value="0" />

            @php
                $selected_protocol_ids = $product->protocols()->pluck('protocols.id')->toArray()
            @endphp
            @foreach (\App\Models\Protocol::pluck('title', 'id') as $id => $title)
                <div class="checkbox">
                    <label>
                        <input type="checkbox" name="protocols[]" value="{{ $id }}" @checked(in_array($id, $selected_protocol_ids))> {{ $title }}
                    </label>
                </div>
            @endforeach
        </div>
        <div class="panel-footer text-right">
            <button type="submit" class="btn btn-action" @click="submitForm('productForm', $event)">Save</button>
        </div>
    </div>
</form>
