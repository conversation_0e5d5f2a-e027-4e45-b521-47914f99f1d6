@component('partials.modal', ['id' => 'activateScheduleModal'])
    @slot('header')
        Activate Schedule
    @endslot

    @slot('body')
        <form action="/admin/schedules/{{ $schedule->id }}/activate" method="POST" id="activateScheduleForm">
            @csrf
            @method('PUT')
            <div class="form-group">
                <label for="first_delivery_date" class="flex align-items-m">
                    <div>First Delivery Date</div>
                    <div class="dropdown push-right">
                        <button type="button" class="btn btn-alt btn-alt btn-link pa-0 mr-sm fs-xs text-gray-medium" data-toggle="dropdown"></button>
                    </div>
                </label>
                <x-form.pikaday-input
                        name="first_delivery_date"
                        class="form-control"
                        value="{{ request('first_delivery_date') ?? '' }}"
                        id="first_delivery_date"
                        placeholder="First delivery date"
                        tabindex="1"
                />
            </div>


            <div class="form-group">
                <label for="order_cutoff">
                    Order Cutoff
                    <p class="label-context">
                        Set the number of days ordering closes prior to the delivery date. Set to 0 to set as the delivery date.
                    </p>
                </label>
                <select id="order_cutoff" name="order_cutoff" class="form-control flex-item">
                    {!! selectOptions(array_combine(range(0, 30), range(0, 30))) !!}
                </select>
            </div>

            <div class="form-group">
                <label for="ordering_in_advance">
                    Advance ordering
                </label>
                <div class="form-group flex align-items-m">
                    <div class="flex-item-fill">
                        Allow customers to select up to the next
                    </div>
                    <select name="ordering_in_advance" class="form-control flex-item">
                        {!! selectOptions([
                            '' => 'No limit',
                            '1' => 1,
                            '2' => 2,
                            '3' => 3,
                            '4' => 4,
                            '5' => 5,
                            '6' => 6,
                            '7' => 7,
                            '8' => 8,
                            '9' => 9,
                            '10' => 10,
                        ]) !!}
                    </select>
                    <div class="flex-item-fill">
                        available delivery dates at checkout.
                    </div>
                </div>
            </div>

            @endslot
            @slot('footer')
                <button type="button" class="btn btn-alt" @click="hideModal('activateScheduleModal')">Cancel</button>
                <button type="submit" class="btn btn-action" @click="submitForm('activateScheduleForm')">Start Schedule</button>
        </form>
    @endslot
@endcomponent
