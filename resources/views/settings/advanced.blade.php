@extends('settings.layout', ['settingTitle' => 'Tags &amp; Scripts'])

@section('setting_toolbar')
<button class="btn btn-success" @click="submitForm('settingsForm')">Save</button>
@stop

@section('setting_content')
<form action="{{ route('admin.settings.update') }}" method="POST" id="settingsForm">
@csrf
@method('PUT')
    <div class="panel">
        <div class="panel-body pa-0">
            <table class="table   table-striped table-settings">
                <tbody>
                <tr>
                    <td>
                        <h2>Meta Tags</h2>
                        <p>Add meta tags you want to apply to your entire site. The content entered here will appear as-is in the header of every page.</p>
                    </td>
                    <td>
                        <textarea name="settings[site_meta_tags]" class="form-control" rows="14">{{ old('site_meta_tags', setting('site_meta_tags')) }}</textarea>
                    </td>
                </tr>

                <tr>
                    <td>
                        <h2><PERSON><PERSON> Scripts</h2>
                        <p>The content entered here will appear as-is in the header of every page.
                        <br/><br/><em>Note: Be sure to include the opening and closing &lt;script&gt;&lt;/script&gt; tags.</em></p>
                    </td>
                    <td>
                        <textarea name="settings[header_scripts]" class="form-control" rows="14">{{ old('header_scripts', setting('header_scripts')) }}</textarea>
                    </td>
                </tr>

                <tr>
                    <td>
                        <h2>Footer Scripts</h2>
                        <p>The content entered here will appear as-is at the bottom of every page of every page.
                        <br/><br/><em>Note: Be sure to include the opening and closing &lt;script&gt;&lt;/script&gt; tags.</em></p>
                    </td>
                    <td>
                        <textarea name="settings[footer_scripts]" class="form-control" rows="14">{{ old('header_scripts', setting('footer_scripts')) }}</textarea>
                    </td>
                </tr>

                <tr>
                    <td>
                        <h2>Checkout Confirmation Scripts</h2>
                        <p>Use this for adding conversion tracking scripts like Google Ads.
                        <br/><br/><em>Note: DO NOT include the opening and closing &lt;script&gt;&lt;/script&gt; tags.</em></p>
                    </td>
                    <td>
                        <textarea name="settings[checkout_scripts]" class="form-control" rows="14">{{ old('header_scripts', setting('checkout_scripts')) }}</textarea>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="panel-footer text-right">
            <button class="btn btn-action" @click="submitForm('settingsForm', $event)">Save</button>
        </div>
    </div>        
</form>
@stop
