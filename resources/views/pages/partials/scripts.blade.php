<form action="{{ route('admin.pages.update', $page->id) }}" method="POST" id="updateResourceForm">
    @csrf
    @method('put')
    <div class="panel panel-tabs">
        <div class="panel-body pa-0">
            <table class="table table-striped table-settings">
                <tbody>
                <tr>
                    <td>
                        <h2>Meta Tags / Header Scripts</h2>
                        <p>Anything entered here will be displayed as-is in the &lt;HEAD&gt; tag of this page.</p>
                    </td>
                    <td>
                        <textarea class="form-control" name="settings[meta_tags]" rows="10">{{ $page->settings->meta_tags }}</textarea>
                    </td>
                </tr>
                <tr>
                    <td>
                        <h2>Body Scripts</h2>
                        <p>Anything entered here will be displayed as-is at the bottom of the page. This is a great place to put page-specific javascript.</p>
                    </td>
                    <td>
                        <textarea class="form-control" name="settings[body_scripts]" rows="10">{{ $page->settings->body_scripts }}</textarea>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="panel-footer text-right">
            <button type="submit" class="btn btn-action" @click="submitForm('updateResourceForm')">Save</button>
        </div>
    </div>
</form>
