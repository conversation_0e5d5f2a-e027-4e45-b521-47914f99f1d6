<div>
    <div>
        <!-- Dropdown menu on small screens -->
        <div class="sm:hidden">
            <label for="current-tab" class="hidden">Select a tab</label>
            <select id="current-tab" name="current-tab" onchange="setActiveTab(this.value)" class="block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-keppel-500 focus:outline-none focus:ring-keppel-500 sm:text-sm">
                <option value="legacy" @if($current_tab === 'legacy') selected @endif>Legacy</option>
                <option value="Promo" @if($current_tab === 'Promo') selected @endif>Promo</option>
            </select>
        </div>
        <!-- Tabs at small breakpoint and up -->
        <div class="hidden sm:block">
            <div class="border-b border-gray-200">
                <nav class="-mb-px flex space-x-8">
                    <a href="{{ route('admin.pages.index', ['tab' => 'legacy']) }}" class="@if($current_tab === 'legacy') border-keppel-500 text-keppel-600 @else border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 @endif whitespace-nowrap pb-4 px-1 border-b-2 font-medium text-sm" aria-current="page">Legacy</a>
                    <a href="{{ route('admin.pages.index', ['tab' => 'promo']) }}" class="@if($current_tab === 'promo') border-keppel-500 text-keppel-600 @else border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 @endif whitespace-nowrap pb-4 px-1 border-b-2 font-medium text-sm">Promo</a>
                </nav>
            </div>
        </div>
    </div>
    <div class="bg-white py-10">
        <div class="mx-auto max-w-7xl">
            <div class="px-4 sm:px-6 lg:px-8">
                <div class="sm:flex sm:items-center">
                    <div class="sm:flex-auto">
                        <h1 class="text-base font-semibold text-gray-900">Pages</h1>
                        <p class="mt-2 text-sm text-gray-700">A list of all the pages on the website.</p>
                    </div>
                    <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
                        <button type="button" @click="showModal('createPageModal')" class="block rounded-md bg-keppel-600 px-3 py-2 text-center text-sm font-semibold text-white shadow-sm hover:bg-keppel-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-keppel-600">
                            Add page
                        </button>
                    </div>
                </div>

                <div class="mt-6">
                    <form action="{{ route('admin.pages.index') }}" method="GET" class="hidden-print">
                        <input type="hidden"
                               name="tab"
                               id="tab"
                               class="sr-only"
                               value="{{$current_tab}}"
                        >
                        <div>
                            <label for="pages" class="sr-only">Pages</label>
                            <div class="relative mt-2 rounded-md shadow-sm">
                                <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                                    <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"/>
                                    </svg>
                                </div>
                                <input type="text"
                                       name="pages"
                                       id="pages"
                                       class="block w-full rounded-md border-0 py-1.5 pl-10 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-keppel-600 sm:text-sm/6"
                                       placeholder="Search by name..."
                                       value="{{ request('pages') }}"
                                       autofocus
                                >
                            </div>
                        </div>
                    </form>
                </div>
                @if($pages->isEmpty())
                    <div class="mt-8 text-center">
                        <svg class="mx-auto h-12 w-12 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M9.879 7.519c1.171-1.025 3.071-1.025 4.242 0 1.172 1.025 1.172 2.687 0 3.712-.203.179-.43.326-.67.442-.745.361-1.45.999-1.45 1.827v.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 5.25h.008v.008H12v-.008Z"/>
                        </svg>

                        <h3 class="mt-2 text-sm font-semibold text-gray-900">No pages found.</h3>
                    </div>
                @else
                    <div class="mt-8 flow-root">
                        <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                            <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                                <table class="min-w-full divide-y divide-gray-300">
                                    <thead>
                                    <tr>
                                        <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-0">
                                            {!! sortTable('Name', 'title') !!}
                                        </th>
                                        <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                                            Slug
                                        </th>
                                        <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                                            {!! sortTable('Last Updated', 'updated_at') !!}
                                        </th>
                                        <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-0">
                                            <span class="sr-only">Manage</span>
                                        </th>
                                    </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-200">
                                    @foreach($pages as $page)
                                        <tr>
                                            <td class="py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-0">
                                                <a href="{{ route('admin.pages.edit', [$page->id]) }}">{{ $page->title }}</a>
                                                <p class="m-0 tw-mt-1 text-xs text-gray-500">{{ $page->page_title }}</p>
                                            </td>
                                            <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500 flex items-center space-x-1">
                                                <div>
                                                    {{ $page->slug }}
                                                </div>
                                                <a target="_blank" href="{{ $rootURL }}/{{ $page->slug }}">
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-4">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 6H5.25A2.25 2.25 0 0 0 3 8.25v10.5A2.25 2.25 0 0 0 5.25 21h10.5A2.25 2.25 0 0 0 18 18.75V10.5m-10.5 6L21 3m0 0h-5.25M21 3v5.25"/>
                                                    </svg>
                                                </a>
                                            </td>
                                            <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                                <span class="tooltip" title="{{ $page->updated_at->format('m/d/Y') }}">{{ $page->updated_at->diffForHumans() }}</span>
                                            </td>
                                            <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-0">
                                                <button type="button" x-on:click="$dispatch('open-modal-delete-page-confirmation', { page_id: {{ $page->id }} })">
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"/>
                                                    </svg>
                                                </button>
                                            </td>
                                        </tr>
                                    @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>

</div>

<div class="mt-8 text-center">
    <p>Showing {{ $pages->count() }} of {{ $pages->total() }} result(s)</p>
    {!! $pages->appends(['q' => Request::get('q')])->render() !!}
</div>
