@extends('layouts.main', ['pageTitle' => 'Income Analysis | Reports'])

@section('toolbar-breadcrumb')
    <li>Reports</li>
    <li>Income Analysis</li>
@stop

@section('toolbar-buttons')
    <button onclick="window.print()" class="btn btn-default flex-item"><i class="fas fa-print"></i> Print</button>
@stop

@section('content')
    <div class="visible-print text-center">
        <h5><strong>Income Analysis</strong></h5>
        @if(session()->has('product-income-analysis-filters'))
            @foreach(session('product-income-analysis-filters') as $key => $filterGroup)
                <ul class="list-inline">
                    @foreach($filterGroup as $filter)
                        <li>{{ $filter }}</li>
                    @endforeach
                </ul>
            @endforeach
        @endif
    </div>
    @include('reports.income-analysis.partials.index-table')
@stop()

@section('scripts')
    <script src="https://unpkg.com/@popperjs/core@2"></script>
    <script type="text/javascript">

        const trigger = document.querySelector('#net-total-tooltip-trigger');
        const tooltip = document.querySelector('#net-total-tooltip');

        if (trigger && tooltip) {
            const poppedTooltip = Popper.createPopper(trigger, tooltip, {
                placement: 'right'
            });
        }

        function show() {
            if (!tooltip) return;
            tooltip.classList.remove('hidden');

            // We need to tell Popper to update the tooltip position
            // after we show the tooltip, otherwise it will be incorrect
            poppedTooltip.update();
        }

        function hide() {
            if (!tooltip) return;
            tooltip.classList.add('hidden');
        }

        hide();

        const showEvents = ['mouseenter', 'focus'];
        const hideEvents = ['mouseleave', 'blur'];

        if (trigger) {
            showEvents.forEach((event) => {
                trigger.addEventListener(event, show);
            });

            hideEvents.forEach((event) => {
                trigger.addEventListener(event, hide);
            });
        }
    </script>
@stop
