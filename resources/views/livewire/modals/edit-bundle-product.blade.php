<x-modal>
    @if(!is_null($bundle_id) && !is_null($product_id))
        <form wire:submit="submit" class="relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl sm:mx-auto sm:my-8 sm:w-full sm:max-w-lg">
            <div class="bg-white px-4 pb-4 pt-5 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-keppel-100 sm:mx-0 sm:h-10 sm:w-10">
                        <svg class="h-6 w-6 text-keppel-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M6 6.878V6a2.25 2.25 0 0 1 2.25-2.25h7.5A2.25 2.25 0 0 1 18 6v.878m-12 0c.235-.083.487-.128.75-.128h10.5c.263 0 .515.045.75.128m-12 0A2.25 2.25 0 0 0 4.5 9v.878m13.5-3A2.25 2.25 0 0 1 19.5 9v.878m0 0a2.246 2.246 0 0 0-.75-.128H5.25c-.263 0-.515.045-.75.128m15 0A2.25 2.25 0 0 1 21 12v6a2.25 2.25 0 0 1-2.25 2.25H5.25A2.25 2.25 0 0 1 3 18v-6c0-.98.626-1.813 1.5-2.122" />
                        </svg>
                    </div>
                    <div class="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left">
                        <h3 class="text-base font-semibold leading-6 text-gray-900" id="modal-title">Update bundle product quantity</h3>
                        <div class="mt-2">
                            <p class="m-0 text-sm text-gray-500">Set how many <strong>{{ $product->title }}</strong> are contained within
                                the <strong>{{ $bundle->title }}</strong> bundle.
                            </p>
                        </div>
                        <div class="mt-4">
                            <label for="quantity" class="hidden">Bundle quantity</label>
                            <div class="relative mt-2 rounded-md shadow-sm">
                                <input type="text" wire:model="quantity" name="quantity" id="quantity" class="block w-full rounded-md border-0 py-1.5  ring-1 ring-inset pr-10  focus:ring-2 focus:ring-inset sm:text-sm sm:leading-6 @error('quantity') text-red-900 ring-red-300 placeholder:text-red-300 focus:ring-red-500 @else text-gray-900 ring-gray-300 placeholder:text-gray-400 focus:ring-keppel-600 @enderror" requikeppel @error('quantity') aria-invalid="true" @enderror aria-describedby="quantity-error">
                                @error('quantity')
                                <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                                    <svg class="h-5 w-5 text-red-500" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                @enderror
                            </div>
                            @error('quantity') <p class="mt-2 text-sm text-red-600" id="name-error">{{ $message }}</p> @enderror
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
                <button type="submit" wire:loading.attr="disabled" class="inline-flex w-full justify-center rounded-md bg-keppel-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-keppel-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-keppel-600 sm:ml-3 sm:w-auto">
                    Update
                </button>
                <button type="button" wire:loading.attr="disabled" wire:click="close" class="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:mt-0 sm:w-auto">
                    Cancel
                </button>
            </div>
        </form>
    @endif
</x-modal>
