<x-modal>
    <div class="relative transform rounded-lg bg-white text-left shadow-xl sm:mx-auto sm:my-8 sm:w-full sm:max-w-2xl">
        <div class="rounded-t-lg bg-white px-4 pb-4 pt-5 sm:p-6 sm:pb-4">
            <div class="sm:flex sm:items-start">
                <div class="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-keppel-100 sm:mx-0 sm:h-10 sm:w-10">
                    <svg class="h-6 w-6 text-keppel-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M21 7.5l-9-5.25L3 7.5m18 0l-9 5.25m9-5.25v9l-9 5.25M3 7.5l9 5.25M3 7.5v9l9 5.25m0-9v9"/>
                    </svg>
                </div>
                <div class="mt-3 w-full text-center sm:ml-4 sm:mt-0 sm:text-left">
                    <h3 class="text-base font-semibold leading-6 text-gray-900" id="modal-title">Product inventory history</h3>
                    <div class="mt-2">
                        <p class="text-sm text-gray-500">Recent adjustments made to on-site inventory.</p>
                    </div>

                    <div class="mt-10 ">
                        <div class="flow-root">
                            <ul role="list" class="-mb-8">
                                @foreach($adjustments as $adjustment)
                                    <li>
                                        <div class="relative pb-8">
                                            @if ( ! $loop->last)
                                                <span class="absolute left-4 top-4 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true"></span>
                                            @endif
                                            <div class="relative flex space-x-3">
                                                <div>
                                                    <span class="flex h-8 w-8 items-center justify-center rounded-full bg-gray-400 ring-8 ring-white">

                                                        @if($adjustment->new_amount >= $adjustment->old_amount)
                                                            <svg class="text-white size-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                                <path stroke-linecap="round" stroke-linejoin="round" d="m15 11.25-3-3m0 0-3 3m3-3v7.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"/>
                                                            </svg>
                                                        @else
                                                            <svg class="text-white size-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                                <path stroke-linecap="round" stroke-linejoin="round" d="m9 12.75 3 3m0 0 3-3m-3 3v-7.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"/>
                                                            </svg>
                                                        @endif
                                                    </span>
                                                </div>
                                                <div class="flex min-w-0 flex-1 justify-between space-x-4 pt-1.5">
                                                    <div>
                                                        <p class="m-0 text-sm text-gray-500">@if($adjustment->new_amount >= $adjustment->old_amount)
                                                                Increased
                                                            @else
                                                                Decreased
                                                            @endif from
                                                            <span class="text-gray-900">{{ $adjustment->old_amount }}</span>
                                                            to
                                                            <span class="text-gray-900">{{ $adjustment->new_amount }}</span>
                                                            by
                                                            <a href="{{ route('admin.users.edit', [$adjustment->user_id]) }}" class="font-medium text-gray-900">{{ $adjustment->user?->full_name ?? 'Deleted Customer' }}</a>
                                                        </p>

                                                    </div>
                                                    <div class="whitespace-nowrap text-right text-sm text-gray-500">
                                                        <time datetime="{{ $adjustment->created_at->format('Y-m-d H:i:s') }}">{{ $adjustment->created_at->format('M jS | h:m A') }}</time>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </li>
                                @endforeach


                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="rounded-b-lg bg-gray-50 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
            <button type="button" wire:click="close" class="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:mt-0 sm:w-auto">
                Close
            </button>
        </div>
    </div>
</x-modal>
