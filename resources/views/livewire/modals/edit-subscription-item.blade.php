<x-modal>
    @if (!is_null($item_id))
        <form
                x-data="{ type: @entangle('type') }"
                wire:submit="submit"
                class="relative transform rounded-lg bg-white text-left shadow-xl sm:mx-auto sm:my-8 sm:w-full sm:max-w-lg"
        >
            <div class="rounded-t-lg bg-white px-4 pb-4 pt-5 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-keppel-100 sm:mx-0 sm:h-10 sm:w-10">
                        <svg class="h-6 w-6 text-keppel-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M21 7.5l-9-5.25L3 7.5m18 0l-9 5.25m9-5.25v9l-9 5.25M3 7.5l9 5.25M3 7.5v9l9 5.25m0-9v9" />
                        </svg>
                    </div>
                    <div class="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left">
                        <h3 class="text-base font-semibold leading-6 text-gray-900" id="modal-title">Update subscription item</h3>
                        <div class="mt-2">
                            <p class="text-sm text-gray-500">Update an existing subscription item. Changes impact the next order generated for this
                                subscription.</p>
                        </div>
                        <div class="mt-4 text-left">
                            <div>
                                <div>
                                    <label for="product_id" class="hidden">Product</label>
                                    <div class="relative mt-2 rounded-md shadow-sm">
                                        <livewire:admin.subscription-product-select
                                                :selected_product_id="$product_id"
                                        />
                                        @error('product_id')
                                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                                            <svg class="h-5 w-5 text-red-500" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
                                            </svg>
                                        </div>
                                        @enderror
                                    </div>
                                    @error('product_id') <p class="mt-2 text-sm text-red-600" id="product-id-error">{{ $message }}</p> @enderror
                                </div>
                                <div class="flex flex-col sm:space-x-4 sm:flex-row">
                                    <div class="mt-4 sm:flex-1">
                                        <label for="type" class="block text-sm font-medium leading-6 text-gray-900">Type</label>
                                        <select wire:model.live="type" name="type" id="type" class="mt-2 block w-full rounded-md border-0 py-1.5 pl-3 pr-10 ring-1 ring-inset focus:ring-2 sm:text-sm sm:leading-6 @error('type') text-red-900 ring-red-300 placeholder:text-red-300 focus:ring-red-500 @else text-gray-900 ring-gray-300 placeholder:text-gray-400 focus:ring-keppel-600 @enderror" required @error('type') aria-invalid="true" @enderror aria-describedby="type-error">
                                            <option value="recurring">Standard</option>
                                            <option value="addon">Add-on</option>
                                            <option value="promo">Promo</option>
                                        </select>
                                        @error('type') <p class="mt-2 text-sm text-red-600" id="type-error">{{ $message }}</p> @enderror
                                    </div>

                                    <div class="mt-4 sm:flex-1">
                                        <label for="qty" class="block text-sm font-medium leading-6 text-gray-900">Quantity</label>
                                        <div class="relative mt-2 rounded-md shadow-sm">
                                            <input type="text" wire:model.live="qty" name="qty" id="qty" class="block w-full rounded-md border-0 py-1.5  ring-1 ring-inset pr-10  focus:ring-2 focus:ring-inset sm:text-sm sm:leading-6 @error('qty') text-red-900 ring-red-300 placeholder:text-red-300 focus:ring-red-500 @else text-gray-900 ring-gray-300 placeholder:text-gray-400 focus:ring-keppel-600 @enderror" min="1" required @error('qty') aria-invalid="true" @enderror aria-describedby="qty-error">
                                            @error('qty')
                                            <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                                                <svg class="h-5 w-5 text-red-500" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
                                                </svg>
                                            </div>
                                            @enderror
                                        </div>
                                        @error('qty') <p class="mt-2 text-sm text-red-600" id="qty-error">{{ $message }}</p> @enderror
                                    </div>
                                </div>
                            </div>
                            <div class="mt-2">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <svg class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z" clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                    <div class="ml-2 flex-1">
                                        <p x-show="type === 'promo'" class="m-0 text-sm text-gray-700">Promo items repeat on every order</p>
                                        <p x-show="type === 'addon'" class="m-0 text-sm text-gray-700">Add-on items are included on the next delivery only</p>
                                        <p x-show="type === 'standard' || type === 'recurring'" class="m-0 text-sm text-gray-700">Standard items repeat on every
                                            order</p>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-4">
                                <div class="flex justify-between">
                                    <label for="unit_price_override" class="block text-sm font-medium leading-6 text-gray-900">Unit price</label>
                                    <span class="text-sm leading-6 text-gray-500" id="unit_price_override-optional">Optional</span>
                                </div>
                                <div class="relative mt-2 rounded-md shadow-sm">
                                    <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                                        <span class="text-gray-500 sm:text-sm">$</span>
                                    </div>
                                    <input type="text" wire:model.live="formatted_unit_price_override" name="formatted_unit_price_override" id="formatted_unit_price_override" placeholder="Market price" class="block w-full rounded-md border-0 py-1.5 pl-7 pr-10  ring-1 ring-inset focus:ring-2 focus:ring-inset sm:text-sm sm:leading-6 @error('formatted_unit_price_override') text-red-900 ring-red-300 placeholder:text-red-300 focus:ring-red-500 @else text-gray-900 ring-gray-300 placeholder:text-gray-400 focus:ring-keppel-600 @enderror" @error('formatted_unit_price_override') aria-invalid="true" @enderror aria-describedby="unit_price_override-error">
                                    @error('formatted_unit_price_override')
                                    <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                                        <svg class="h-5 w-5 text-red-500" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                    @enderror
                                </div>
                                @error('formatted_unit_price_override')
                                <p class="mt-2 text-sm text-red-600" id="unit_price_override-error">{{ $message }}</p> @enderror
                                <div class="mt-2">
                                    <div class="flex">
                                        <div class="flex-shrink-0">
                                            <svg class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z" clip-rule="evenodd" />
                                            </svg>
                                        </div>
                                        <div class="ml-2 flex-1">
                                            <p class="m-0 text-sm text-gray-700">Override the market price on the next order</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="rounded-b-lg bg-gray-50 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
                <button type="submit" wire:loading.attr="disabled" class="inline-flex w-full justify-center rounded-md bg-keppel-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-keppel-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-keppel-600 sm:ml-3 sm:w-auto">
                    Update
                </button>
                <button type="button" wire:loading.attr="disabled" wire:click="close" class="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:mt-0 sm:w-auto">
                    Cancel
                </button>
            </div>
        </form>
    @endif
</x-modal>
