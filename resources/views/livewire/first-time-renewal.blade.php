<div class="bg-white rounded-lg">
    <div class="p-4 sm:p-6 lg:p-8">
        <div class="sm:flex sm:items-center">
            <div class="sm:flex-auto">
                <h1 class="text-base font-semibold leading-6 text-gray-900">First Time Renewal</h1>
                <p class="mt-2 text-sm text-gray-700">A list of subscriptions with an upcoming first renewal event.</p>
            </div>
            <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
                <button type="button" wire:click="export" wire:target="export" wire:loading.attr="disabled" wire
                        class="block rounded-md bg-keppel-600 px-3 py-2 text-center text-sm font-semibold text-white shadow-sm hover:bg-keppel-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-keppel-600">
                    Export
                </button>
            </div>
        </div>
        <div class="mt-8">
            <div class="md:flex md:justify-between mb-4 px-4 md:p-0">
                <div class="w-full mb-4 md:mb-0 md:flex md:flex-wrap md:gap-2">
                    <div>
                        <fieldset class="p-0">
                            <legend class="text-sm font-medium text-gray-900">Delivery date</legend>
                            <div class="mt-1 rounded-md bg-white shadow-sm">
                                <div class="flex -space-x-px">
                                    <div class="w-1/2 min-w-0 flex-1">
                                        <div>
                                            <label for="event_start_date" class="sr-only">Event start</label>
                                            <div class="relative rounded-md shadow-sm">

                                                <input type="date" name="event_start_date" id="event_start_date"
                                                       pattern="\d{4}-\d{2}-\d{2}" wire:model.live="event_start_date"
                                                       class="relative block w-full rounded-none rounded-l-md border-0 bg-transparent py-1.5 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:z-10 focus:ring-2 focus:ring-inset focus:ring-keppel-600 sm:text-sm sm:leading-6">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="min-w-0 flex-1">
                                        <div>
                                            <label for="event_end_date" class="sr-only">Event end</label>
                                            <div class="relative rounded-md shadow-sm">
                                                <input type="date" name="event_end_date" id="event_end_date"
                                                       pattern="\d{4}-\d{2}-\d{2}" wire:model.live="event_end_date"
                                                       class="relative block w-full rounded-none rounded-r-md border-0 bg-transparent py-1.5 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:z-10 focus:ring-2 focus:ring-inset focus:ring-keppel-600 sm:text-sm sm:leading-6">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </fieldset>
                    </div>
                </div>
            </div>
        </div>
        @if($subscriptions->isEmpty())
            <div class="mt-8 flow-root text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none"
                     viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round"
                          d="M9.879 7.519c1.171-1.025 3.071-1.025 4.242 0 1.172 1.025 1.172 2.687 0 3.712-.203.179-.43.326-.67.442-.745.361-1.45.999-1.45 1.827v.75M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9 5.25h.008v.008H12v-.008z" />
                </svg>
                <h3 class="mt-2 text-sm font-semibold text-gray-900">No subscriptions</h3>
                <p class="mt-1 text-sm text-gray-500">Try updating the filters.</p>
                <div class="mt-6">
                    <button wire:click.prevent="clearFilters" type="button"
                            class="inline-flex items-center rounded-md bg-keppel-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-keppel-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-keppel-600">
                        Clear filters
                    </button>
                </div>
            </div>
        @else
            <div class="mt-8 flow-root">
                <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                    <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                        <table class="min-w-full divide-y divide-gray-300">
                            <thead>
                            <tr>
                                <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-3">Name</th>
                                <th scope="col" class="px-3 py-3.5 text-sm font-semibold text-gray-900">Contact</th>
                                <th scope="col" class="px-3 py-3.5 text-sm font-semibold text-gray-900">Delivery method</th>
                                <th scope="col" class="px-3 py-3.5 text-sm font-semibold text-gray-900">Dates</th>
                                <th scope="col" class="sr-only">
                                    Manage
                                </th>
                            </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200">
                            @foreach($subscriptions as $subscription)
                                @php /** @var \App\Models\RecurringOrder $subscription */ @endphp
                                <tr class="border-t border-gray-300">
                                    <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm text-gray-900 sm:pl-3">
                                        <span class="gap-x-1.5 rounded-md py-1 text-sm font-medium">
                                            <div class="font-medium text-gray-900">
                                                <a href="{{ route('admin.users.edit', [$subscription->customer_id]) }}" target="_blank">
                                                    {{ $subscription->customer->first_name }} {{ $subscription->customer->last_name }}
                                                </a>
                                                <div class="mt-1 text-gray-500">{{ $subscription->fulfillment->title }}</div>
                                            </div>
                                        </span>
                                    </td>
                                    <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                        <span class="inline-flex items-center gap-x-1.5 rounded-md py-1 text-sm font-medium">
                                            <div>
                                                <a href="mailto:{{ $subscription->customer->email }}" target="_blank" class="block font-medium hover:text-keppel-700">{{ $subscription->customer->email }}</a>
                                                <a href="tel:{{ $subscription->customer->phone }}" target="_blank" class="block mt-1 hover:text-keppel-700">{{ $subscription->customer->phone }}</a>
                                            </div>
                                        </span>
                                    </td>
                                    <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                        {{ $subscription->fulfillment->title }}
                                    </td>
                                    <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                        <span class="inline-flex items-center gap-x-1.5 rounded-md py-1 text-sm font-medium">
                                            <div>
                                                <div class="font-medium text-gray-900">
                                                    Delivers:
                                                    @if(!is_null($subscription->overridden_ready_at))
                                                        {{ $subscription->overridden_ready_at->format('D, M jS') }} (Overridden)
                                                    @else
                                                        {{ $subscription->ready_at->format('D, M jS') }}
                                                    @endif
                                                </div>
                                                <div class="mt-1 text-gray-500 text-xs">Generates: {{ $subscription->generate_at->format('M j | H:i A') }}</div>
                                            </div>
                                        </span>

                                    </td>
                                    <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                                        <a href="{{ route('admin.subscriptions.edit', [$subscription->id]) }}" class="text-keppel-600 hover:text-keppel-900">View
                                            <span class="hidden">, {{ $subscription->id}}</span>
                                        </a>
                                    </td>
                                </tr>
                            @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        @endif
        @if($subscriptions->hasPages())
            <div class="border-t border-gray-200 mt-6 pt-6">
                {{ $subscriptions->links('pagination.livewire-tailwind') }}
            </div>
        @endif
    </div>
</div>
