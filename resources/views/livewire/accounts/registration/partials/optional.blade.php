<h1 class="text-lg font-semibold leading-5 text-gray-800 sm:text-2xl">One more thing!</h1>
<div class="mt-1 text-sm text-gray-500 sm:text-base">Help us out by filling in the optional fields below.</div>

<form
    method="POST"
    class="max-w-xl mx-auto mt-12"
    wire:submit="saveOptional"
>
    @csrf
    <div class="mt-6 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
        <input type="text" wire:model.live="username" name="username" class="hidden">
        @error('username')
            <p class="m-0 mt-2 text-sm text-red-600" id="first-name-error">There was an error. Please try again.</p>
        @enderror

        <div class="sm:col-span-6">
            <label for="referrer" class="block text-sm font-medium text-gray-700">
                <div class="flex items-center justify-between">
                    <div>How did you hear about GrazeCart?</div>
                    <div class="text-gray-500">Optional</div>
                </div>
            </label>
            <div class="relative mt-1 rounded-md shadow-sm">
                <select wire:model.live="referrer" id="referrer" name="referrer" class="block w-full rounded-md border border-gray-300 focus:outline-none focus:border-keppel-500 focus:ring-keppel-500 sm:text-sm @error('referrer') border-red-300 pr-10 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500 @enderror" aria-invalid="true" aria-describedby="referrer-error">
                    @foreach (config('grazecart.referrals') as $value => $referrer)
                        <option value="{{ $value }}">{{ $referrer }}</option>
                    @endforeach
                </select>
                @error('referrer')
                    <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                        <!-- Heroicon name: mini/exclamation-circle -->
                        <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
                        </svg>
                    </div>
                @enderror
            </div>
            @error('referrer')
                <p class="m-0 mt-2 text-sm text-red-600" id="referrer-error">{{ $message }}</p>
            @enderror
        </div>

        <div class="sm:col-span-6">
            <label for="current_online_sales" class="block text-sm font-medium text-gray-700">
                <div class="flex items-center justify-between">
                    <div>How much does your farm currently sell online each year?</div>
                    <div class="text-gray-500">Optional</div>
                </div>
            </label>
            <div class="relative mt-1 rounded-md shadow-sm">
                <select wire:model.live="current_online_sales" id="current_online_sales" name="current_online_sales" class="block w-full rounded-md border border-gray-300 focus:outline-none focus:border-keppel-500 focus:ring-keppel-500 sm:text-sm @error('current_online_sales') border-red-300 pr-10 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500 @enderror" aria-invalid="true" aria-describedby="current_online_sales-error">
                    @foreach (config('grazecart.yearly_revenue_questions') as $value => $current_online_sales)
                        <option value="{{ $value }}">{{ $current_online_sales }}</option>
                    @endforeach
                </select>
                @error('current_online_sales')
                <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                    <!-- Heroicon name: mini/exclamation-circle -->
                    <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
                    </svg>
                </div>
                @enderror
            </div>
            @error('current_online_sales')
                <p class="m-0 mt-2 text-sm text-red-600" id="current_online_sales-error">{{ $message }}</p>
            @enderror
        </div>

        <div class="mt-6 flex justify-center sm:col-span-6">
            <button type="submit" wire:click.prevent="saveOptional" wire:loading.attr="disabled" class="w-full rounded-md bg-keppel-600 px-3.5 py-2.5 text-lg font-semibold text-white shadow-sm hover:bg-keppel-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-keppel-600">
                <div wire:loading.remove>Start trial</div>
                <div wire:loading.inline-flex>
                    <div class="inline-flex items-center">
                        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <div>
                            Starting trial...
                        </div>
                    </div>
                </div>
            </button>
        </div>

        <div class="sm:col-span-6">
            <p class="text-sm text-gray-500">
                By proceeding, you agree to our <a href="{{ route('account.legal.terms-of-service') }}" target="_blank" class="font-medium text-keppel-600 hover:text-keppel-500">terms of service</a> and <a href="{{ route('account.legal.privacy-policy') }}" target="_blank" class="font-medium text-keppel-600 hover:text-keppel-500">privacy policy.</a>
            </p>
        </div>
    </div>
</form>