
<div class="text-left" x-data="{ open: @entangle('open').live }">
    @php
        /** @var \Illuminate\Support\Collection $current_promo_products */
        /** @var \Illuminate\Support\Collection $other_products */
    @endphp
    <label id="product-listbox-label" class="block text-sm font-medium leading-6 text-gray-900">Product</label>
    <div class="relative mt-2">
        <button x-on:click="open = !open; $nextTick(() => { $refs.searchInput.focus(); });" type="button" class="relative w-full cursor-default rounded-md bg-white py-1.5 pl-3 pr-10 text-left text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:outline-none focus:ring-2 focus:ring-keppel-600 sm:text-sm sm:leading-6" aria-haspopup="listbox" aria-expanded="true" aria-labelledby="product-listbox-label">
            <span class="block truncate">{{ $selected_product?->title ?? 'Select product...' }}</span>
            <span class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                <svg class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fill-rule="evenodd" d="M10 3a.75.75 0 01.55.24l3.25 3.5a.75.75 0 11-1.1 1.02L10 4.852 7.3 7.76a.75.75 0 01-1.1-1.02l3.25-3.5A.75.75 0 0110 3zm-3.76 9.2a.75.75 0 011.06.04l2.7 2.908 2.7-2.908a.75.75 0 111.1 1.02l-3.25 3.5a.75.75 0 01-1.1 0l-3.25-3.5a.75.75 0 01.04-1.06z" clip-rule="evenodd" />
                </svg>
            </span>
        </button>

        <ul
            x-show="open"
            x-cloak
            x-on:click.away="open = false"
            x-transition:enter=""
            x-transition:enter-start=""
            x-transition:enter-end=""
            x-transition:leave="transition ease-in duration-100"
            x-transition:leave-start="opacity-100"
            x-transition:leave-end="opacity-0"
            class="absolute z-10 mt-1 w-full overflow-hidden rounded-md bg-white p-2 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm"
            tabindex="-1"
            role="listbox"
            aria-labelledby="product-listbox-label"
            aria-activedescendant="listbox-option-3"
        >
            <input type="text" wire:model.live.debounce.500ms="term" x-ref="searchInput" wire:keyup.enter="" class="w-full rounded-md border-0 bg-gray-100 px-4 py-2.5 text-gray-900 focus:ring-0 sm:text-sm" placeholder="Search..." role="combobox" aria-expanded="false" aria-controls="options">

            <!-- Results, show/hide based on command palette state. -->
            <ul class="-mb-2 max-h-72 scroll-py-2 overflow-auto py-2 text-sm text-gray-800" id="options" role="listbox">
                @if($current_promo_products->isNotEmpty())
                    <li>
                        <h2 class="bg-gray-100 px-4 py-2.5 text-xs font-semibold text-gray-900">Current incentives</h2>
                        <ul class="mt-2 text-sm text-gray-800">
                            @foreach($current_promo_products as $product)
                                <li wire:key="incentive-option-{{ $product->id }}" id="incentive-option-{{ $product->id }}" wire:click="selectProduct({{ $product->id }})" class="cursor-pointer select-none rounded-md px-4 py-2 hover:bg-keppel-600 hover:text-white" id="incentive-option-{{ $product->id }}" role="option" tabindex="-1">{{ $product->title }}</li>
                            @endforeach
                        </ul>
                    </li>
                @endif

                @if($other_products->isNotEmpty())
                    <li class="@if($current_promo_products->isNotEmpty()) mt-2 @endif">
                        @if($current_promo_products->isNotEmpty())
                            <h2 class="bg-gray-100 px-4 py-2.5 text-xs font-semibold text-gray-900">All products</h2>
                        @endif
                        <ul class="@if($current_promo_products->isNotEmpty()) mt-2 @endif text-sm text-gray-800">
                            <!-- Active: "bg-keppel-600 text-white" -->
                            @foreach($other_products as $product)
                                <li wire:key="other-option-{{ $product->id }}" id="other-option-{{ $product->id }}" wire:click="selectProduct({{ $product->id }})" class="cursor-pointer select-none rounded-md px-4 py-2 hover:bg-keppel-600 hover:text-white" id="other-option-{{ $product->id }}" role="option" tabindex="-1">{{ $product->title }}</li>
                            @endforeach
                        </ul>
                    </li>
                @endif
            </ul>

            @if ($current_promo_products->isEmpty() && $other_products->isEmpty())
                <!-- Empty state, show/hide based on command palette state. -->
                <div class="px-4 py-14 text-center sm:px-14">
                    <svg class="mx-auto h-6 w-6 text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M21 7.5l-2.25-1.313M21 7.5v2.25m0-2.25l-2.25 1.313M3 7.5l2.25-1.313M3 7.5l2.25 1.313M3 7.5v2.25m9 3l2.25-1.313M12 12.75l-2.25-1.313M12 12.75V15m0 6.75l2.25-1.313M12 21.75V19.5m0 2.25l-2.25-1.313m0-16.875L12 2.25l2.25 1.313M21 14.25v2.25l-2.25 1.313m-13.5 0L3 16.5v-2.25" />
                    </svg>
                    <p class="mt-4 text-sm text-gray-900">No products found using that search term.</p>
                </div>
            @endif
        </ul>
    </div>
</div>
