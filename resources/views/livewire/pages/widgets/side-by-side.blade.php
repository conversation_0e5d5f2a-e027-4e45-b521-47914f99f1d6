<form
        wire:submit="submit"
        class="relative transform rounded-lg text-left shadow-xl sm:mx-auto sm:my-8 sm:w-full sm:max-w-4xl"
        x-data="{ view: 'content', selecting_image: null }"
        x-on:media-selected.window="event => $wire.set(selecting_image, event.detail.photo.path)"
>
    <div class="rounded-lg bg-gray-100">
        <div class="sm:flex sm:items-start">
            <div class="sm:flex-1 sm:flex">
                <div class="p-4 sm:p-6 sm:w-1/4">
                    <div class="flex space-x-2">
                        <svg class="h-6 w-6 text-keppel-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M21 7.5l-9-5.25L3 7.5m18 0l-9 5.25m9-5.25v9l-9 5.25M3 7.5l9 5.25M3 7.5v9l9 5.25m0-9v9"/>
                        </svg>
                        <h3 class="text-base font-semibold leading-6 text-gray-900" id="modal-title">Edit Widget</h3>
                    </div>
                    <div class="mt-2">
                        <p class="text-sm text-gray-500">Edit the settings for the widget.</p>
                    </div>

                    <nav class="flex flex-1 flex-col" aria-label="Sidebar">
                        <ul role="list" class="-mx-2 space-y-1">
                            <li>
                                <button type="button" x-on:click="view = 'content'" :class="view === 'content' ? 'bg-gray-200 text-gray-900' : 'text-gray-600 hover:text-gray-700 hover:bg-gray-200'" class="group w-full flex items-center gap-x-3 rounded-md p-2 pl-3 text-sm/6 font-semibold">
                                    <svg class="size-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25H12"/>
                                    </svg>

                                    Content
                                </button>
                            </li>
                            <li>
                                <button type="button" x-on:click="view = 'background'" :class="view === 'background' ? 'bg-gray-200 text-gray-900' : 'text-gray-600 hover:text-gray-700 hover:bg-gray-200'" class="group w-full flex items-center gap-x-3 rounded-md p-2 pl-3 text-sm/6 font-semibold">
                                    <svg class="size-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"/>
                                    </svg>
                                    Background
                                </button>
                            </li>
                            <li>
                                <button type="button" x-on:click="view = 'size'" :class="view === 'size' ? 'bg-gray-200 text-gray-900' : 'text-gray-600 hover:text-gray-700 hover:bg-gray-200'" class="group w-full flex items-center gap-x-3 rounded-md p-2 pl-3 text-sm/6 font-semibold">
                                    <svg class="size-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 3.75v4.5m0-4.5h4.5m-4.5 0L9 9M3.75 20.25v-4.5m0 4.5h4.5m-4.5 0L9 15M20.25 3.75h-4.5m4.5 0v4.5m0-4.5L15 9m5.25 11.25h-4.5m4.5 0v-4.5m0 4.5L15 15"/>
                                    </svg>
                                    Size
                                </button>
                            </li>
                            <li>
                                <button type="button" x-on:click="view = 'general'" :class="view === 'general' ? 'bg-gray-200 text-gray-900' : 'text-gray-600 hover:text-gray-700 hover:bg-gray-200'" class="group w-full flex items-center gap-x-3 rounded-md p-2 pl-3 text-sm/6 font-semibold">
                                    <svg class="size-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="m11.25 11.25.041-.02a.75.75 0 0 1 1.063.852l-.708 2.836a.75.75 0 0 0 1.063.853l.041-.021M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9-3.75h.008v.008H12V8.25Z"/>
                                    </svg>

                                    General
                                </button>
                            </li>
                        </ul>
                    </nav>
                </div>

                <div class="p-4 text-left sm:p-6 sm:flex-1">
                    <div class="bg-white rounded-md shadow-sm p-3 text-left sm:p-4 sm:flex-1">
                        <div x-show="view === 'content'">
                            <div class="grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">


                                <div class="col-span-full">
                                    <div class="col-span-full">
                                        <label for="image_url" class="block text-sm font-medium leading-6 text-gray-900">Background Image</label>
                                        @if(!empty($image_url))
                                            <div class="mt-2 w-full aspect-w-16 aspect-h-9">
                                                <img src="{{ $image_url }}" alt="Image" class="w-full h-full object-center object-cover rounded-lg ">
                                            </div>
                                            <button type="button" x-on:click="$wire.set('image_url', null)" class="mt-2 relative text-sm cursor-pointer rounded-md bg-white font-semibold text-keppel-600 focus-within:outline-none focus-within:ring-2 focus-within:ring-keppel-600 focus-within:ring-offset-2 hover:text-keppel-500">
                                                Remove
                                            </button>
                                        @else
                                            <div wire:key="select-a-background-file" class="mt-2 flex justify-center rounded-lg border border-dashed border-gray-900/25 px-6 py-10">
                                                <div class="text-center">
                                                    <svg class="mx-auto h-12 w-12 text-gray-300" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
                                                        <path fill-rule="evenodd" d="M1.5 6a2.25 2.25 0 012.25-2.25h16.5A2.25 2.25 0 0122.5 6v12a2.25 2.25 0 01-2.25 2.25H3.75A2.25 2.25 0 011.5 18V6zM3 16.06V18c0 .414.336.75.75.75h16.5A.75.75 0 0021 18v-1.94l-2.69-2.689a1.5 1.5 0 00-2.12 0l-.88.879.97.97a.75.75 0 11-1.06 1.06l-5.16-5.159a1.5 1.5 0 00-2.12 0L3 16.061zm10.125-7.81a1.125 1.125 0 112.25 0 1.125 1.125 0 01-2.25 0z" clip-rule="evenodd"/>
                                                    </svg>
                                                    <div class="mt-4 flex text-sm leading-6 text-gray-600">
                                                        <button type="button" x-on:click="selecting_image = 'image_url'; eventHub.emit('mediaBrowser:toggle')" class="relative cursor-pointer rounded-md bg-white font-semibold text-keppel-600 focus-within:outline-none focus-within:ring-2 focus-within:ring-keppel-600 focus-within:ring-offset-2 hover:text-keppel-500">
                                                            <span>Select a file</span>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        @endif
                                    </div>
                                </div>

                                <div class="col-span-6">
                                    <div class="flex justify-between">
                                        <label for="text" class="block text-sm/6 font-medium text-gray-900">Text</label>
                                        <span class="text-sm/6 text-gray-500" id="email-optional">Supports HTML</span>
                                    </div>
                                    <div class="relative mt-2 rounded-md shadow-sm">
                                        <textarea wire:model="text"
                                                  name="text"
                                                  id="text"
                                                  rows="5"
                                                  class="block w-full rounded-md border border-gray-300 focus:outline-none focus:border-keppel-500 focus:ring-keppel-500 sm:text-sm @error('text') border-red-300 pr-10 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500 @enderror"
                                                  aria-invalid="true"
                                                  aria-describedby="text-error"
                                        ></textarea>
                                        @error('text')
                                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                                            <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"/>
                                            </svg>
                                        </div>
                                        @enderror
                                    </div>
                                    @error('text')
                                    <p class="m-0 mt-2 text-sm text-red-600" id="text-error">{{ $message }}</p>
                                    @enderror
                                </div>
                                <div class="sm:col-span-2">
                                    <label for="image_position" class="block text-sm/6 font-medium text-gray-900">Image Position</label>
                                    <div class="mt-2 relative rounded-md shadow-sm">
                                        <select wire:model="image_position" id="image_position" name="image_position" class="block w-full rounded-md border border-gray-300 focus:outline-none focus:border-keppel-500 focus:ring-keppel-500 sm:text-sm @error('image_position') border-red-300 pr-10 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500 @enderror" aria-invalid="true" aria-describedby="image_position-error">
                                            <option value="left">Left</option>
                                            <option value="right">Right</option>
                                        </select>
                                        @error('image_position')
                                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                                            <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"/>
                                            </svg>
                                        </div>
                                        @enderror
                                    </div>
                                    @error('image_position')
                                    <p class="m-0 mt-2 text-sm text-red-600" id="image_position-error">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div x-show="view === 'background'">
                            <div class="grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
                                <div class="sm:col-span-2">
                                    <label for="background_color" class="block text-sm/6 font-medium text-gray-900">Color</label>
                                    <div class="relative mt-2 rounded-md shadow-sm">
                                        <div x-data x-on:color-selected="event => $wire.background_color = event.detail;">
                                            <x-form.color-picker initial_color="{{ $background_color }}"/>
                                        </div>
                                        @error('background_color')
                                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                                            <!-- Heroicon name: mini/exclamation-circle -->
                                            <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"/>
                                            </svg>
                                        </div>
                                        @enderror
                                    </div>
                                    @error('background_color')
                                    <p class="m-0 mt-2 text-sm text-red-600" id="background_color-error">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div x-show="view === 'size'">
                            <div class="grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
                                <div class="sm:col-span-3">
                                    <label for="padding_top" class="block text-sm/6 font-medium text-gray-900">Padding Top</label>
                                    <div class="mt-2 relative rounded-md shadow-sm">
                                        <select wire:model="padding_top" id="padding_top" name="padding_top" class="block w-full rounded-md border border-gray-300 focus:outline-none focus:border-keppel-500 focus:ring-keppel-500 sm:text-sm @error('padding_top') border-red-300 pr-10 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500 @enderror" aria-invalid="true" aria-describedby="padding_top-error">
                                            <option value="none">None</option>
                                            <option value="sm">Small</option>
                                            <option value="md">Medium</option>
                                            <option value="lg">Large</option>
                                            <option value="xl">X-Large</option>
                                        </select>
                                        @error('padding_top')
                                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                                            <!-- Heroicon name: mini/exclamation-circle -->
                                            <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"/>
                                            </svg>
                                        </div>
                                        @enderror
                                    </div>
                                    @error('padding_top')
                                    <p class="m-0 mt-2 text-sm text-red-600" id="padding_top-error">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div class="sm:col-span-3">
                                    <label for="padding_bottom" class="block text-sm/6 font-medium text-gray-900">Padding Bottom</label>
                                    <div class="mt-2 relative rounded-md shadow-sm">
                                        <select wire:model="padding_bottom" id="padding_bottom" name="padding_bottom" class="block w-full rounded-md border border-gray-300 focus:outline-none focus:border-keppel-500 focus:ring-keppel-500 sm:text-sm @error('padding_bottom') border-red-300 pr-10 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500 @enderror" aria-invalid="true" aria-describedby="padding_bottom-error">
                                            <option value="none">None</option>
                                            <option value="sm">Small</option>
                                            <option value="md">Medium</option>
                                            <option value="lg">Large</option>
                                            <option value="xl">X-Large</option>
                                        </select>
                                        @error('padding_bottom')
                                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                                            <!-- Heroicon name: mini/exclamation-circle -->
                                            <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"/>
                                            </svg>
                                        </div>
                                        @enderror
                                    </div>
                                    @error('padding_bottom')
                                    <p class="m-0 mt-2 text-sm text-red-600" id="padding_bottom-error">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div class="sm:col-span-3">
                                    <label for="max_width" class="block text-sm/6 font-medium text-gray-900">Max Width</label>
                                    <div class="mt-2 relative rounded-md shadow-sm">
                                        <select wire:model="max_width" id="max_width" name="max_width" class="block w-full rounded-md border border-gray-300 focus:outline-none focus:border-keppel-500 focus:ring-keppel-500 sm:text-sm @error('max_width') border-red-300 pr-10 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500 @enderror" aria-invalid="true" aria-describedby="max_width-error">
                                            <option value="none">None</option>
                                            <option value="sm">Small</option>
                                            <option value="md">Medium</option>
                                            <option value="lg">Large</option>
                                            <option value="xl">X-Large</option>
                                        </select>
                                        @error('max_width')
                                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                                            <!-- Heroicon name: mini/exclamation-circle -->
                                            <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"/>
                                            </svg>
                                        </div>
                                        @enderror
                                    </div>
                                    @error('max_width')
                                    <p class="m-0 mt-2 text-sm text-red-600" id="max_width-error">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div x-show="view === 'general'">
                            <div class="grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
                                <div class="sm:col-span-3">
                                    <label for="name" class="block text-sm/6 font-medium text-gray-900">Name</label>
                                    <div class="relative mt-2 rounded-md shadow-sm">
                                        <input type="text" wire:model="name" name="name" id="name" class="block w-full rounded-md border border-gray-300 focus:outline-none focus:border-keppel-500 focus:ring-keppel-500 sm:text-sm @error('name') border-red-300 pr-10 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500 @enderror" aria-invalid="true" aria-describedby="name-error">
                                        @error('name')
                                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                                            <!-- Heroicon name: mini/exclamation-circle -->
                                            <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"/>
                                            </svg>
                                        </div>
                                        @enderror
                                    </div>
                                    @error('name')
                                    <p class="m-0 mt-2 text-sm text-red-600" id="name-error">{{ $message }}</p>
                                    @enderror
                                </div>
                                <div class="sm:col-span-3">
                                    <label for="html_id" class="block text-sm/6 font-medium text-gray-900">HTML ID</label>
                                    <div class="relative mt-2 rounded-md shadow-sm">
                                        <input type="text" wire:model="html_id" name="html_id" id="html_id" class="block w-full rounded-md border border-gray-300 focus:outline-none focus:border-keppel-500 focus:ring-keppel-500 sm:text-sm @error('html_id') border-red-300 pr-10 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500 @enderror" aria-invalid="true" aria-describedby="html_id-error">
                                        @error('html_id')
                                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                                            <!-- Heroicon html_id: mini/exclamation-circle -->
                                            <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"/>
                                            </svg>
                                        </div>
                                        @enderror
                                    </div>
                                    @error('html_id')
                                    <p class="m-0 mt-2 text-sm text-red-600" id="html_id-error">{{ $message }}</p>
                                    @enderror
                                    <p class="m-0 mt-2 text-sm text-gray-600">Useful when linking directly to this section</p>
                                </div>
                            </div>
                            <div class="mt-10 border-t border-gray-200 pt-6 flex items-start justify-between">
                                <div>
                                    <h3 class="text-base font-semibold text-gray-900">Delete widget</h3>
                                    <div class="mt-2 max-w-xl text-sm text-gray-500">
                                        <p class="m-0">Permanantly remove this widget from the page.</p>
                                    </div>
                                </div>
                                <div class="ml-6 flex shrink-0 items-center">
                                    <button type="button" wire:click="deleteWidget('{{ $widget['id'] }}')" class="inline-flex items-center rounded-md bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-600">Delete</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="pt-4 flex items-center justify-end gap-x-4">
                        <button type="button" wire:click="cancel" class="text-sm/6 font-semibold text-gray-900">Close</button>
                        <button type="button" wire:click="preview" class="rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50">Preview</button>
                        <button type="button" wire:click="save" class="rounded-md bg-keppel-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-keppel-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-keppel-600">Save</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
