@php
    //    /** @var string $id */
    //    /** @var string $padding_top */
    //    /** @var string $ids */
    //    /** @var array{color: string, image: string|null, effect: string|null, overlay: string|null} $background */
    //    /** @var array{label: string, action: string} $cta */
@endphp

@php
    //    /** @var string $id */
    //    /** @var string $padding */
    //    /** @var string $ids */
    //    /** @var array{color: string, image: string|null, effect: string|null, overlay: string|null} $background */
    //    /** @var array{label: string, action: string} $cta */
@endphp


<form
        wire:submit="submit"
        class="relative transform rounded-lg text-left shadow-xl sm:mx-auto sm:my-8 sm:w-full sm:max-w-4xl"
        x-data="{ view: 'content', selecting_image: null }"
        x-on:media-selected.window="event => $wire.set(selecting_image, event.detail.photo.path)"
>
    <div class="rounded-lg bg-gray-100">
        <div class="sm:flex sm:items-start">
            <div class="sm:flex-1 sm:flex">
                <div class="p-4 sm:p-6 sm:w-1/4">
                    <div class="flex space-x-2">
                        <svg class="h-6 w-6 text-keppel-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M21 7.5l-9-5.25L3 7.5m18 0l-9 5.25m9-5.25v9l-9 5.25M3 7.5l9 5.25M3 7.5v9l9 5.25m0-9v9"/>
                        </svg>
                        <h3 class="text-base font-semibold leading-6 text-gray-900" id="modal-title">Edit Widget</h3>
                    </div>
                    <div class="mt-2">
                        <p class="text-sm text-gray-500">Edit the settings for the widget.</p>
                    </div>

                    <nav class="flex flex-1 flex-col" aria-label="Sidebar">
                        <ul role="list" class="-mx-2 space-y-1">
                            <li>
                                <button type="button" x-on:click="view = 'content'" :class="view === 'content' ? 'bg-gray-200 text-gray-900' : 'text-gray-600 hover:text-gray-700 hover:bg-gray-200'" class="group w-full flex items-center gap-x-3 rounded-md p-2 pl-3 text-sm/6 font-semibold">
                                    <svg class="size-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25H12"/>
                                    </svg>

                                    Content
                                </button>
                            </li>
                            <li>
                                <button type="button" x-on:click="view = 'background'" :class="view === 'background' ? 'bg-gray-200 text-gray-900' : 'text-gray-600 hover:text-gray-700 hover:bg-gray-200'" class="group w-full flex items-center gap-x-3 rounded-md p-2 pl-3 text-sm/6 font-semibold">
                                    <svg class="size-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"/>
                                    </svg>
                                    Background
                                </button>
                            </li>
                            <li>
                                <button type="button" x-on:click="view = 'size'" :class="view === 'size' ? 'bg-gray-200 text-gray-900' : 'text-gray-600 hover:text-gray-700 hover:bg-gray-200'" class="group w-full flex items-center gap-x-3 rounded-md p-2 pl-3 text-sm/6 font-semibold">
                                    <svg class="size-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 3.75v4.5m0-4.5h4.5m-4.5 0L9 9M3.75 20.25v-4.5m0 4.5h4.5m-4.5 0L9 15M20.25 3.75h-4.5m4.5 0v4.5m0-4.5L15 9m5.25 11.25h-4.5m4.5 0v-4.5m0 4.5L15 15"/>
                                    </svg>
                                    Size
                                </button>
                            </li>
                            <li>
                                <button type="button" x-on:click="view = 'general'" :class="view === 'general' ? 'bg-gray-200 text-gray-900' : 'text-gray-600 hover:text-gray-700 hover:bg-gray-200'" class="group w-full flex items-center gap-x-3 rounded-md p-2 pl-3 text-sm/6 font-semibold">
                                    <svg class="size-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="m11.25 11.25.041-.02a.75.75 0 0 1 1.063.852l-.708 2.836a.75.75 0 0 0 1.063.853l.041-.021M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9-3.75h.008v.008H12V8.25Z"/>
                                    </svg>

                                    General
                                </button>
                            </li>
                        </ul>
                    </nav>
                </div>

                <div class="p-4 text-left sm:p-6 sm:flex-1">
                    <div class="bg-white rounded-md shadow-sm p-3 text-left sm:p-4 sm:flex-1">
                        <div x-show="view === 'content'">
                            <div class="grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
                                <div class="col-span-full">
                                    <label for="product_ids" class="block text-sm/6 font-medium text-gray-900">Product IDs</label>
                                    <div class="relative mt-2 rounded-md shadow-sm">
                                        <input type="text" wire:model="product_ids" name="product_ids" id="product_ids" autocomplete="given-name" required class="block w-full rounded-md border border-gray-300 focus:outline-none focus:border-keppel-500 focus:ring-keppel-500 sm:text-sm @error('product_ids') border-red-300 pr-10 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500 @enderror" aria-invalid="true" aria-describedby="product_ids-error">
                                        @error('product_ids')
                                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                                            <!-- Heroicon name: mini/exclamation-circle -->
                                            <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"/>
                                            </svg>
                                        </div>
                                        @enderror
                                    </div>
                                    @error('product_ids')
                                    <p class="m-0 mt-2 text-sm text-red-600" id="product_ids-error">{{ $message }}</p>
                                    @enderror
                                    <p class="mt-3 text-sm/6 text-gray-600">Accepts a comma-separated list.</p>
                                </div>

                                <div class="sm:col-span-3">
                                    <label for="cta_action" class="block text-sm/6 font-medium text-gray-900">CTA Action</label>
                                    <div class="mt-2 relative rounded-md shadow-sm">
                                        <select wire:model="cta_action" id="cta_action" name="cta_action" class="block w-full rounded-md border border-gray-300 focus:outline-none focus:border-keppel-500 focus:ring-keppel-500 sm:text-sm @error('cta_action') border-red-300 pr-10 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500 @enderror" aria-invalid="true" aria-describedby="cta_action-error">
                                            <option value="add_to_cart">Add to cart</option>
                                            <option value="show_details">View product</option>
                                        </select>
                                        @error('cta_action')
                                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                                            <!-- Heroicon name: mini/exclamation-circle -->
                                            <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"/>
                                            </svg>
                                        </div>
                                        @enderror
                                    </div>
                                    @error('cta_action')
                                    <p class="m-0 mt-2 text-sm text-red-600" id="cta_action-error">{{ $message }}</p>
                                    @enderror
                                </div>
                                <div class="sm:col-span-3">
                                    <label for="cta_label" class="block text-sm/6 font-medium text-gray-900">CTA Label</label>
                                    <div class="relative mt-2 rounded-md shadow-sm">
                                        <input type="text" wire:model="cta_label" name="cta_label" id="cta_label" autocomplete="given-name" required class="block w-full rounded-md border border-gray-300 focus:outline-none focus:border-keppel-500 focus:ring-keppel-500 sm:text-sm @error('product_ids') border-red-300 pr-10 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500 @enderror" aria-invalid="true" aria-describedby="cta_label-error">
                                        @error('cta_label')
                                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                                            <!-- Heroicon name: mini/exclamation-circle -->
                                            <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"/>
                                            </svg>
                                        </div>
                                        @enderror
                                    </div>
                                    @error('cta_label')
                                    <p class="m-0 mt-2 text-sm text-red-600" id="cta_label-error">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div x-show="view === 'background'">
                            <div class="grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
                                <div class="sm:col-span-2">
                                    <label for="background_color" class="block text-sm/6 font-medium text-gray-900">Color</label>
                                    <div class="relative mt-2 rounded-md shadow-sm">
                                        <div x-data x-on:color-selected="event => $wire.background_color = event.detail;">
                                            <x-form.color-picker initial_color="{{ $background_color }}"/>
                                        </div>

                                        @error('background_color')
                                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                                            <!-- Heroicon name: mini/exclamation-circle -->
                                            <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"/>
                                            </svg>
                                        </div>
                                        @enderror
                                    </div>
                                    @error('background_color')
                                    <p class="m-0 mt-2 text-sm text-red-600" id="background_color-error">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div x-show="view === 'size'">
                            <div class="grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
                                <div class="sm:col-span-3">
                                    <label for="padding_top" class="block text-sm/6 font-medium text-gray-900">Padding Top</label>
                                    <div class="mt-2 relative rounded-md shadow-sm">
                                        <select wire:model="padding_top" id="padding_top" name="padding_top" class="block w-full rounded-md border border-gray-300 focus:outline-none focus:border-keppel-500 focus:ring-keppel-500 sm:text-sm @error('padding_top') border-red-300 pr-10 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500 @enderror" aria-invalid="true" aria-describedby="padding_top-error">
                                            <option value="none">None</option>
                                            <option value="sm">Small</option>
                                            <option value="md">Medium</option>
                                            <option value="lg">Large</option>
                                            <option value="xl">X-Large</option>
                                        </select>
                                        @error('padding_top')
                                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                                            <!-- Heroicon name: mini/exclamation-circle -->
                                            <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"/>
                                            </svg>
                                        </div>
                                        @enderror
                                    </div>
                                    @error('padding_top')
                                    <p class="m-0 mt-2 text-sm text-red-600" id="padding_top-error">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div class="sm:col-span-3">
                                    <label for="padding_bottom" class="block text-sm/6 font-medium text-gray-900">Padding Bottom</label>
                                    <div class="mt-2 relative rounded-md shadow-sm">
                                        <select wire:model="padding_bottom" id="padding_bottom" name="padding_bottom" class="block w-full rounded-md border border-gray-300 focus:outline-none focus:border-keppel-500 focus:ring-keppel-500 sm:text-sm @error('padding_bottom') border-red-300 pr-10 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500 @enderror" aria-invalid="true" aria-describedby="padding_bottom-error">
                                            <option value="none">None</option>
                                            <option value="sm">Small</option>
                                            <option value="md">Medium</option>
                                            <option value="lg">Large</option>
                                            <option value="xl">X-Large</option>
                                        </select>
                                        @error('padding_bottom')
                                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                                            <!-- Heroicon name: mini/exclamation-circle -->
                                            <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"/>
                                            </svg>
                                        </div>
                                        @enderror
                                    </div>
                                    @error('padding_bottom')
                                    <p class="m-0 mt-2 text-sm text-red-600" id="padding_bottom-error">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div class="sm:col-span-3">
                                    <label for="max_width" class="block text-sm/6 font-medium text-gray-900">Max Width</label>
                                    <div class="mt-2 relative rounded-md shadow-sm">
                                        <select wire:model="max_width" id="max_width" name="max_width" class="block w-full rounded-md border border-gray-300 focus:outline-none focus:border-keppel-500 focus:ring-keppel-500 sm:text-sm @error('max_width') border-red-300 pr-10 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500 @enderror" aria-invalid="true" aria-describedby="max_width-error">
                                            <option value="none">None</option>
                                            <option value="sm">Small</option>
                                            <option value="md">Medium</option>
                                            <option value="lg">Large</option>
                                            <option value="xl">X-Large</option>
                                        </select>
                                        @error('max_width')
                                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                                            <!-- Heroicon name: mini/exclamation-circle -->
                                            <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"/>
                                            </svg>
                                        </div>
                                        @enderror
                                    </div>
                                    @error('max_width')
                                    <p class="m-0 mt-2 text-sm text-red-600" id="max_width-error">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div x-show="view === 'general'">
                            <div class="grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
                                <div class="sm:col-span-3">
                                    <label for="name" class="block text-sm/6 font-medium text-gray-900">Name</label>
                                    <div class="relative mt-2 rounded-md shadow-sm">
                                        <input type="text" wire:model="name" name="name" id="name" class="block w-full rounded-md border border-gray-300 focus:outline-none focus:border-keppel-500 focus:ring-keppel-500 sm:text-sm @error('name') border-red-300 pr-10 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500 @enderror" aria-invalid="true" aria-describedby="name-error">
                                        @error('name')
                                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                                            <!-- Heroicon name: mini/exclamation-circle -->
                                            <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"/>
                                            </svg>
                                        </div>
                                        @enderror
                                    </div>
                                    @error('name')
                                    <p class="m-0 mt-2 text-sm text-red-600" id="name-error">{{ $message }}</p>
                                    @enderror
                                </div>
                                <div class="sm:col-span-3">
                                    <label for="html_id" class="block text-sm/6 font-medium text-gray-900">HTML ID</label>
                                    <div class="relative mt-2 rounded-md shadow-sm">
                                        <input type="text" wire:model="html_id" name="html_id" id="html_id" class="block w-full rounded-md border border-gray-300 focus:outline-none focus:border-keppel-500 focus:ring-keppel-500 sm:text-sm @error('html_id') border-red-300 pr-10 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500 @enderror" aria-invalid="true" aria-describedby="html_id-error">
                                        @error('html_id')
                                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                                            <!-- Heroicon html_id: mini/exclamation-circle -->
                                            <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"/>
                                            </svg>
                                        </div>
                                        @enderror
                                    </div>
                                    @error('html_id')
                                    <p class="m-0 mt-2 text-sm text-red-600" id="html_id-error">{{ $message }}</p>
                                    @enderror
                                    <p class="m-0 mt-2 text-sm text-gray-600">Useful when linking directly to this section</p>
                                </div>
                            </div>
                            <div class="mt-10 border-t border-gray-200 pt-6 flex items-start justify-between">
                                <div>
                                    <h3 class="text-base font-semibold text-gray-900">Delete widget</h3>
                                    <div class="mt-2 max-w-xl text-sm text-gray-500">
                                        <p class="m-0">Permanantly remove this widget from the page.</p>
                                    </div>
                                </div>
                                <div class="ml-6 flex shrink-0 items-center">
                                    <button type="button" wire:click="deleteWidget('{{ $widget['id'] }}')" class="inline-flex items-center rounded-md bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-600">Delete</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="pt-4 flex items-center justify-end gap-x-4">
                        <button type="button" wire:click="cancel" class="text-sm/6 font-semibold text-gray-900">Close</button>
                        <button type="button" wire:click="preview" class="rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50">Preview</button>
                        <button type="button" wire:click="save" class="rounded-md bg-keppel-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-keppel-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-keppel-600">Save</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

