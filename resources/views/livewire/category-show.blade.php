@php
    /** @var App\Models\Category $category */
    $subcategories = $category->subcategories;
    $isSubcategory = $category->isSubcategory()
@endphp

<div class="mx-auto max-w-7xl" x-on:media-selected.window="event => $wire.set('cover_photo', event.detail.photo.path)">
    <div class="relative border-b border-gray-200 pb-5">
        <div class="md:flex md:items-center md:justify-between">
            <h3 class="text-lg font-semibold leading-6 text-gray-900">
                @if($isSubcategory)
                    {{ $category->parentCategory->name }} >
                @endif
                {{ $category->name }}
            </h3>
        </div>
    </div>
    <div class="mt-4">
        <div class="block">
            <nav class="flex space-x-4" aria-label="Tabs">
                <!-- Current: "bg-gray-200 text-gray-800", Default: "text-gray-600 hover:text-gray-800" -->
                <button wire:click="set('tab', 'settings')" class="rounded-md px-3 py-2 text-sm font-medium @if($tab === 'settings') bg-gray-200 text-gray-800 hover:text-gray-800 @else text-gray-600 hover:text-gray-800 @endif">
                    Settings
                </button>
                <button wire:click="set('tab', 'products')" class="rounded-md px-3 py-2 text-sm font-medium @if($tab === 'products') bg-gray-200 text-gray-800 hover:text-gray-800 @else text-gray-600 hover:text-gray-800 @endif">
                    Products
                </button>
                @if(!$isSubcategory)
                    <button wire:click="set('tab', 'subcategories')" class="rounded-md px-3 py-2 text-sm font-medium @if($tab === 'subcategories') bg-gray-200 text-gray-800 hover:text-gray-800 @else text-gray-600 hover:text-gray-800 @endif">
                        Subcategories
                    </button>
                @endif
            </nav>
        </div>
    </div>

    @if($tab === 'settings')
        <div wire:key="settings-tab" class="mt-4 rounded-lg bg-white shadow">
            <div class="mx-auto max-w-2xl px-4 py-5 sm:p-6">

                <div class="mt-6 space-y-12">
                    <div class="border-b border-gray-900/10 pb-12">
                        <h2 class="text-base font-semibold leading-7 text-gray-900">Basic</h2>
                        <p class="mt-1 text-sm leading-6 text-gray-600">This information is used when displaying this category in the store.</p>

                        <div class="mt-10 grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">


                            <div class="col-span-full">
                                <label for="slug" class="block text-sm font-medium leading-6 text-gray-900">URL</label>
                                <div class="mt-2">
                                    <div class="relative isolate flex rounded-md shadow-sm ring-1 ring-inset ring-gray-300 focus-within:ring-2 focus-within:ring-inset focus-within:ring-keppel-600 @error('display_name') border-red-300 placeholder-red-300 focus:border-red-500 focus:ring-red-500 @enderror">
                                        <span class="flex select-none items-center pl-3 text-gray-500 sm:text-sm">
                                            @if($isSubcategory)
                                                {{ config('app.url') }}/store/categories/{{ $category->parentCategory->slug}}/
                                            @else
                                                {{ config('app.url') }}/store/categories/
                                            @endif
                                        </span>
                                        <input type="text" name="slug" id="slug" wire:model="slug" class="block flex-1 border-0 bg-transparent py-1.5 pr-10 pl-0.5 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm sm:leading-6 @error('display_name') pr-10 text-red-900 placeholder-red-300 @enderror" placeholder="my-category-slug" @error('display_name') aria-invalid="true" aria-describedby="slug-error" @enderror>
                                        <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                                            <a href="{{ ! is_null($category->category_id) ? route('store.subcategories.show', [$category->parentCategory?->slug, $category->slug]) : route('store.categories.show', [$category->slug]) }}" target="_blank">
                                                <svg class="h-5 w-5 text-keppel-700 hover:text-keppel-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 6H5.25A2.25 2.25 0 0 0 3 8.25v10.5A2.25 2.25 0 0 0 5.25 21h10.5A2.25 2.25 0 0 0 18 18.75V10.5m-10.5 6L21 3m0 0h-5.25M21 3v5.25"/>
                                                </svg>

                                            </a>

                                        </div>
                                    </div>
                                </div>
                                @error('slug')
                                <p class="m-0 mt-2 text-sm text-red-600" id="slug-error">{{ $message }}</p>
                                @enderror
                            </div>

                            <div class="col-span-full">
                                <label for="cover-photo" class="block text-sm font-medium leading-6 text-gray-900">Cover photo</label>
                                @if(!empty($cover_photo))
                                    <img src="{{ $cover_photo }}" alt="" class="mt-2 w-full rounded-lg">
                                    <button type="button" x-on:click="$wire.set('cover_photo', null)" class="mt-2 relative text-sm cursor-pointer rounded-md bg-white font-semibold text-keppel-600 focus-within:outline-none focus-within:ring-2 focus-within:ring-keppel-600 focus-within:ring-offset-2 hover:text-keppel-500">
                                        Remove
                                    </button>
                                @else
                                    <div wire:key="select-a-file" class="mt-2 flex justify-center rounded-lg border border-dashed border-gray-900/25 px-6 py-10">
                                        <div class="text-center">
                                            <svg class="mx-auto h-12 w-12 text-gray-300" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
                                                <path fill-rule="evenodd" d="M1.5 6a2.25 2.25 0 012.25-2.25h16.5A2.25 2.25 0 0122.5 6v12a2.25 2.25 0 01-2.25 2.25H3.75A2.25 2.25 0 011.5 18V6zM3 16.06V18c0 .414.336.75.75.75h16.5A.75.75 0 0021 18v-1.94l-2.69-2.689a1.5 1.5 0 00-2.12 0l-.88.879.97.97a.75.75 0 11-1.06 1.06l-5.16-5.159a1.5 1.5 0 00-2.12 0L3 16.061zm10.125-7.81a1.125 1.125 0 112.25 0 1.125 1.125 0 01-2.25 0z" clip-rule="evenodd"/>
                                            </svg>
                                            <div class="mt-4 flex text-sm leading-6 text-gray-600">
                                                <button x-on:click="eventHub.emit('mediaBrowser:toggle')" class="relative cursor-pointer rounded-md bg-white font-semibold text-keppel-600 focus-within:outline-none focus-within:ring-2 focus-within:ring-keppel-600 focus-within:ring-offset-2 hover:text-keppel-500">
                                                    <span>Select a file</span>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                @endif
                            </div>

                            <div class="sm:col-span-3">
                                <label for="name" class="block text-sm font-medium leading-6 text-gray-900">Name</label>
                                <div class="relative mt-2 rounded-md shadow-sm">
                                    <input type="text" name="name" id="name" wire:model="name"
                                           class="block w-full rounded-md border border-gray-300 focus:outline-none focus:border-keppel-500 focus:ring-keppel-500 sm:text-sm @error('name') border-red-300 pr-10 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500 @enderror"
                                           @error('name') aria-invalid="true" aria-describedby="name-error" @enderror
                                    />
                                    @error('name')
                                    <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                                        <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg"
                                             viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                            <path fill-rule="evenodd"
                                                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z"
                                                  clip-rule="evenodd"/>
                                        </svg>
                                    </div>
                                    @enderror
                                </div>
                                @error('name')
                                <p class="m-0 mt-2 text-sm text-red-600" id="name-error">{{ $message }}</p>
                                @enderror
                            </div>

                            <div class="sm:col-span-3">
                                <div class="flex justify-between">
                                    <label for="display-name" class="block text-sm font-medium leading-6 text-gray-900">Display name</label>
                                    <span class="text-sm leading-6 text-gray-500" id="display-name-optional">Optional</span>
                                </div>
                                <div class="relative mt-2 rounded-md shadow-sm">
                                    <input type="text" name="display_name" id="display_name" wire:model="display_name" placeholder="{{ $category->name }}"
                                           class="block w-full rounded-md border border-gray-300 focus:outline-none focus:border-keppel-500 focus:ring-keppel-500 sm:text-sm @error('display_name') border-red-300 pr-10 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500 @enderror"
                                           @error('display_name') aria-invalid="true" aria-describedby="display_name-error" @enderror
                                    />
                                    @error('display_name')
                                    <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                                        <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg"
                                             viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                            <path fill-rule="evenodd"
                                                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z"
                                                  clip-rule="evenodd"/>
                                        </svg>
                                    </div>
                                    @enderror
                                </div>
                                @error('display_name')
                                <p class="m-0 mt-2 text-sm text-red-600" id="display_name-error">{{ $message }}</p>
                                @enderror
                            </div>

                            <div class="col-span-full">
                                <label for="subheading" class="block text-sm font-medium leading-6 text-gray-900">Subheading</label>
                                <div class="mt-2">
                                    <textarea id="subheading" name="subheading" wire:model="subheading" rows="4" class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-keppel-600 sm:text-sm sm:leading-6"></textarea>
                                </div>
                            </div>

                            <div class="col-span-full">
                                <label for="summary" class="block text-sm font-medium leading-6 text-gray-900">Summary</label>
                                <div class="mt-2">
                                    <textarea id="summary" name="summary" wire:model="summary" rows="4" class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-keppel-600 sm:text-sm sm:leading-6"></textarea>
                                </div>
                                <p class="mt-3 text-sm leading-6 text-gray-600">A brief, one or two sentence description.</p>
                            </div>

                            <div class="col-span-full">
                                <div class="flex justify-between">
                                    <label for="description" class="block text-sm font-medium leading-6 text-gray-900">Description</label>
                                    <span class="text-sm leading-6 text-gray-500" id="description-optional">Supports HTML</span>
                                </div>
                                <div class="mt-2">
                                    <textarea id="description" name="description" wire:model="description" rows="12" class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-keppel-600 sm:text-sm sm:leading-6" aria-describedby="description-optional"></textarea>
                                </div>
                                <p class="mt-3 text-sm leading-6 text-gray-600">A long descripiton shown in the footer.</p>
                            </div>


                        </div>
                    </div>

                    <div class="border-b border-gray-900/10 pb-12">
                        <h2 class="text-base font-semibold leading-7 text-gray-900">SEO</h2>
                        <p class="mt-1 text-sm leading-6 text-gray-600">Configure the SEO settings for this category.</p>

                        <div class="mt-10 grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
                            <div class="sm:col-span-4">
                                <label for="meta-title" class="block text-sm font-medium leading-6 text-gray-900">Meta title</label>
                                <div class="mt-2">
                                    <input type="text" name="meta-title" wire:model="seo_meta_title" id="meta-title" class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-keppel-600 sm:text-sm sm:leading-6">
                                </div>
                                <p class="mt-3 text-sm leading-6 text-gray-600">The title used in the HTML header.</p>

                            </div>

                            <div class="col-span-full">
                                <label for="meta-description" class="block text-sm font-medium leading-6 text-gray-900">Meta description</label>
                                <div class="mt-2">
                                    <textarea id="meta-description" name="meta-description" wire:model="seo_meta_description" rows="3" class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-keppel-600 sm:text-sm sm:leading-6"></textarea>
                                </div>
                                <p class="mt-3 text-sm leading-6 text-gray-600">The description used in the HTML header.</p>

                            </div>

                            <div class="sm:col-span-6">
                                <div class="flex items-center">
                                    <div class="relative flex items-start">
                                        <div class="flex h-6 items-center">
                                            <input id="visibility" aria-describedby="visibility-description" wire:model="seo_visible" name="visibility" type="checkbox" class="h-4 w-4 rounded border-gray-300 text-keppel-600 focus:ring-keppel-600">
                                        </div>
                                        <div class="ml-3 text-sm leading-6">
                                            <label for="visibility" class="font-medium text-gray-900">Visible to search engines</label>
                                            <p id="visibility-description" class="text-gray-500">Set whether this category url is indexable and followable.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt-6 flex items-center justify-end gap-x-6">
                    <button type="button" wire:click="save" class="rounded-md bg-keppel-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-keppel-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-keppel-600">
                        Save
                    </button>
                </div>
            </div>
        </div>
    @elseif($tab === 'products')
        @php
            $products = $category->products;
        @endphp
        <div wire:key="products-tab" class="mt-4 bg-white shadow rounded-md">
            <div class="mx-auto max-w-2xl px-4 py-5 sm:p-6">
                <div class="sm:flex sm:items-center">
                    <div class="sm:flex-auto">
                        <h1 class="text-base font-semibold leading-6 text-gray-900">Products</h1>
                        <p class="mt-2 text-sm text-gray-700">A list of products assigned
                            to {{ $isSubcategory ? "{$category->parentCategory->name} > " : '' }} {{ $category->name }}. Sort them in the order they should
                            appear in the store.</p>
                    </div>
                </div>
                @if($products->isEmpty())
                    <div class="mt-8 flow-root text-center">
                        <svg class="mx-auto h-12 w-12 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none"
                             viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                  d="M9.879 7.519c1.171-1.025 3.071-1.025 4.242 0 1.172 1.025 1.172 2.687 0 3.712-.203.179-.43.326-.67.442-.745.361-1.45.999-1.45 1.827v.75M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9 5.25h.008v.008H12v-.008z"/>
                        </svg>
                        <h3 class="mt-2 text-sm font-semibold text-gray-900">No products</h3>
                    </div>
                @else
                    <div class="mt-2 flow-root">
                        <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                            <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                                <table class="min-w-full divide-y divide-gray-300">
                                    <thead>
                                    <tr>
                                        <th></th>
                                        <th scope="col"
                                            class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-0">
                                            Name
                                        </th>
                                    </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-200" wire:sortable="updateProductCategoryOrder">
                                    @foreach($products as $product)
                                        @php
                                            /** @var \App\Models\Product $product */
                                        @endphp
                                        <tr wire:sortable.item="{{ $product->id }}" wire:key="product-{{ $product->id }}">
                                            <td wire:sortable.handle>
                                                <span class="fa draghandle" title="Drag to reorder"></span>
                                            </td>
                                            <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-0">
                                                <a href="{{ route('admin.products.edit', [$product->id]) }}" class="text-keppel-600 hover:text-keppel-500">
                                                    {{ $product->title }}
                                                </a>
                                            </td>
                                        </tr>
                                    @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    @elseif($tab === 'subcategories' && !$isSubcategory)
        <div class="mt-4 bg-white shadow rounded-md">
            <div class="mx-auto max-w-2xl px-4 py-5 sm:p-6">
                <div class="sm:flex sm:items-center">
                    <div class="sm:flex-auto">
                        <h1 class="text-base font-semibold leading-6 text-gray-900">Subcategories</h1>
                        <p class="mt-2 text-sm text-gray-700">A list of subcategories under {{ $category->name }}. Sort them in the order they should appear in
                            the store.</p>
                    </div>
                    <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
                        <button type="button"
                                wire:click="$dispatch('open-modal-add-category', { category_id: {{ $category->id }} })"
                                class="block rounded-md bg-keppel-600 px-3 py-2 text-center text-sm font-semibold text-white shadow-sm hover:bg-keppel-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-keppel-600">
                            Add Subcategory
                        </button>
                    </div>
                </div>
                @if($subcategories->isEmpty())
                    <div class="mt-8 flow-root text-center">
                        <svg class="mx-auto h-12 w-12 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none"
                             viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                  d="M9.879 7.519c1.171-1.025 3.071-1.025 4.242 0 1.172 1.025 1.172 2.687 0 3.712-.203.179-.43.326-.67.442-.745.361-1.45.999-1.45 1.827v.75M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9 5.25h.008v.008H12v-.008z"/>
                        </svg>
                        <h3 class="mt-2 text-sm font-semibold text-gray-900">No Subcategories</h3>
                    </div>
                @else
                    <div class="mt-2 flow-root">
                        <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                            <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                                <table class="min-w-full divide-y divide-gray-300">
                                    <thead>
                                    <tr>
                                        <th></th>
                                        <th scope="col"
                                            class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-0">
                                            Name
                                        </th>
                                        <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pr-0">
                                            Slug
                                        </th>
                                    </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-200" wire:sortable="updateSubcategoryOrder">
                                    @foreach($subcategories as $subcategory)
                                        @php
                                            /** @var \App\Models\Category $subcategory */
                                        @endphp
                                        <tr wire:sortable.item="{{ $subcategory->id }}" wire:key="category-{{ $subcategory->id }}">
                                            <td wire:sortable.handle>
                                                <span class="fa draghandle" title="Drag to reorder"></span>
                                            </td>
                                            <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-0">
                                                <a href="{{ route('admin.categories.edit', [$subcategory->id]) }}" class="text-keppel-600 hover:text-keppel-500">
                                                    {{ $subcategory->name }}
                                                </a>
                                            </td>
                                            <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pr-0">
                                                {{ $subcategory->slug }}
                                            </td>
                                        </tr>
                                    @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    @endif
    <media-browser
            x-ref="mediaBrowser"
            wire:key="media-browser"
    ></media-browser>
</div>

@section('modals')
    <livewire:admin.modals.add-category/>
@endsection
