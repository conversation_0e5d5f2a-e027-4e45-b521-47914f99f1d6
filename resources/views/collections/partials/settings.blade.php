<div class="panel">
    <div class="panel-body">
        <form action="{{ route('admin.collections.update', $collection->id) }}" method="POST" id="updateCollectionForm">
            @csrf
            @method('put')
            <div class="form-group">
                <label for="title">Collection Name</label>
                <input type="text" name="title" value="{{ $collection->title }}" class="form-control">
            </div>

            <div class="form-group">
                <label for="title">Display Name</label>
                <input type="text" name="settings[display_name]" value="{{ $collection->settings->display_name ?? '' }}" class="form-control" placeholder="Will use collection name by default">
            </div>

            <div class="form-group">
                <label for="summary">Summary</label>
                <textarea name="settings[summary]" id="summary" class="form-control">{{ $collection->settings->summary  ?? '' }}</textarea>
            </div>

            <div class="form-group">
                <label for="title">Description <small>(supports HTML)</small></label>
                <textarea name="description" rows="10" class="wysiwyg form-control">{{ $collection->description }}</textarea>
            </div>

            <div class="form-group">
                <label for="title">Footer Description <small>(supports HTML)</small></label>
                <textarea name="footer_description" rows="10" class="wysiwyg form-control">{{ $collection->footer_description }}</textarea>
            </div>

            <div class="form-group">
                <label for="title">URL</label>
                <div class="input-group">
                    <span class="input-group-addon">{{ url('/store') }}</span>
                    <input type="text" name="slug" value="{{ $collection->slug }}" class="form-control">
                </div>
            </div>

            <hr>
            <div class="form-group">
                <label for="seo_visibility">Visible to Search Engines</label>
                <select class="form-control" name="seo_visibility">
                    <option value="1" {{ $collection->seo_visibility ? 'selected="selected"' : '' }}>Yes</option>
                    <option value="0" {{ $collection->seo_visibility ? '' : 'selected="selected"' }}>No</option>
                </select>
            </div>

            <div class="form-group">
                <label for="title">Page Meta Title</label>
                <input type="text" name="page_title" value="{{ $collection->page_title ?? '' }}" class="form-control">
            </div>

            <div class="form-group">
                <label for="page_description">Meta Description</label>
                <textarea name="page_description" rows="5" class="form-control" id="page_description">{{ $collection->page_description }}</textarea>
            </div>

            <div class="form-group">
                <label for="canonical_url">Canonical URL</label>
                <input type="text" name="canonical_url" value="{{ $collection->canonical_url ?? '' }}" class="form-control" id="canonical_url">
            </div>

            <div class="form-group">
                <label for="head_tags">Other Head Tags</label>
                <textarea 
                    class="form-control" 
                    name="head_tags" 
                    rows="5" 
                    placeholder="Use this for any extra meta tags you want in the header."
                >{{ $collection->head_tags }}</textarea>
            </div>
    
            <div class="form-group">
                <label for="body_tags">Body Scripts</label>
                <textarea 
                    class="form-control" 
                    name="body_tags" 
                    rows="5" 
                    placeholder="Use this for any script tags you want in the body."
                >{{ $collection->body_tags }}</textarea>
            </div>
        </form>
    </div>
</div>        
<div class="panel">
    <div class="panel-body">
        <label>Cover Photo</label>
        <cover-photo 
            url="/admin/collections/{{ $collection->id }}/photo" 
            src="{{ $collection->cover_photo }}" 
            field="photo_path" 
            message="Add Cover Photo"
        ></cover-photo>
    </div>
</div>        