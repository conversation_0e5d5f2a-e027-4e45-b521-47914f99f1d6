<!DOCTYPE HTML>
<html lang="en" class="antialiased h-full bg-gray-100">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <meta id="token" name="token" content="{{ csrf_token() }}">
    <title>{{ $page_title ?? 'GrazeCart | Ecommerce & Websites For Farms' }}</title>
    <meta name="keywords" content="direct marketing, small farms, grassfed, ecommerce, seven sons farms">
    <meta name="description" content="{{ $page_description ?? '' }}">
    <meta name="referrer" content="origin">
    @vite([
        'resources/assets/css/tailwind.css',
    ])
    <noscript id="deferred-styles">
        <link href="https://fonts.googleapis.com/css?family=Lato:400,700,900&display=swap" rel="stylesheet">
    </noscript>
    <style>
        body {
            font-family: 'Lato', sans-serif;
        }
    </style>

    @yield('header')

    @production
        <script src="https://script.tapfiliate.com/tapfiliate.js" type="text/javascript" async></script>
        <script type="text/javascript">
            (function(t, a, p) {
                t.TapfiliateObject = a;
                t[a] = t[a] || function() {
                    (t[a].q = t[a].q || []).push(arguments);
                };
            })(window, 'tap');

            tap('create', '21138-04bf07', { integration: 'stripe' });
            tap('detect');
        </script>
        <!-- Global site tag (gtag.js) - Google Ads: 762553735 -->
        <script async src="https://www.googletagmanager.com/gtag/js?id=AW-762553735"></script>
        <script>
            window.dataLayer = window.dataLayer || [];

            function gtag() {
                dataLayer.push(arguments);
            }

            gtag('js', new Date());

            gtag('config', 'AW-762553735');
        </script>
    @endproduction
</head>

<body class="h-full">
@production
    <noscript>
        <img height="1" width="1" style="display:none" alt="FB Pixel" src="https://www.facebook.com/tr?id=346903192832906&ev=PageView&noscript=1"/>
    </noscript>
@endproduction

<div class="flex flex-col h-full">
    <div class="flex-auto">
        @yield('content')
    </div>
</div>

@production
    <!-- Global site tag (gtag.js) - Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=UA-********-4"></script>
    <script>
        window.dataLayer = window.dataLayer || [];

        function gtag() {
            dataLayer.push(arguments);
        }

        gtag('js', new Date());

        gtag('config', 'UA-********-4', { 'optimize_id': 'GTM-52QGHCZ' });
    </script>

    <script>
        // Drip
        var _dcq = _dcq || [];
        var _dcs = _dcs || {};
        _dcs.account = '4770432';

        (function() {
            var dc = document.createElement('script');
            dc.type = 'text/javascript';
            dc.async = true;
            dc.src = '//tag.getdrip.com/4770432.js';
            var s = document.getElementsByTagName('script')[0];
            s.parentNode.insertBefore(dc, s);
        })();
        // End Drip
    </script>

    <script>

        // Facebook Pixel Code
        !function(f, b, e, v, n, t, s) {
            if (f.fbq) return;
            n = f.fbq = function() {
                n.callMethod ?
                    n.callMethod.apply(n, arguments) : n.queue.push(arguments);
            };
            if (!f._fbq) f._fbq = n;
            n.push = n;
            n.loaded = !0;
            n.version = '2.0';
            n.queue = [];
            t = b.createElement(e);
            t.async = !0;
            t.src = v;
            s = b.getElementsByTagName(e)[0];
            s.parentNode.insertBefore(t, s);
        }(window, document, 'script',
            'https://connect.facebook.net/en_US/fbevents.js');

        @if(!empty($fb_pixel_id ?? null))
        fbq('init', '346903192832906');
        fbq('track', 'PageView');
        @endif
        // End Facebook Pixel Code
    </script>
@endproduction

@yield('scripts')

</body>
</html>
