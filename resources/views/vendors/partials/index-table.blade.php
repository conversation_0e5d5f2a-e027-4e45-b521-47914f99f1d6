@if($vendors->count())
<div class="panel">
    <div class="panel-body pa-0">
        <div class="table-responsive">
            <table class="table table-striped table-full table-list">
                <thead>
                <tr>
                    <th>{!! sortTable('Name', 'title') !!}</th>
                    <th>{!! sortTable('Phone', 'phone') !!}</th>
                    <th>{!! sortTable('Email', 'email') !!}</th>
                    <th>{!! sortTable('Website', 'website') !!}</th>
                </tr>
                </thead>
                <tbody>
                @foreach($vendors as $vendor)
                    <tr>
                        <td>
                            <a href="{{ route('admin.vendors.edit', $vendor->id) }}">{{ $vendor->title }}</a>
                            <span class="label label-light _{!! (int) !$vendor->visible !!}">Hidden</span>
                        </td>
                        <td>{{ $vendor->phone }}</td>
                        <td>{{ $vendor->email }}</td>
                        <td><a href="{{ $vendor->website }}" target="_blank">{{ $vendor->website }}</a></td>
                    </tr>
                @endforeach
                </tbody>
            </table>
        </div>
    </div>
</div>
@else
    <div class="row row-medium">
        <div class="content">
            <div class="panel ">
                <div class="panel-heading">What are Vendors?</div>
                <div class="panel-body">
                    <p>Vendors are another way for you to organize products in your store.</p>

<p>If you sell products that another farm produces, you can create a vendor profile for that farm so your customers can learn more about where those products come from.</p>

<p>Once a vendor profile has been created you can assign any product to that vendor under the product's "Settings" tab.</p>
                </div>
                <div class="panel-footer text-right">
                    <button type="button" @click="showModal('createVendorModal')" class="btn btn-action">Create a Vendor</a>
                </div>
            </div>
        </div>
    </div>
@endif