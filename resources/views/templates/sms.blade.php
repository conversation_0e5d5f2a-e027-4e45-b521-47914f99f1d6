@extends('layouts.main', ['pageTitle' => 'SMS Templates'])

{{--Toolbar--}}
@section('toolbar-breadcrumb')
    <li>SMS Templates</li>
@stop
@section('toolbar-buttons')
    <a href="#" @click="showModal('createTemplateModal')" class="btn btn-success">New Template</a>
@stop

@section('content')
    {{--Partials--}}
    @include('templates.partials.create-sms-template-modal')

    <div class="panel panel-body">
        Visit <a href="/admin/settings/notifications">Settings / Email &amp; Notifications</a> to assign which email templates get used by default.
    </div>

    <ul class="nav">
        <li role="presentation" class="{{ activeTab('email', Request::get('tab', 'email')) }}">
            <a href="/admin/templates?tab=email">Email</a>
        </li>
        <li role="presentation" class="{{ activeTab('sms', Request::get('tab')) }}">
            <a href="/admin/templates?tab=sms">SMS</a>
        </li>
    </ul>
    @include('templates.partials.sms-index-table')
@stop()

@section('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            $('.merge-tags-list li').on('click', function() {
                $('textarea[name="plain_text"]').insertAtCaret($(this).text());
            });
        });
    </script>
@stop()
