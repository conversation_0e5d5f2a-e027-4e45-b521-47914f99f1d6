@extends('layouts.login')

@section('content')
    <div class="flex min-h-full flex-col justify-center px-6 py-12 lg:px-8">
        <div class="sm:mx-auto sm:w-full sm:max-w-sm">
            <img class="mx-auto h-16 w-auto" src="{{ asset('images/seven-sons-logo.png') }}" alt="Seven Sons Farm">
        </div>

        <div class="mt-10 sm:mx-auto sm:w-full sm:max-w-sm">
            <form class="space-y-6" action="/admin/password/reset" method="POST">
                @csrf
                @if(Session::has('flash_notification.message'))
                    <div class="rounded-md bg-blue-50 p-4">
                        <div class="flex">
                            <div>
                                <h3 class="text-sm font-medium text-blue-800">{!! Session::get('flash_notification.message') !!}</h3>
                                <div class="mt-2 text-sm text-blue-700">
                                    @if($errors->count())
                                        <ul role="list" class="list-disc space-y-1 pl-5">
                                            @foreach($errors->all() as $error)
                                                <li>{!! $error !!}</li>
                                            @endforeach
                                        </ul>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                @endif

                <input type="hidden" name="token" value="{{ $token }}">
                <div>
                    <label for="email" class="block text-sm/6 font-medium text-white">Email address</label>
                    <div class="mt-2">
                        <input type="email" name="email" id="email" autocomplete="email" value="{{ old('email', request('email')) }}" required class="block w-full rounded-md bg-white/5 px-3 py-1.5 text-base text-white outline outline-1 -outline-offset-1 outline-white/10 placeholder:text-gray-500 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-keppel-500 sm:text-sm/6">
                    </div>
                </div>
                <div>
                    <label for="password" class="block text-sm/6 font-medium text-white">Password</label>
                    <div class="mt-2">
                        <input type="password" name="password" id="password" autocomplete="" required class="block w-full rounded-md bg-white/5 px-3 py-1.5 text-base text-white outline outline-1 -outline-offset-1 outline-white/10 placeholder:text-gray-500 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-keppel-500 sm:text-sm/6">
                    </div>
                </div>

                <div>
                    <div class="flex items-center justify-between">
                        <label for="password_confirmation" class="block text-sm/6 font-medium text-white">Confirm Password</label>
                    </div>
                    <div class="mt-2">
                        <input type="password" name="password_confirmation" id="password_confirmation" required class="block w-full rounded-md bg-white/5 px-3 py-1.5 text-base text-white outline outline-1 -outline-offset-1 outline-white/10 placeholder:text-gray-500 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-keppel-500 sm:text-sm/6">
                    </div>
                </div>

                <div>
                    <button type="submit" class="flex w-full justify-center rounded-md bg-keppel-500 px-3 py-1.5 text-sm/6 font-semibold text-white shadow-sm hover:bg-keppel-400 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-keppel-500">Reset Password</button>
                </div>
            </form>
        </div>
    </div>

@stop
