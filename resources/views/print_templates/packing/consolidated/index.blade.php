@extends('print_templates.layout')

@php
    /** @var \App\Services\BarcodeService $barcode_service */
    $barcode_service = app(\App\Services\BarcodeService::class);
@endphp

@section('content')
    @foreach($orders as $order)

        @php /** @var \App\Models\Order $order */ @endphp

                <!-- Start Page -->
        <div class="pageContainer">
            <div class="pageInnerContainer">
                <!-- Start Details Container -->
                <div class="detailsContainer">

                    <div class="detailsInnerContainer">
                        <div class="barcodeContainer">
                            {!! $barcode_service->generateAsSvg($order->id) !!}
                        </div>
                        <div class="pageHeading">
                            Order #{{ $order->id }}
                        </div>

                        <ul class="customerOrderDetailsList">
                            <li>Total orders: <strong>{{ $order->customer_orders_count }}</strong></li>
                        </ul>


                        <div class="customerName">
                            {{ $order->customer_first_name }} {{ $order->customer_last_name }}
                        </div>

                        <ul class="customerDetailsList">
                            {!! $order->first_time_order ? '<li>&#9733; New Customer &#9733;</li>' : '' !!}
                            {!! $order->flagged ? '<li>&#9873; Flagged</li>' : '' !!}
                            <li>{{ $order->customer_phone }}</li>
                            <li>{{ $order->customer_email }}</li>
                            <li>
                                <div>{{ $order->shipping_street }} {{ $order->shipping_street_2 }}</div>
                                <div>{{ $order->shipping_city }}, {{ $order->shipping_state }} {{ $order->shipping_zip }}</div>
                            </li>
                        </ul>


                        <div class="fulfillmentLocation">
                            {{ $order->present()->pickupTitle() }}
                            <div class="scheduleTitle">{{ $order->present()->scheduleTitle() }}</div>
                        </div>
                        <ul class="customerDetailsList">
                            <li>{{ $order->present()->pickupDate() }}</li>
                        </ul>

                        <div class="parcelsContainer">
                            <div style="margin-bottom: 1rem;">
                                Weight: <strong>{{ $order->weight }}</strong> lb.
                            </div>
                            <div class="parcels">
                                Parcels: {{ $order->present()->totalContainers() ? $order->present()->totalContainers() : '__________'}}
                            </div>
                            <div class="packedBy">
                                Packed by:
                            </div>
                        </div>

                        <!-- Start Order Notes -->
                        @if($order->isGift() && ! empty($order->recipient_notes))
                            <div class="giftNotes">
                                <div>
                                    <span style="filter: grayscale(100%);">&#127873;</span> GIFT MESSAGE <span style="filter: grayscale(100%);">&#127873;</span>
                                </div>

                                {{ $order->recipient_notes }}
                            </div>
                        @endif

                        <!-- Start Order Notes -->
                        @if($order->customer_notes)
                            <div class="customerNotes">
                                <div>Customer Notes:</div>
                                {{ $order->customer_notes }}
                            </div>
                        @endif
                        @if($order->packing_notes)
                            <div class="packingNotes">
                                <div>Packing Notes:</div>
                                {{ $order->packing_notes }}
                            </div>
                        @endif
                        <!-- End Order Notes -->
                    </div>
                </div>
                <!-- End Details Container -->


                <!-- Start Order Container -->
                <div class="orderContainer">
                    <div class="orderItemsContainer">
                        <table>
                            <thead>
                            <tr>
                                <th colspan="100%">
                                    <div style="display: flex; align-items: center; margin-bottom: 1.5rem; border: 1px solid #000; padding: 0.5rem; margin-top: 0.5rem; margin-right: 0.5rem;">

                                        <svg style="height: 48px; width: 48px;" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z"/>
                                        </svg>

                                        <div style="margin-left: 0.75rem; font-size: 14px; text-align: left; font-weight: normal;">
                                            Every order is backed by our guarantee! If you find any issues or you have questions, please call/text us at
                                            <strong>(877) 620-1977</strong> or email us at <strong><EMAIL></strong> — we’re always happy to help!
                                        </div>
                                    </div>
                                </th>
                            </tr>
                            </thead>
                        </table>
                        <!-- Start Order Items -->
                        @foreach($order->consolidatedItems->groupBy('inventory_type') as $key => $itemGroup)

                            <table class="orderItemsTable {{ $itemGroup->count() >= 15 ? 'fullPage' : 'sharePage' }}">
                                <thead class="packingGroupHeader">
                                <tr>
                                    <th colspan="100%">{{ packingGroup($key) }}</th>
                                </tr>
                                </thead>
                                <tbody>
                                @foreach($itemGroup->groupBy('consolidated_product_id')  as $consolidated_product_id => $consolidatedItems)
                                    @php
                                        $item = $consolidatedItems->first();
                                        $item->consolidated_qty = $consolidatedItems->sum('consolidated_qty');
                                        $item->consolidated_fulfilled_qty = $consolidatedItems->sum('consolidated_fulfilled_qty');
                                        $item->stock_status = $item->consolidated_qty < $item->consolidated_fulfilled_qty
                                            ? ($item->consolidated_fulfilled_qty === 0 ? 'out' : 'short')
                                            : 'full';
                                    @endphp
                                    {{--                                    {{ dump($item) }}--}}
                                    @include('print_templates.packing.consolidated._items')
                                @endforeach
                                </tbody>
                            </table>
                        @endforeach
                        <!-- End Order Items -->
                    </div>
                </div>
                <!-- End Order Container -->
            </div>
            <div class="orderFooter">
                <div class="orderFooter_divider">
                    <hr>
                </div>
                <div class="orderFooter_message">END OF ORDER #{{ $order->id }}</div>
                <div class="orderFooter_divider">
                    <hr>
                </div>
            </div>
        </div>
    @endforeach
@stop
