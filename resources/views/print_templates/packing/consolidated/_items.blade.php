@php
    /** @var \App\Models\OrderItem $item */
@endphp
<tr>
    <td class="skuColumn">
        <div class="sku">{{ $item->sku }}</div>
    </td>
    <td class="weightColumn">
        @if($item->isPricedByWeight())
            <div class="weightBox">{{ $item->weight }} est.</div>
        @else
            <div class="checkBox"></div>
        @endif
    </td>
    <td>
        <div class="qtyContainer">
            <div class="no-break" style="margin-right: 8px;">
                @if($item->consolidated_fulfilled_qty < $item->consolidated_qty)
                    <span class="quantity">( {{ $item->consolidated_fulfilled_qty }}</span>
                    <span style="font-size: 1.0rem; margin-left: 2px; margin-right: 2px;">of</span>
                    <span class="quantity">{{ $item->consolidated_qty }} )</span>
                @else
                    <span class="quantity">( {{ $item->consolidated_qty }} )</span>
                @endif
            </div>
            <div class="itemName">
                <div class="fulfillmentInstructions" @if($item->stock_status === 'out') class="strike-through" @endif>
                    {{ $item->fulfillment_instructions ?? $item->title }}
                </div>
            </div>
        </div>
    </td>
</tr>

