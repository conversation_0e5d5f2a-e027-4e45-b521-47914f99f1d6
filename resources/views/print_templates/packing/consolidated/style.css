body {
    margin: 0;
    padding: 0;
    font-family: "Helvetica Neue", Helvetica, Roboto, Arial, sans-serif;
    font-size: 12px;
}

.no-break {
    white-space: nowrap;
}

.packing-list {
    page-break-after: always;
    break-after: always;
}

.pageContainer {
    width: 100%;
    page-break-after: always;
}

.pageInnerContainer {
    display: grid;
    grid-auto-flow: row;
    grid-template-columns: 215px 1fr;
    grid-gap: 1rem;
}

.detailsContainer {
    height: 100%;
    border-right: solid 1px #000;
}

.barcodeContainer {
    padding-right: 0.5rem;
    padding-bottom: 0.5rem;
}

.pageHeading {
    font-size: 1.25rem;
    font-weight: bolder;
    margin-bottom: 1rem;
}

.parcels {
    margin-bottom: 1rem;
    font-weight: bolder;
    font-size: 1rem;
}

.quantity {
    font-size: 1.75rem;
    font-weight: bolder;
}

.itemName {
    display: flex;
    align-items: flex-start;
    font-size: 11px;
    color: #444;
}

.fulfillmentInstructions {
    font-size: 16px;
    font-weight: bold;
    margin: 2px 0 0 0;
    margin-left: 1px;
    color: #000;
}

.packingGroupHeader tr th {
    font-weight: bolder;
    font-size: 1rem;
    text-align: left;
    margin: 0 auto;
    padding: 0 0 0.25rem 0;
    border-bottom: solid 4px #000;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.customerName, .fulfillmentLocation {
    font-weight: bolder;
    font-size: 1.25rem;
    margin-bottom: 0.25rem;
}

.scheduleTitle {
    font-size: 0.75rem !important;
    color: #6d6d6d;
}

.customerDetailsList {
    list-style-type: none;
    padding: 0;
    margin: 0;
    margin-bottom: 1rem;
}

.customerDetailsList li {
    margin-bottom: 0.10rem;
}

.customerOrderDetailsList {
    list-style-type: none;
    padding: 0;
    margin: 0;
    margin-bottom: 1rem;
    font-size: 0.875rem;
}


.sortColumn {
    font-size: 1.25rem;
    font-weight: bolder;
    margin: -5px 0.1rem 0 0;
}

.weightColumn {
    width: 100px;
    padding-right: 1rem !important;
}

.weightBox, .checkBox {
    border-radius: 2px;
    height: 34px;
    border: solid 1px #000;
}

.weightBox {
    padding: 0 6px;
    width: auto;
    line-height: 34px;
    color: #f2f2f2;
    text-align: right;
}

.checkBox {
    width: 34px;
    height: 34px;
    margin-left: auto;:
}

.qtyContainer {
    display: flex;
    align-items: center;
}


.orderFooter {
    display: flex;
}

.orderFooter_divider {
    flex: 1 0 auto;
}

.orderFooter_divider hr {
    /*height: 2px;*/
    border: none;
    border-top: dashed 2px #000;
}


.orderFooter_message {
    flex: 0 1 auto;
    margin: 0 1rem;
    letter-spacing: 1px;
}


table tr {
    page-break-inside: avoid;
}

.orderItemsTable {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 2rem;
    page-break-inside: always;
    border-bottom: solid 4px #000;
}

.fullPage {
    page-break-after: always !important;
}

.orderItemsTable tbody td {
    padding: 0.5rem 0;
    border-top: solid 1px #000;
    /*border-right: solid 2px #ddd;*/
    page-break-inside: avoid;
}

.weightColumn {
    border-right: none !important;
}

.giftNotes {
    border: 3px double gray;
    padding: 8px;
    margin-right: 8px;
}

.packedBy, .giftNotes, .customerNotes, .packingNotes {
    margin-bottom: 2rem;
    padding-right: 0.5rem;
    overflow-wrap: break-word;
    word-wrap: break-word;
    hyphens: auto;
}

.packedBy {
    margin-bottom: 3rem;
}

.skuColumn {
    font-size: 16px;
    font-weight: bolder;
    margin: -5px 0.1rem 0 0;
    width: 10%;
}

.packedBy div, .giftNotes div, .customerNotes div, .packingNotes div {
    font-weight: bolder;
}

.parcelsContainer {
    margin-top: 2.5rem;
}

.mb-1 {
    margin-bottom: 1rem !important;
}

.bundle_1 {
    font-weight: bolder;
    padding-right: 0.25rem;
}

.bundle_0 {
    display: none;
}

.fulfillmentLocation {
    font-weight: bolder;
}

@page {
    size: letter;
    margin: 10% !important;

    @top-right-corner {
        content: "Page " counter(page);
    }
}

.strike-through {
    text-decoration: line-through;
}
