@extends('print_templates.layout')

@section('content')
    @php
        $fresh_dry_packing_group_ids = collect([
            \App\Models\PackingGroup::isFresh(),
            \App\Models\PackingGroup::dry()
        ])->filter()->toArray();
    @endphp
    @foreach($orders as $order)
        @php /** @var \App\Models\Order $order */ @endphp

        <div class="label-container">
            <div class="order-number">
                #{{ $order->id }}
            </div>
            <div class="customer-name">
                {{ $order->customer_first_name }} {{ $order->customer_last_name }}
            </div>
            <div class="pickup-location">
                {{ $order->present()->pickupTitle() }}
            </div>
            <div class="box-count">
                @if($order->containers)
                    FRZ: {{ $order->containers }}
                @else
                    FRZ: ____
                @endif
            </div>
            <div class="box-count align-right">
                @if($order->containsPackingGroups($fresh_dry_packing_group_ids))
                    @if($order->containers_2)
                        FRESH: {{ $order->containers_2 }}
                    @else
                        FRESH: YES
                    @endif
                @endif
            </div>
        </div>
    @endforeach
@stop
