body {
    margin: 0px;
    padding: 0px;
    font-family: Trebu<PERSON> MS,Lucida Grande,Lucida Sans Unicode,Lucida Sans,Tahoma,sans-serif; 
    font-size: 16px;
    color: #3d3d3d;
}

header {
    padding: 0.50cm 0.75cm 0.25cm 0.75cm;
    width: 100%;
    background-color: #fafafa;
    border-bottom: solid 1px #f3f3f3;
    box-sizing: border-box;
}

main {
    padding: 0 0.75cm;
}

#customer {
    padding: 0.25cm 0;
}

.invoice {
    page-break-after: always;
    break-after: always;
}

.company-details {
    font-size: 14px;
}

.invoice-details {
    width: auto !important;
    margin-left: auto;
}

.invoice-details tr td:first-of-type {
    padding-right: 5px;
}

.text-right {
    text-align: right;
}

.text-center {
    text-align: center;
}

table {
    width: 100%;
    border-collapse: collapse;
}

a[href]::after {
    content: none;
}

h1,h2,h3,h4,h5,h6,th,strong {
    font-family: '<PERSON><PERSON><PERSON>', sans-serif;
    font-weight: bold;
    margin: 0;
    padding: 0;
}

h1 {
    font-size: 40px;
    font-weight: normal;
}

h3 {
    margin: 0 0 5px 0;
}

ul {
    list-style-type: none;
    margin: 0px;
    padding: 0px;
}

th {
    text-transform: uppercase;
}

.customer-details ul {
    list-style-type: none;
    margin: 0px;
    padding: 0px;
}

#items {
    margin: 1em 0px 0.5em 0px;
}

#items table {
    width: 100%;
    text-align: left;
    margin-bottom: 0.5em;
    border-collapse: collapse;
}

#items table thead tr th {
    padding: 3px;
    font-weight: bold;
    border: solid 1px #ddd;
    background-color: #eee;
    font-size: 0.9em;
}

#items table tbody tr td {
    border: solid 1px #ddd;
    padding: 4px;
}

#items table tbody tr td:last-of-type {
    text-align: right;
}

.totals {
    margin-top: 20px;
    display: inline-block;
    text-align: right;
}

.totalsTable {
    width: 250px;
    margin-left: auto;
    margin-right: 0;
}

.totalsTable tr {
    border-bottom: solid 1px #eee;
}

.totalsTable tr td {
    padding: 6px 6px;
}

.totalsTable tr td:first-of-type {
    text-align: left;
}

.totalsTable tr td:last-of-type {
    text-align: right;
}

.final-total {
    font-weight: bold;
    font-size: 1.20em;
    background-color: #eee;
    border-bottom: none;
}

.text-right {
    text-align: right;
}

.notes {
    max-width: 75%;
}

.signature {
    font-family: 'Great Vibes', cursive;
    font-size: 30px;
}