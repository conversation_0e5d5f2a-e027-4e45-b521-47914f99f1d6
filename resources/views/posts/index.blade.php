@extends('layouts.main', ['pageTitle' => 'Posts'])

@section('content')
    {{--Partials--}}
    @include('posts.partials.create-post-modal')

    {{--Toolbar--}}
    @section('toolbar-breadcrumb')
        <li>Blog Posts ({{ $posts->total() }})</li>
    @stop
    @section('toolbar-buttons')
        <button type="button" class="btn btn-success" @click="showModal('createPostModal')">New Post</button>
    @stop

    <div class="row">
        <div class="content">
            {{--Table of posts--}}
            @include('posts.partials.index-table')
        </div>

        <div class="sidebar sidebar-1 sidebar-right">
            {{--Sidebar with filter--}}
            @include('posts.partials.filter-form')
        </div>
    </div>
@stop()

@section('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            $('#publishDate').daterangepicker({
                singleDatePicker: false,
                opens: 'left',
                showDropdowns: true,
                locale: {
                    format: 'YYYY-MM-DD',
                    cancelLabel: 'Clear'
                }
            });

            $('#publishDate').val('{{ Request::get('published_date') }}');

            $('#publishDate').on('cancel.daterangepicker', function(ev, picker) {
                //do something, like clearing an input
                $('#publishDate').val('');
            });
        });
    </script>
@stop
