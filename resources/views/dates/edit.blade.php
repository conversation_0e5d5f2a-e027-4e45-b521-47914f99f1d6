@extends('layouts.main', ['pageTitle' => $date->schedule->title])

@section('toolbar-breadcrumb')
    <li><a href="/admin/schedules">Schedules</a></li>
    <li><a href="/admin/schedules/{{ $date->schedule->id }}/edit">{{ $date->schedule->title }}</a></li>
    <li>{{ $date->pickup_date->format('M jS, Y') }}</li>
@stop
@section('toolbar-buttons')
    <button class="btn btn-light mr-sm" @click="submitForm('updateResourceForm')">Save</button>
    <div class="dropdown">
        <button type="button" class="btn btn-light dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
            <i class="fas fa-gear"></i> Actions <i class="fas fa-caret-down"></i>
        </button>
        <ul class="dropdown-menu pull-right">
            <li><a href="#" @click="showModal('deleteDateModal')">Delete</a></li>
        </ul>
    </div>
@stop

@section('content')
    @include('dates.partials.delete-date-modal')
    <form action="/admin/schedules/{{ $date->schedule->id}}/dates/{{ $date->id }}" method="POST" id="updateResourceForm">
        @csrf
        @method('PUT')
        <div class="row">
            <div class="content">
                <div class="panel panel-tab">
                    <div class="panel-body">
                        {{--Pickup_date--}}
                        <div class="form-group">
                            <label for="pickup_date">Pickup Date</label>
                            <input type="text" name="pickup_date" class="form-control pickup-date" value="{{ $date->pickup_date }}" />
                        </div>

                        {{--Order_window--}}
                        <div class="form-group">
                            <label for="order_window">Order Window</label>
                            <input type="text" name="order_window" class="form-control order-window" value="{{ $date->order_start_date->format('Y-m-d g:i A') }} - {{ $date->order_end_date->format('Y-m-d g:i A') }}" />
                        </div>
                    </div>
                </div>

                <div class="panel  panel-tab">
                    <div class="panel-heading">Notes</div>
                    <div class="panel-body">
                        <textarea name="notes" class="form-control" rows="10">{{ $date->notes }}</textarea>
                    </div>
                </div>
            </div>

            <div class="sidebar sidebar-1 sidebar-right">
                <div class="panel ">
                    <div class="panel-heading">Delivery Driver</div>
                    <div class="panel-body">
                        <x-form.staff-select
                                class="form-control"
                                name="user_id"
                                placeholder="N/A"
                                :selected="(int) $date->user_id"
                        />
                    </div>
                </div>

                <div class="panel ">
                    <div class="panel-heading">Status</div>
                    <div class="panel-body">
                        @if($date->active)
                            <div class="radio">
                                <label class="mr-sm">
                                    <input type="radio" name="active" id="active" value="1" checked> Active
                                </label>
                                <label>
                                    <input type="radio" name="active" id="inactive" value="0"> Inactive
                                </label>
                            </div>
                        @else
                            <div class="radio">
                                <label class="mr-sm">
                                    <input type="radio" name="active" id="active" value="1"> Active
                                </label>
                                <label>
                                    <input type="radio" name="active" id="inactive" value="0" checked> Inactive
                                </label>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </form>
@stop

@section('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            $('.pickup-date').daterangepicker({
                singleDatePicker: true,
                showDropdowns: true,
                locale: {
                    format: 'YYYY-MM-DD'
                }
            });

            $('.order-window').daterangepicker({
                timePicker: false,
                timePickerIncrement: 30,
                locale: {
                    format: 'YYYY-MM-DD'
                }
            });
        });
    </script>
@stop

