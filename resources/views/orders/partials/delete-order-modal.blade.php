<div class="gc-modal gc-modal-mask" id="deleteOrderModal" @click="hideModal('deleteOrderModal')">
    <div class="gc-modal-wrapper">
        <div class="gc-modal-container" @click.stop>
            <form action="/admin/orders/{{ $order->id }}" method="POST">
                @csrf
                @method('DELETE')
                <div class="gc-modal-header">
                    Delete Order
                </div>

                <div class="gc-modal-body">
                    Are you sure you want to delete this order? This action cannot be undone and may affect your inventory & reporting.
                </div>

                <div class="gc-modal-footer">
                    <button type="button" class="btn btn-alt" @click="hideModal('deleteOrderModal')">Cancel</button>
                    <button type="submit" class="btn btn-danger">Delete Order</button>
                </div>
            </form>
        </div>
    </div>
</div>

<div class="gc-modal gc-modal-mask" id="cancelOrderModal" @click="hideModal('cancelOrderModal')">
    <div class="gc-modal-wrapper">
        <div class="gc-modal-container" @click.stop>
            <form action="/admin/orders/{{ $order->id }}/canceled" method="POST" id="cancelOrderForm">
                @csrf
                <input type="hidden" name="status_id" value="{{ \App\Support\Enums\OrderStatus::canceled() }}">
                <div class="gc-modal-header">
                    Cancel Order
                </div>

                <div class="gc-modal-body">
                    Are you sure you want to cancel this order? This action will move the order to the canceled status and issue the canceled order email to the
                    customer.
                </div>

                <div class="gc-modal-footer">
                    <button type="button" class="btn btn-alt" @click="hideModal('cancelOrderModal')">Cancel</button>
                    <button type="submit" class="btn btn-danger" @click="submitForm('cancelOrderForm')">Cancel Order</button>
                </div>
            </form>
        </div>
    </div>
</div>
