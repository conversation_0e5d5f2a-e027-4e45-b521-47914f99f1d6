<div class="gc-modal gc-modal-mask" id="contactInfoModal" @click="hideModal('contactInfoModal')">
    <div class="gc-modal-wrapper">
        <div class="gc-modal-container" @click.stop>
            <form action="/admin/orders/{{ $order->id }}" method="POST">
                @csrf
                @method('PUT')
                <div class="gc-modal-header">
                    Order Contact Info
                </div>

                <div class="gc-modal-body">
                    {{--Customer_first_name--}}
                    <div class="form-group">
                        <label for="customer_first_name">Customer First Name</label>
                        <input type="text" name="customer_first_name" class="form-control" value="{{ old('customer_first_name', $order->customer_first_name) }}" />
                    </div>

                    {{--Customer_last_name--}}
                    <div class="form-group">
                        <label for="customer_last_name">Customer Last Name</label>
                        <input type="text" name="customer_last_name" class="form-control" value="{{ old('customer_last_name', $order->customer_last_name) }}" />
                    </div>

                    {{--Customer_phone--}}
                    <div class="form-group">
                        <label for="customer_phone">Customer Phone</label>
                        <input type="text" name="customer_phone" class="form-control" value="{{ old('customer_phone', $order->customer_phone) }}" />
                    </div>

                    {{--Customer_email--}}
                    <div class="form-group">
                        <label for="customer_email">Customer Email</label>
                        <input type="text" name="customer_email" class="form-control" value="{{ old('customer_email', $order->customer_email) }}" />
                    </div>

                    <div class="form-group">
                        <label for="customer_email_alt">Customer Email Alternative</label>
                        <input type="text" name="customer_email_alt" class="form-control" value="{{ old('customer_email_alt', $order->customer_email_alt) }}" />
                    </div>

                    <div class="section">
                        <div class="section-heading">
                            <span>Shipping Address</span>
                        </div>
                        <div class="section-body">
                            {{--Street--}}
                            <div class="form-group">
                                <label for="shipping_street">Street:</label>
                                <input type="text" name="shipping_street" class="form-control" value="{{ old('shipping_street', $order->shipping_street) }}" id="street" />
                            </div>

                            {{--Street_2--}}
                            <div class="form-group">
                                <label for="shipping_street_2">Street Line 2:</label>
                                <input type="text" name="shipping_street_2" class="form-control" value="{{ old('shipping_street_2', $order->shipping_street_2) }}" id="street_2" />
                            </div>

                            {{--City--}}
                            <div class="form-group">
                                <label for="shipping_city">City:</label>
                                <input type="text" name="shipping_city" class="form-control" value="{{ old('shipping_city', $order->shipping_city) }}" id="city" />
                            </div>

                            {{--State--}}
                            <div class="form-group">
                                <label for="shipping_state">State:</label>
                                <x-form.state-select
                                        class="form-control"
                                        name="shipping_state"
                                        id="state"
                                        :selected="$order->shipping_state"
                                />
                            </div>

                            {{--Zip--}}
                            <div class="form-group">
                                <label for="shipping_zip">Zip:</label>
                                <input type="text" name="shipping_zip" class="form-control" value="{{ old('shipping_zip', $order->shipping_zip) }}" id="zip" />
                            </div>
                        </div>
                    </div>

                    <div class="section">
                        <div class="section-heading">
                            <span>Billing Address</span>
                        </div>
                        <div class="section-body">
                            <div class="checkbox mb-sm">
                                <label>
                                    <input type="checkbox" id="billingSameAsShipping"> Billing is the same as above
                                </label>
                            </div>
                            {{--Billing_street--}}
                            <div class="form-group">
                                <label for="billing_street">Billing Street:</label>
                                <input type="text" name="billing_street" class="form-control" value="{{ old('billing_street', $order->billing_street) }}" id="billing_street" />
                            </div>

                            {{--Billing_street_2--}}
                            <div class="form-group">
                                <label for="billing_street_2">Billing Street Line 2:</label>
                                <input type="text" name="billing_street_2" class="form-control" value="{{ old('billing_street_2', $order->billing_street_2) }}" id="billing_street_2" />
                            </div>

                            {{--Billing_city--}}
                            <div class="form-group">
                                <label for="billing_city">Billing City:</label>
                                <input type="text" name="billing_city" class="form-control" value="{{ old('billing_city', $order->billing_city) }}" id="billing_city" />
                            </div>

                            {{--Billing_state--}}
                            <div class="form-group">
                                <label for="billing_state">Billing State:</label>
                                <x-form.state-select
                                        class="form-control"
                                        name="billing_state"
                                        id="billing_state"
                                        :selected="$order->billing_state"
                                />
                            </div>

                            {{--Billing_zip--}}
                            <div class="form-group">
                                <label for="billing_zip">Billing Zip:</label>
                                <input type="text" name="billing_zip" class="form-control" value="{{ old('billing_zip', $order->billing_zip) }}" id="billing_zip" />
                            </div>
                        </div>
                    </div>

                    <div class="section">
                        <div class="section-heading">
                            <span>Gift Order Details</span>
                        </div>
                        <div class="section-body">
                            {{--Gift Recipient Email--}}
                            <div class="form-group">
                                <label for="recipient_email">Recipient Email:</label>
                                <input type="text" name="recipient_email" class="form-control" value="{{ old('recipient_email', $order->recipient_email) }}" id="recipient_email" />
                            </div>

                            {{--Gift Recipient Notes--}}
                            <div class="form-group">
                                <label for="recipient_notes">Recipient Notes:</label>
                                <textarea name="recipient_notes" class="form-control" rows="4">{{ old('recipient_notes', $order->recipient_notes) }}</textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="gc-modal-footer">
                    <button type="button" class="btn btn-alt" @click="hideModal('contactInfoModal')">Cancel</button>
                    <button type="submit" class="btn btn-action">Save</button>
                </div>
            </form>
        </div>
    </div>
</div>
