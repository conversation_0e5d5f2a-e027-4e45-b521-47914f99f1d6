<div class="panel panel-tabs">
    <div class="panel-body pa-0">
        <table class="table table-striped table-full table-list">
            <thead>
            <tr>
                <th>Name</th>
                <th>Date</th>
                <th>Email</th>
                <th>Order Count</th>
            </tr>
            </thead>
            <tbody>
            @foreach($referrals as $referral)
                <tr>
                    <td data-label="Name"><a href="{{ route('admin.users.edit', $referral->id) }}">{{ $referral->present()->firstName().' '.$referral->last_name }}</a></td>
                    <td data-label="Date">{{ $referral->created_at->format('m/d/y') }}</td>
                    <td data-label="Email">{{ $referral->email }}</td>
                    <td data-label="Order Count">{{ $referral->orders_count }}</td>
                </tr>
            @endforeach
            </tbody>
        </table>
    </div>
</div>            
<div class="text-center">
    <p class="fs-1">Showing {{ $referrals->count() }} of {{ $referrals->total() }} result(s)</p>
    {!! $referrals->appends(request()->all())->render() !!}</div>