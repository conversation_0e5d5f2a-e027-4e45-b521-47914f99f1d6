@extends('layouts.main', ['pageTitle' => $tag->title])

@section('toolbar-breadcrumb')
    <li><a href="{{ route('admin.tags.index') }}">Tags</a></li>
    <li>{{ $tag->title }}</li>
@stop
@section('toolbar-buttons')
    <button class="btn btn-success flex-item mr-sm" @click="submitForm('updateResourceForm')">Save</button>

    <div class="dropdown">
        <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
            <i class="fas fa-gear"></i> Actions <i class="fas fa-caret-down"></i>
        </button>
        <ul class="dropdown-menu pull-right">
            <li><a href="#" @click="showModal('deleteTagModal')">Delete</a></li>
        </ul>
    </div>
@stop

@section('content')
    {{--Partials--}}
    @include('tags.partials.delete-tag-modal')
    <div class="panel">
        <div class="panel-body">
            @include('tags.partials.edit-form')
        </div>
        <div class="panel-footer text-right">
            <button class="btn btn-action" @click="submitForm('updateResourceForm')">Save</button>
        </div>
    </div>
@stop
