<div class="filterPanel" id="filterPanel">
    <div class="settingsPanel pa-0">
        <div class="settingsPanel__header flex align-items-m">
            <div class="flex-item-fill">Filter Pickups</div>
            <button
                    type="button"
                    class="btn btn-alt flex-item"
                    @click="hidePanel('filterPanel')"
            ><i class="fas fa-times fa-lg"></i></button>
        </div>
        <div class="settingsPanel__body">
            {{--Schedule--}}
            <div class="form-group">
                <label for="schedule_id">Schedule</label>
                <x-form.schedule-select
                        class="form-control autofocus"
                        name="schedule_id"
                        placeholder="All"
                        :selected="request('schedule_id')"
                        :only-active="true"
                />
            </div>

            {{--Status--}}
            <div class="form-group">
                <label for="location_status">Status</label>
                <x-form.delivery-method-status-select
                        class="form-control"
                        name="location_status"
                        placeholder="All"
                        :selected="request('location_status')"
                />
            </div>

            <div class="form-group">
                <label for="state">State</label>
                <x-form.state-select
                        class="form-control"
                        name="state"
                        placeholder="All"
                        :selected="request('state')"
                />
            </div>

            <div class="form-group">
                <label for="location_visibility">Visibility</label>
                <x-form.visibility-select
                        class="form-control"
                        name="location_visibility"
                        placeholder="All"
                        :selected="request('location_visibility')"
                />
            </div>
        </div>
        <div class="settingsPanel__footer flex align-items-m">
            <button type="submit" class="btn btn-action btn-block btn-lg" @click="submitForm('filterLocationsForm')">Filter</button>
        </div>
    </div>
</div>
