@extends('layouts.main', ['pageTitle' => 'Subscription #'.$subscription->id])

@php /** @var App\Models\RecurringOrder $subscription */ @endphp

@section('toolbar-breadcrumb')
    <li><a href="/admin/subscriptions">Subscriptions</a></li>
    <li>{{ 'Subscription #'.$subscription->id }}</li>
@stop

@section('toolbar-buttons')
    @if ( ! $subscription->trashed())
        <div x-data class="dropdown mr-sm">
            <button type="button" class="btn btn-light dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <i class="fas fa-gear"></i> Actions <i class="fas fa-caret-down"></i>
            </button>
            <ul class="dropdown-menu pull-right">
                <li>
                    <a href="#" x-on:click="$dispatch('open-modal-cancel-subscription-confirmation', { subscription_id: {{ $subscription->id }} })">Cancel
                        subscription</a></li>
            </ul>
        </div>
    @endif
@stop

@section('content')
    @php
        $current_tab = Request::get('tab', 'orders');
    @endphp

    <div class="relative border-b border-gray-200 pb-5 sm:pb-0">
        <div class="min-w-0 flex-1">
            <div class="sm:flex sm:items-baseline sm:justify-between">
                <div class="sm:w-0 sm:flex-1">
                    <div class="flex items-baseline space-x-4">
                        @php($name = str($subscription->customer?->first_name ?? 'Deleted customer'))
                        <h1 class="mt-2 text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">{{ $name->endsWith('s') ? $name->append('\'') : $name->append('\'s')  }}
                            subscription</h1>
                        <p class="m-0 text-gray-500">ID: {{ $subscription->id }}</p>
                    </div>

                </div>
                <div class="mt-4 flex items-center justify-between space-x-2 sm:mt-0 sm:ml-6 sm:flex-shrink-0 sm:justify-start">
                    @if ( ! $subscription->trashed())
                        <span class="inline-flex items-center rounded-full bg-keppel-100 px-3 py-0.5 text-sm font-medium text-keppel-800">Active</span>
                    @else
                        <span class="inline-flex items-center rounded-full bg-gray-100 px-3 py-0.5 text-sm font-medium text-gray-800">Canceled</span>
                    @endif
                </div>
            </div>

            <div class="mt-1 flex flex-col sm:mt-0 sm:flex-row sm:flex-wrap sm:space-x-8">
                <div class="mt-2 flex items-center text-sm text-gray-500">
                    <svg class="mr-1.5 h-5 w-5 flex-shrink-0 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z" />
                    </svg>
                    <a href="{{ ! is_null($subscription->customer) ? route('admin.users.edit', $subscription->customer) : '#' }}" class="text-gray-500 @if (is_null($subscription->customer)) italic @endif hover:text-gray-700">{{ $subscription->customer?->email ?? 'Deleted' }}</a>
                </div>
                <div class="mt-2 flex items-center text-sm text-gray-500">
                    <svg class="mr-1.5 h-5 w-5 flex-shrink-0 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                        <path fill-rule="evenodd" d="M5.75 2a.75.75 0 01.75.75V4h7V2.75a.75.75 0 011.5 0V4h.25A2.75 2.75 0 0118 6.75v8.5A2.75 2.75 0 0115.25 18H4.75A2.75 2.75 0 012 15.25v-8.5A2.75 2.75 0 014.75 4H5V2.75A.75.75 0 015.75 2zm-1 5.5c-.69 0-1.25.56-1.25 1.25v6.5c0 .69.56 1.25 1.25 1.25h10.5c.69 0 1.25-.56 1.25-1.25v-6.5c0-.69-.56-1.25-1.25-1.25H4.75z" clip-rule="evenodd" />
                    </svg>
                    Subscribed on {{ $subscription->created_at->format('M d, Y') }}
                </div>
                @if($subscription->trashed())
                    <div class="mt-2 flex items-center text-sm text-gray-500">
                        <svg class="mr-1.5 h-5 w-5 flex-shrink-0 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>

                        Canceled on {{ $subscription->deleted_at->format('M d, Y') }}
                    </div>
                @endif
            </div>
        </div>

        <div class="mt-6">
            <!-- Dropdown menu on small screens -->
            <div class="sm:hidden">
                <label for="current-tab" class="hidden">Select a tab</label>
                <select id="current-tab" name="current-tab" onchange="setActiveTab(this.value)" class="block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-keppel-500 focus:outline-none focus:ring-keppel-500 sm:text-sm">
                    <option value="orders" @if($current_tab === 'orders') selected @endif>Orders</option>
                    <option value="products" @if($current_tab === 'products') selected @endif>Products</option>
                    <option value="events" @if($current_tab === 'events') selected @endif>Events</option>
                </select>
            </div>
            <!-- Tabs at small breakpoint and up -->
            <div class="hidden sm:block">
                <nav class="-mb-px flex space-x-8">
                    <a href="{{ route('admin.subscriptions.edit', ['subscription' => $subscription, 'tab' => 'orders']) }}" class="@if($current_tab === 'orders') border-keppel-500 text-keppel-600 @else border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 @endif whitespace-nowrap pb-4 px-1 border-b-2 font-medium text-sm" aria-current="page">Orders</a>
                    <a href="{{ route('admin.subscriptions.edit', ['subscription' => $subscription, 'tab' => 'products']) }}" class="@if($current_tab === 'products') border-keppel-500 text-keppel-600 @else border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 @endif whitespace-nowrap pb-4 px-1 border-b-2 font-medium text-sm">Products</a>
                    <a href="{{ route('admin.subscriptions.edit', ['subscription' => $subscription, 'tab' => 'events']) }}" class="@if($current_tab === 'events') border-keppel-500 text-keppel-600 @else border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 @endif whitespace-nowrap pb-4 px-1 border-b-2 font-medium text-sm">Events</a>
                </nav>
            </div>
        </div>
    </div>

    <div class="mt-4">
        @include("subscriptions.partials.".$current_tab)
    </div>
@stop

@section('modals')
    <livewire:admin.modals.add-subscription-item />
    <livewire:admin.modals.edit-subscription-item />
    <livewire:admin.modals.delete-subscription-item-confirmation />
    <livewire:admin.modals.cancel-subscription-confirmation />
@endsection

@section('scripts')
    <script>
        function setActiveTab(tab) {
            window.location = "{{ route('admin.subscriptions.edit', $subscription->id) }}?tab=" + tab;
        }
    </script>
@endsection
