@props(['selected' => null, 'placeholder' => null])

@php
    $selected_array = \Illuminate\Support\Arr::wrap($selected);
@endphp

<select {{ $attributes }}>
    @if($placeholder)
        <option value="">{{ $placeholder }}</option>
    @endif
    @foreach(\App\Support\Enums\OrderStatus::all() as $key => $label)
        <option value="{{ $key }}" @selected(in_array($key, $selected_array))>{{ $label }}</option>
    @endforeach
</select>
