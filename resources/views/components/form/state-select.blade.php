@props(['selected' => null, 'placeholder' => null])

<select {{ $attributes }}>
    @if($placeholder)
        <option value="">{{ $placeholder }}</option>
    @endif
    <optgroup label="United States">
        @foreach(config('grazecart.states') as $abbr => $label)
            <option value="{{ $abbr }}" @selected($abbr === $selected)>{{ $label }}</option>
        @endforeach
    </optgroup>
    <optgroup label="Canadian Provinces">
        @foreach(config('grazecart.provinces') as $abbr => $label)
            <option value="{{ $abbr }}" @selected($abbr === $selected)>{{ $label }}</option>
        @endforeach
    </optgroup>
</select>

