@props(['close_on_click_away' => true])

<div x-data="{ open: @entangle('open') }"
     x-init="$watch('open', value => $dispatch(value === true ? 'modal-panel-opened' : 'modal-panel-closed'))"
     x-show="open"
     x-cloak
     @keydown.window.escape="$dispatch('modal-escaped')"
     @if($close_on_click_away)
         x-on:click.away="$dispatch('modal-escaped')"
     @endif
     aria-labelledby="modal-title"
     x-ref="dialog"
     aria-modal="true"
>
    <div class="relative z-[1000]">
        <div
                x-show="open"
                x-cloak
                style="display: none;"
                x-transition:enter="ease-out duration-300"
                x-transition:enter-start="opacity-0"
                x-transition:enter-end="opacity-100" \
                x-transition:leave="ease-in duration-200"
                x-transition:leave-start="opacity-100"
                x-transition:leave-end="opacity-0"
                class="pointer-events-none fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
        ></div>

        <div x-show="open" class="pointer-events-none fixed inset-0 z-10 overflow-y-auto">
            <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
                <div x-show="open"
                     x-cloak
                     style="display: none;"
                     x-transition:enter="ease-out duration-300"
                     x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                     x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
                     x-transition:leave="ease-in duration-200"
                     x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
                     x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                     class="pointer-events-auto relative w-full mx-auto transform transition-all"
                >
                    {{ $slot }}
                </div>
            </div>
        </div>
    </div>
</div>
