@extends('layouts.main', ['pageTitle' => $collection->title])

@section('toolbar-breadcrumb')
    <li><a href="/admin/subcollections">Subcollections</a></li>
    <li>{{ $collection->title }}</li>
@stop
@section('toolbar-buttons')
    <button class="btn btn-success flex-item mr-sm" @click="submitForm('updateCollectionForm', $event)">Save</button>
    <div class="dropdown flex-item">
        <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
            <i class="fas fa-gear"></i> Actions <i class="fas fa-caret-down"></i>
        </button>
        <ul class="dropdown-menu pull-right">
            <li><a href="#" @click="showModal('deleteCollectionModal')">Delete</a></li>
        </ul>
    </div>
@stop

@section('content')
    @include('subcollections.partials.delete-collection-modal')
    <div class="row">
        <div class="content">
            @include("subcollections.partials.settings")
        </div>
    </div>
@stop
