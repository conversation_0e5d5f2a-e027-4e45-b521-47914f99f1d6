<div class="panel ">
    <div class="panel-heading">Filter Recipes</div>
    <div class="panel-body">
        <form action="{{ route('admin.recipes.index') }}" method="GET">
            {{--Search--}}
            <div class="form-group">
                <label for="title">Search</label>
                <input type="text" name="recipes" class="form-control" value="{{ Request::get('recipes') }}" placeholder="Search by title" />
            </div>

            <div class="form-group">
                <label for="author">Author</label>
                <x-form.staff-select
                        class="form-control"
                        name="user_id"
                        placeholder="All"
                        :selected="request('user_id')"
                />
            </div>

            <div class="form-group">
                <label for="published_date">Published Date</label>
                <input type="text" name="published_date" class="form-control" value="{{ Request::get('published_date') }}" id="publishDate" />
            </div>

            <div class="form-group">
                <button type="submit" class="btn btn-default btn-block">Filter</button>
            </div>
        </form>
    </div>
</div>
