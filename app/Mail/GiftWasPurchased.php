<?php

namespace App\Mail;

use App\Models\Order;
use App\Models\Template;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Symfony\Component\Mime\Email;

class GiftWasPurchased extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $subject;
    public $content;
    public $text;
    public $metadata;
    public $styles;

    public function __construct(public int $order_id)
    {}

    public function build()
    {
        $order = Order::forEmail()->find($this->order_id);

        if(empty($order->recipient_email)) {
            return;
        }

        // Set up the headers.
        $this->metadata['order_id'] = $order->id;
        $this->metadata['customer_id'] = $order->customer_id;

        $template = Template::find(setting('email_gift_purchased_template'));

        if (is_null($template)) {
            return;
        }

        // Build the template content.
        $mergedContent = $template->mergeWithOrder($order);
        $this->subject = $template->subject;
        $this->content = $mergedContent->getHTML();
        $this->text = $mergedContent->getText();
        $this->styles = $template->settings;

        $this->from($template->getFromEmail(), $template->getFromName());
        $this->replyTo($template->getReplyToEmail(), $template->getFromName());

        return $this->withSymfonyMessage(function (Email $m) {
            $headers = $m->getHeaders();
            $headers->addTextHeader('X-Mailgun-Variables', json_encode($this->metadata));
            $headers->addTextHeader('X-Mailgun-Tag', 'GiftPurchased');
        })
            ->view('emails.transactional-html')
            ->text('emails.transactional-text');
    }

    public function tags(): array
    {
        return ['email', 'gift-purchased'];
    }
}
