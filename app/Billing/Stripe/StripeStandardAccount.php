<?php

namespace App\Billing\Stripe;

use App\Billing\Gateway\Customer as GatewayCustomer;
use App\Billing\Gateway\GatewayException;
use App\Billing\Gateway\PaymentMethod as GatewayPaymentMethod;
use App\Billing\Gateway\Transaction;
use App\Contracts\Billing;
use App\Events\Billing\CardWasDeclined;
use App\Models\Card;
use App\Models\Order;
use App\Models\OrderPayment;
use App\Models\User;
use App\Services\SettingsService;
use Bugsnag\BugsnagLaravel\Facades\Bugsnag;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Stripe\Customer;
use Stripe\Exception\ApiErrorException;
use Stripe\Exception\CardException;
use Stripe\Exception\InvalidRequestException;
use Stripe\PaymentIntent;
use Stripe\PaymentMethod;
use Stripe\SetupIntent;
use Stripe\Token;

/**
 * Seven Sons only
 */
class StripeStandardAccount implements Billing
{
    protected Stripe $stripe;

    public function __construct()
    {
        // Standard accounts should use their own Stripe API key
        $this->stripe = app(Stripe::class)
            ->overrideApiKey(app(SettingsService::class)->stripeSecretKey());
    }

    public function isConnected(): bool
    {
        return true;
    }

    /**
     * @throws GatewayException
     */
    public function chargeOrderWithCard(Order $order, Card $card, int $amount, string $description): Transaction
    {
        return match (true) {
            $card->isPaymentMethod() => $this->chargeOrderWithPaymentMethod($order, $card, $amount, $description),
            default => $this->chargeOrderWithSource($order, $card, $amount, $description)
        };
    }

    protected function isPaymentMethod(string $source_id): bool
    {
        return Str::startsWith($source_id, 'pm_');
    }

    /**
     * @throws GatewayException
     */
    protected function chargeOrderWithPaymentMethod(Order $order, Card $card, int $amount, string $description): Transaction
    {
        $payment_method = $this->retrievePaymentMethod($card->source_id);

        $payload = [
            'amount' => $amount,
            'application_fee_amount' => null,
            'currency' => $this->currency(),
            'confirm' => true,
            'description' => $description,
            'off_session' => true,
            'customer' => $payment_method->customer,
            'payment_method' => $payment_method,
            // payment_method_data is used instead of payment_method when passing tokenized source
            // 'payment_method_data' => ['type' => 'card', 'card' => ['token' => $token]],
            'receipt_email' => $order->customer_email,
            'metadata' => $this->metaPayloadForOrder($order)
        ];

        if ( ! empty($order->shipping_street)) {
            $payload['shipping'] = $this->shippingPayloadForOrder($order);
        }

        try {
            $intent = $this->stripe->createPaymentIntent($payload);
        } catch (ApiErrorException $exception) {
            throw new GatewayException($exception->getMessage());
        }

        /**
         * Charges object is supported on the API version we are currently
         * using (See App\Billing\Stripe\Stripe) but support is removed in
         * a future version (https://stripe.com/docs/changelog#november-16,-2022)
         */
        $charge = $intent->charges->first();

        if ($charge->status !== 'succeeded') {
            throw new GatewayException($charge->failure_message);
        }

        return new Transaction(
            type: Transaction::TYPE_CHARGE,
            id: $charge->id,
            amount: $charge->amount,
            success: true,
            source_id: $charge->payment_method ?? null,
            payment_type: $charge->payment_method_details->card->brand ?? null,
            description: $description
        );
    }

    /**
     * @throws GatewayException
     */
    protected function retrievePaymentMethod(string $payment_method_id): PaymentMethod
    {
        try {
            return $this->stripe->retrievePaymentMethod($payment_method_id);
        } catch (ApiErrorException $exception) {
            throw new GatewayException($exception->getMessage());
        }
    }

    protected function currency(): string
    {
        return setting('currency', 'USD');
    }

    protected function metaPayloadForOrder(Order $order): array
    {
        return [
            'order #' => $order->id,
            'customer' => $order->customer_full_name,
            'email' => $order->customer_email,
            'phone' => $order->customer_phone,
        ];
    }

    protected function shippingPayloadForOrder(Order $order): array
    {
        return [
            'name' => $order->customer_full_name,
            'phone' => $order->customer_phone ?? null,
            'tracking_number' => $order->tracking_id ?? null,
            'address' => [
                'line1' => $order->shipping_street,
                'line2' => $order->shipping_street_2 ?? null,
                'city' => $order->shipping_city ?? null,
                'state' => $order->shipping_state ?? null,
                'country' => $order->shipping_country ?? null,
                'postal_code' => $order->shipping_zip ?? null,
            ]
        ];
    }

    /**
     * @throws ApiErrorException
     */
    public function createPaymentIntent(int $amount, string $payment_method, bool $confirm = false): PaymentIntent
    {
        return $this->stripe->createPaymentIntent([
            'amount' => $amount,
            'currency' => 'usd',
            'payment_method' => $payment_method,
            'confirm' => $confirm
        ]);
    }

    /**
     * TODO: Deprecate Charges API in favor of PaymentIntents API
     *
     * @throws GatewayException
     */
    protected function chargeOrderWithSource(Order $order, Card $card, int $amount, string $description): Transaction
    {
        try {
            $source = $this->stripe->retrieveCustomerSource($order->customer->id, $card->source_id);
        } catch (ApiErrorException $exception) {
            throw new GatewayException($exception->getMessage());
        }

        $payload = [
            'customer' => $order->customer->id,
            'source' => $source,
            'amount' => $amount,
            'currency' => $this->currency(),
            'application_fee' => null,
            'description' => $description,
            'receipt_email' => $order->customer_email,
            'metadata' => $this->metaPayloadForOrder($order)
        ];

        if (!empty($order->shipping_street)) {
            $payload['shipping'] = $this->shippingPayloadForOrder($order);
        }

        try {
            $charge = $this->stripe->createCharge($payload, [
                'idempotency_key' => $order->idempotencyKey($amount),
            ]);

        } catch (CardException $exception) {
            $error_message = $exception->getMessage();

            if ($handled_message = $this->getDeclinedMessageFromException($exception)) {
                $error_message = $handled_message;
                CardWasDeclined::dispatch($order);
            }

            throw new GatewayException($error_message);

        } catch (InvalidRequestException $exception) {
            $message = $exception->getMessage();

            if (
                isset($exception->getJsonBody()['error']['type'])
                && $exception->getJsonBody()['error']['type'] === 'idempotency_error'
            ) {
                $message = 'You just recently attempted to charge this order. Please wait 1 minute to try again.';
            }

            throw new GatewayException($message);
        } catch (Exception $exception) {
            throw new GatewayException($exception->getMessage());
        }

        return new Transaction(
            type: Transaction::TYPE_CHARGE,
            id: $charge->id,
            amount: $charge->amount,
            success: $charge->status === 'succeeded',
            source_id: $charge->source->id ?? null,
            payment_type: $charge->source->brand ?? null,
            description: $description
        );
    }

    /**
     * @throws GatewayException
     */
    public function retrieveCustomerSource(string $customerId, string $sourceId): ?GatewayPaymentMethod
    {
        $card = $this->retrieveCustomerSourceAsCard($customerId, $sourceId);

        return new GatewayPaymentMethod(
            id: $card->id,
            customer_id: $customerId,
            customer_name: $card->name,
            exp_month: (string) $card->exp_month,
            exp_year: (string) $card->exp_year,
            brand: $card->brand,
            last_four: $card->last4
        );
    }

    /**
     * @throws GatewayException
     */
    protected function retrieveCustomerSourceAsCard(string $customerId, string $sourceId): \Stripe\Card
    {
        try {
            return $this->stripe->retrieveCustomerSource($customerId, $sourceId);
        } catch (ApiErrorException $exception) {
            throw new GatewayException($exception->getMessage());
        }
    }

    protected function getDeclinedMessageFromException(CardException $exception): ?string
    {
        if ($exception->getStripeCode() == 'card_declined') {
            return 'The customer\'s card was declined. An email has been sent to inform them.';
        }

        if ($exception->getStripeCode() == 'incorrect_cvc') {
            return 'The customer\'s card was declined because of the wrong CVC code. An email has been sent to inform them.';
        }

        if ($exception->getStripeCode() == 'expired_card') {
            return 'The customer\'s card was declined because it has expired. An email has been sent to inform them.';
        }

        if ($exception->getStripeCode() == 'incorrect_number') {
            return 'The customer\'s card was declined because the card number was incorrect. An email has been sent to inform them.';
        }

        return null;
    }

    /**
     * @throws GatewayException
     */
    public function chargeAmountWithCard(Card $card, int $amount, string $description): Transaction
    {
        return match (true) {
            $card->isPaymentMethod() => $this->chargeAmountWithPaymentMethod($card, $amount, $description),
            default => $this->chargeAmountWithSource($card, $amount, $description)
        };
    }

    /**
     * @throws GatewayException
     */
    protected function chargeAmountWithPaymentMethod(Card $card, int $amount, string $description): Transaction
    {
        $payment_method = $this->retrievePaymentMethod($card->source_id);

        try {
            $intent = $this->stripe->createPaymentIntent([
                'amount' => $amount,
                'application_fee_amount' => null,
                'currency' => $this->currency(),
                'confirm' => true,
                'description' => $description,
                'off_session' => true,
                'customer' => $payment_method->customer,
                'payment_method' => $payment_method,
                // payment_method_data is used instead of payment_method when passing tokenized source
                // 'payment_method_data' => ['type' => 'card', 'card' => ['token' => $token]],
            ]);
        } catch (ApiErrorException $exception) {
            throw new GatewayException($exception->getMessage());
        }

        /**
         * Charges object is supported on the API version we are currently
         * using (See App\Billing\Stripe\Stripe) but support is removed in
         * a future version (https://stripe.com/docs/changelog#november-16,-2022)
         */
        $charge = $intent->charges->first();

        if ($charge->status !== 'succeeded') {
            throw new GatewayException($charge->failure_message);
        }

        return new Transaction(
            type: Transaction::TYPE_CHARGE,
            id: $charge->id,
            amount: $charge->amount,
            success: true,
            source_id: $charge->payment_method ?? null,
            payment_type: $charge->payment_method_details->card->brand ?? null,
            description: $description
        );
    }

    /**
     * TODO: Deprecate Charges API in favor of PaymentIntents API
     *
     * @throws GatewayException
     */
    protected function chargeAmountWithSource(Card $card, int $amount, string $description): Transaction
    {
        try {
            $source = $this->stripe->retrieveCustomerSource($card->user->customer_id, $card->source_id);
        } catch (ApiErrorException $exception) {
            throw new GatewayException($exception->getMessage());
        }

        try {
            $charge = $this->stripe->createCharge([
                'customer' => $source->customer,
                'source' => $source,
                'amount' => $amount,
                'currency' => $this->currency(),
                'application_fee' => null,
                'description' => $description,
            ], [
                'idempotency_key' => $card->idempotencyKey($amount),
            ]);

        } catch (CardException $exception) {
            $error_message = $exception->getMessage();

            if ($handled_message = $this->getDeclinedMessageFromException($exception)) {
                $error_message = $handled_message;
            }

            throw new GatewayException($error_message);

        } catch (InvalidRequestException $exception) {
            $message = $exception->getMessage();

            if (
                isset($exception->getJsonBody()['error']['type'])
                && $exception->getJsonBody()['error']['type'] === 'idempotency_error'
            ) {
                $message = 'You just recently attempted to charge this order. Please wait a moment and try again.';
            }

            throw new GatewayException($message);
        } catch (Exception $exception) {
            throw new GatewayException($exception->getMessage());
        }

        return new Transaction(
            type: Transaction::TYPE_CHARGE,
            id: $charge->id,
            amount: $charge->amount,
            success: $charge->status === 'succeeded',
            source_id: $charge->source->id ?? null,
            payment_type: $charge->source->brand ?? null,
            description: $description
        );
    }

    /**
     * @throws GatewayException
     */
    public function refundPayment(OrderPayment $payment, ?int $amount = null, ?string $reason = null): Transaction
    {
        try {
            $refund = $this->stripe->createRefund([
                'charge' => $payment->payment_id,
                'amount' => $amount,
                'metadata' => ['description' => $reason]
            ]);

        } catch (ApiErrorException $exception) {
            throw new GatewayException($exception->getMessage());
        }

        return new Transaction(
            type: Transaction::TYPE_REFUND,
            id: $refund->id,
            amount: $refund->amount,
            success: $refund->status === 'succeeded',
            source_id: $payment->source_id ?? null,
            payment_type: $payment->payment_type ?? null,
            description: $reason
        );
    }

    /**
     * @throws GatewayException
     */
    public function createCustomer(User $user): GatewayCustomer
    {
        try {
            $customer = $this->stripe->createCustomer([
                'source' => null,
                'email' => $user->email,
                'description' => $user->full_name,
                'metadata' => [
                    'first_name' => $user->first_name,
                    'last_name' => $user->last_name,
                    'phone' => $user->phone,
                ],
            ]);
        } catch (ApiErrorException $exception) {
            throw new GatewayException($exception->getMessage());
        }

        return new GatewayCustomer(
            id: $customer->id,
            email: $customer->email ?? null,
            first_name: null,
            last_name: null,
            address_1: null,
            address_2: null,
            address_city: null,
            address_state: null,
            address_zip: null,
            address_country: null,
        );
    }

    /**
     * @throws GatewayException
     */
    public function createCustomerSource(User $user, string $token, array $params = []): GatewayPaymentMethod
    {
        try {
            $payment_method = $this->stripe->createCustomerSource($user->customer_id, [
                'source' => $token,
                'metadata' => [
                    'email' => $params['email'] ?? null,
                    'name' => $params['name'] ?? null
                ]
            ]);
        } catch (ApiErrorException $exception) {
            throw new GatewayException($exception->getMessage());
        }

        return new GatewayPaymentMethod(
            id: $payment_method->id,
            customer_id: $user->customer_id,
            customer_name: $payment_method->name,
            exp_month: (string) $payment_method->exp_month,
            exp_year: (string) $payment_method->exp_year,
            brand: $payment_method->brand,
            last_four: $payment_method->last4
        );
    }

    /**
     * @throws GatewayException
     */
    public function retrieveDefaultSourceForUser(User $user): ?GatewayPaymentMethod
    {
        try {
            $source = $this->stripe->retrieveDefaultSource($user->customer_id);
        } catch (ApiErrorException $exception) {
            throw new GatewayException($exception->getMessage());
        }

        if (is_null($source)) {
            return null;
        }

        return new GatewayPaymentMethod(
            id: $source->id,
            customer_id: $user->customer_id,
            customer_name: $source->name,
            exp_month: (string) $source->exp_month,
            exp_year: (string) $source->exp_year,
            brand: $source->brand,
            last_four: $source->last4
        );
    }

    /**
     * @throws GatewayException
     */
    public function setDefaultCard(Card $card): GatewayPaymentMethod
    {
        $params = match (true) {
            $card->isPaymentMethod() => [
                'default_source' => null,
                'invoice_settings' => [
                    'default_payment_method' => $card->source_id
                ]
            ],
            default => [
                'default_source' => $card->source_id,
                'invoice_settings' => [
                    'default_payment_method' => null
                ]
            ]
        };

        try {
            $this->stripe->updateCustomer($card->user->customer_id, $params);
        } catch (ApiErrorException $exception) {
            throw new GatewayException($exception->getMessage());
        }

        $source = $this->stripe->retrieveDefaultSource($card->user->customer_id);

        return new GatewayPaymentMethod(
            id: $source->id,
            customer_id: $card->user->customer_id,
            customer_name: $source->name,
            exp_month: (string) $source->exp_month,
            exp_year: (string) $source->exp_year,
            brand: $source->brand,
            last_four: $source->last4
        );
    }

    /**
     * @throws GatewayException
     */
    public function updateCustomer(string $customerId, GatewayCustomer $customer): GatewayCustomer
    {
        try {
            $this->stripe->updateCustomer($customerId, [
                'email' => $customer->email,
                'description' => $customer->first_name . ' ' . $customer->last_name,
                'metadata' => [
                    'first_name' => $customer->first_name,
                    'last_name' => $customer->last_name,
                ],
                'shipping' => [
                    'name' => $customer->first_name . ' ' . $customer->last_name,
                    'address' => [
                        'line1' => $customer->address_1,
                        'line2' => $customer->address_2,
                        'city' => $customer->address_city,
                        'country' => $customer->address_country,
                        'postal_code' => $customer->address_zip,
                        'state' => $customer->address_state
                    ],
                ]
            ]);
        } catch (ApiErrorException $exception) {
            throw new GatewayException($exception->getMessage());
        }

        return $customer;
    }

    /**
     * @throws GatewayException
     */
    public function updateCardDetails(Card $card, GatewayPaymentMethod $details): GatewayPaymentMethod
    {
        return match (true) {
            $card->isPaymentMethod() => $this->updatePaymentMethod($card, [
                'cardholder_name' => $details->customer_name,
                'exp_month' => $details->exp_month,
                'exp_year' => $details->exp_year,
            ]),
            default => $this->updateCustomerSource($card, $details)
        };
    }

    /**
     * @throws GatewayException
     */
    protected function updatePaymentMethod(Card $card, array $details): GatewayPaymentMethod
    {
        try {
            $this->stripe->updatePaymentMethod(
                $paymentMethod = $this->retrievePaymentMethod($card->source_id),
                ['card' => [
                    'exp_month' => $details['exp_month'] ?? $paymentMethod->exp_month,
                    'exp_year' => $details['exp_year'] ?? $paymentMethod->exp_year,
                ]]
            );
        } catch (ApiErrorException $exception) {
            throw new GatewayException($exception->getMessage());
        }

        return new GatewayPaymentMethod(
            id: $paymentMethod->id,
            customer_id: $card->user->customer_id,
            customer_name: $paymentMethod->customer_name,
            exp_month: $details['exp_month'] ?? $paymentMethod->exp_month,
            exp_year: $details['exp_year'] ?? $paymentMethod->exp_year,
            brand: $paymentMethod->brand,
            last_four: $paymentMethod->last_four
        );
    }

    /**
     * @throws GatewayException
     */
    protected function updateCustomerSource(Card $card, GatewayPaymentMethod $details): GatewayPaymentMethod
    {
        try {
            $this->stripe->updateCustomerCard(
                $fetchedCard = $this->retrieveCustomerSourceAsCard($card->user->customer_id, $card->source_id),
                [
                    'cardholder_name' => $details->customer_name,
                    'exp_month' => $details->exp_month,
                    'exp_year' => $details->exp_year,
                ]
            );
        } catch (ApiErrorException $exception) {
            Bugsnag::notifyException($exception);
            throw new GatewayException($exception->getMessage());
        }

        return new GatewayPaymentMethod(
            id: $fetchedCard->id,
            customer_id: $card->user->customer_id,
            customer_name: $details->customer_name,
            exp_month: $details->exp_month,
            exp_year: $details->exp_year,
            brand: $fetchedCard->id,
            last_four: $fetchedCard->last4
        );
    }

    /**
     * @throws GatewayException
     */
    public function deleteCard(string $customer_id, string $source_id): void
    {
        try {
            match (true) {
                $this->isPaymentMethod($source_id) => $this->stripe->detachPaymentMethod( $source_id),
                default => $this->stripe->deleteCustomerSource($customer_id, $source_id)
            };
        } catch (ApiErrorException $exception) {
            // If card doesn't exist already, just continue. Otherwise, throw an error
            if (str($exception->getMessage())->contains('No such source')) {
                return;
            }

            Bugsnag::notifyException($exception);
            throw new GatewayException($exception->getMessage());
        }
    }

    /**
     * @throws ApiErrorException
     */
    public function capturePaymentIntent(string $payment_intent_id): PaymentIntent
    {
        return $this->stripe->capturePaymentIntent($payment_intent_id);
    }

    /**
     * @throws GatewayException
     */
    public function createSetupIntent(User $user): SetupIntent
    {
        try {
            return $this->stripe->createSetupIntent([
                'payment_method_types' => ['card'],
                'usage' => 'off_session',
                'customer' => $user->customer_id
            ]);
        } catch (ApiErrorException $exception) {
            throw new GatewayException($exception->getMessage());
        }
    }

    /**
     * @throws ApiErrorException
     */
    public function fetchSetupIntent(string $id): SetupIntent
    {
        return $this->stripe->fetchSetupIntent($id);
    }

    /**
     * @throws ApiErrorException
     */
    public function fetchPaymentMethod(string $id): PaymentMethod
    {
        return $this->stripe->fetchPaymentMethod($id);
    }

    /**
     * @deprecated
     */
    public function createCard(User $user, string $paymentMethodId): GatewayPaymentMethod
    {
        throw new \BadMethodCallException('This method is not in use.');
    }

    /**
     * @return Collection<int, \App\Billing\Gateway\PaymentMethod>
     * @throws GatewayException
     */
    public function retrieveUserPaymentMethods(User $user): Collection
    {
        if (empty($user->customer_id)) {
            return collect();
        }

        try {
            return collect($this->stripe->retrieveCustomerPaymentMethods($user->customer_id)->data)
                ->map(fn(PaymentMethod $payment_method) => new GatewayPaymentMethod(
                    id: $payment_method->id,
                    customer_id: $payment_method->customer,
                    customer_name: $payment_method->billing_details->name,
                    exp_month: (string) $payment_method->card->exp_month,
                    exp_year: (string) $payment_method->card->exp_year,
                    brand: $payment_method->card->brand,
                    last_four: $payment_method->card->last4
                ));
        } catch (ApiErrorException $exception) {
            throw new GatewayException($exception->getMessage());
        }
    }

    /**
     * @throws GatewayException
     */
    protected function cloneCardAsToken(Card $card, string $customer_id): Token
    {
        try {
            $this->stripe->retrieveCustomerSource($customer_id, $card->source_id);
            return $this->stripe->createToken(['card' => $card->source_id]);
        } catch (ApiErrorException $exception) {
            throw new GatewayException($exception->getMessage());
        }
    }

    /**
     * @throws GatewayException
     */
    protected function retrieveCustomer($customerId): Customer
    {
        try {
            return $this->stripe->retrieveCustomer($customerId);
        } catch (ApiErrorException $exception) {
            throw new GatewayException($exception->getMessage());
        }
    }
}
