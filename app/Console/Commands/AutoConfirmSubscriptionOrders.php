<?php

namespace App\Console\Commands;

use App\Events\Subscription\RecurringOrderWasConfirmed;
use App\Models\Integration;
use App\Models\Order;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;

class AutoConfirmSubscriptionOrders extends Command
{
    protected $signature = 'grazecart:auto-confirm-orders';

    protected $description = 'Automatically confirm subscription orders ready for confirmation.';

    public function handle(): int
    {
        Integration::initialize('drip');

        Order::query()
            ->readyForAutoConfirmation()
            ->chunkById(100, function (Collection $orders) {
                $orders->each(function (Order $order) {
                    $order->confirm();
                    event(new RecurringOrderWasConfirmed($order));
                });
            });

        return Command::SUCCESS;
    }
}
