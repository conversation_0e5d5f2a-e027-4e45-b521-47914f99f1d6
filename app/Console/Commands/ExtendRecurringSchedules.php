<?php

namespace App\Console\Commands;

use App\Jobs\ExtendRepeatingSchedule;
use App\Models\Schedule;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Collection;

class ExtendRecurringSchedules extends Command
{
    protected $signature = 'schedule:extend';

    protected $description = 'Extend any schedules that need to be extended.';

    public function handle(): void
    {
        Schedule::readyToExtend()
            ->chunk(200, function (Collection $schedules) {
                foreach ($schedules as $schedule) {
                    /** @var Schedule $schedule */
                    ExtendRepeatingSchedule::dispatch($schedule->id);
                }
            });
    }
}
