<?php

namespace App\Models\Concerns;

use App\Cart\Item;
use Illuminate\Support\Collection;

trait HasCartQuantitiesByProductIdMap
{
    public function quantitiesByProductId(): Collection
    {
        return $this->itemsInCart()
            ->map(fn(Item $item) => $item->quantitiesByProductId())
            ->reduce(function ($carry, $collection) {
                $collection->each(function ($value, $key) use ($carry) {
                    // Sum the values by key
                    $carry[$key] = ($carry[$key] ?? 0) + $value;
                });
                return $carry;
            }, collect());
    }
}
