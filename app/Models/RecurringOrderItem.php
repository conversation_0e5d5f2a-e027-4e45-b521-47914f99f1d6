<?php

namespace App\Models;

use EloquentFilter\Filterable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * App\Models\RecurringOrderItem
 *
 * @property int $id
 * @property int $order_id
 * @property int $customer_id
 * @property int|null $product_id
 * @property int $qty
 * @property string $type
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property-read \App\Models\RecurringOrder|null $order
 * @property-read \App\Models\User|null $customer
 * @property-read \App\Models\Product|null $product
 * @method static \Illuminate\Database\Eloquent\Builder|RecurringOrderItem newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|RecurringOrderItem newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|RecurringOrderItem query()
 * @method static \Illuminate\Database\Eloquent\Builder|RecurringOrderItem whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RecurringOrderItem whereCustomerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RecurringOrderItem whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RecurringOrderItem whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RecurringOrderItem whereOrderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RecurringOrderItem whereProductId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RecurringOrderItem whereQty($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RecurringOrderItem whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RecurringOrderItem whereUpdatedAt($value)
 * @method static \Database\Factories\RecurringOrderItemFactory factory($count = null, $state = [])
 * @property int|null $unit_price_override
 * @method static \Illuminate\Database\Eloquent\Builder|RecurringOrderItem whereUnitPriceOverride($value)
 * @mixin \Eloquent
 */
class RecurringOrderItem extends Model
{
    use HasFactory, Filterable;

    protected $guarded = [];

    public function order(): BelongsTo
    {
        return $this->belongsTo(RecurringOrder::class);
    }

    public function customer(): HasOne
    {
        return $this->hasOne(User::class, 'id', 'customer_id');
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class)->withTrashed();
    }

    public function subtotal(): int
    {
        return $this->price() * $this->qty;
    }

    public function price(): int
    {
        if ( ! is_null($this->unit_price_override)) {
            return $this->product->isPricedByWeight()
                ? (int) round($this->unit_price_override * $this->product->weight)
                : $this->unit_price_override;
        }

        return $this->product->getPrice($this->qty);
    }

    public function weight(): float
    {
        return $this->product->weight;
    }

    public function isEligibleForSubscriptionSavings(): bool
    {
        if (is_null($this->product)) {
            return false;
        }

        return ! $this->product->isGiftCard();
    }

    public function formattedType(): string
    {
        return match($this->type) {
            'addon' => 'Add-on',
            'promo' => 'Promo',
            default => 'Standard',
        };
    }

    public function isPromo(): bool
    {
        return $this->type === 'promo';
    }
}
