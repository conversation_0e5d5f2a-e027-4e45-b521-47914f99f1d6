<?php

namespace App\Models;

use EloquentFilter\Filterable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Presenters\ProposalPresenter;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Laracasts\Presenter\PresentableTrait;

/**
 * App\Models\Proposal
 *
 * @property int $id
 * @property int $pickup_id
 * @property int|null $user_id
 * @property string $first_name
 * @property string $last_name
 * @property string $phone
 * @property string $email
 * @property string $status
 * @property int $rating
 * @property string $details
 * @property string $notes
 * @property string|null $start_date
 * @property int $truck_parking
 * @property int $customer_parking
 * @property array|null $days_of_week
 * @property string|null $street
 * @property string|null $street_2
 * @property string|null $city
 * @property string|null $state
 * @property string|null $zip
 * @property string $country
 * @property float|null $lat
 * @property float|null $lng
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read string $map_marker_content
 * @property-read \App\Models\Pickup $pickup
 * @property-read \App\Models\User|null $user
 * @method static \Illuminate\Database\Eloquent\Builder|Proposal newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Proposal newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Proposal query()
 * @method static \Illuminate\Database\Eloquent\Builder|Proposal whereCity($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Proposal whereCountry($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Proposal whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Proposal whereCustomerParking($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Proposal whereDaysOfWeek($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Proposal whereDetails($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Proposal whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Proposal whereFirstName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Proposal whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Proposal whereLastName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Proposal whereLat($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Proposal whereLng($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Proposal whereNotes($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Proposal wherePhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Proposal wherePickupId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Proposal whereRating($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Proposal whereStartDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Proposal whereState($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Proposal whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Proposal whereStreet($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Proposal whereStreet2($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Proposal whereTruckParking($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Proposal whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Proposal whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Proposal whereZip($value)
 * @property-read string $full_name
 * @method static \Database\Factories\ProposalFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder|Proposal filter(array $input = [], $filter = null)
 * @method static \Illuminate\Database\Eloquent\Builder|Proposal paginateFilter($perPage = null, $columns = [], $pageName = 'page', $page = null)
 * @method static \Illuminate\Database\Eloquent\Builder|Proposal simplePaginateFilter($perPage = null, $columns = [], $pageName = 'page', $page = null)
 * @method static \Illuminate\Database\Eloquent\Builder|Proposal whereBeginsWith($column, $value, $boolean = 'and')
 * @method static \Illuminate\Database\Eloquent\Builder|Proposal whereEndsWith($column, $value, $boolean = 'and')
 * @method static \Illuminate\Database\Eloquent\Builder|Proposal whereLike($column, $value, $boolean = 'and')
 * @mixin \Eloquent
 */
class Proposal extends Model
{
    use HasFactory;

    use PresentableTrait, Filterable;

    protected $guarded = [];

    protected $presenter = ProposalPresenter::class;

    protected $appends = ['map_marker_content'];

    public function getFullNameAttribute(): string
    {
        return $this->first_name . ' ' . $this->last_name;
    }

    public function hasPickupPoint(): bool
    {
        return $this->pickup !== null;
    }

    public function pickup(): BelongsTo
    {
        return $this->belongsTo(Pickup::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function getMapMarkerContentAttribute(): string
    {
        return view('proposals.partials.marker-popup')
            ->with(['proposal' => $this])
            ->render();
    }

    public function getDaysOfWeekAttribute(?string $value): ?array
    {
        return json_decode($value);
    }
}
