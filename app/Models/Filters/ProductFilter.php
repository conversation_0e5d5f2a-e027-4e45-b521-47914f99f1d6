<?php 

namespace App\Models\Filters;

use EloquentFilter\ModelFilter;
use Illuminate\Support\Arr;

class ProductFilter extends ModelFilter
{
    /**
    * Related Models that have ModelFilters as well as the method on the ModelFilter
    * As [relationMethod => [input_key1, input_key2]].
    *
    * @var array
    */
    public $relations = [];

    public function sort($direction): void
    {
        $this->when( ! empty($this->input('orderBy')), function ($query) use ($direction) {
            return $query->orderBy(
                column: $this->input('orderBy'),
                direction: in_array($direction, ['asc', 'desc']) ? $direction : 'asc'
            );
        })
            ->orderBy('products.created_at', 'desc');
    }

    public function products(string $search): void
    {
        $this->where(function ($query) use ($search) {
            $query->where('products.title', 'LIKE', '%' . $search . '%')
                ->orWhere('products.sku', $search);
        });
    }

    public function sku(string $sku): void
    {
        $this->where('products.sku', $sku);
    }

    public function vendor($vendor): void
    {
        $this->whereIn('products.vendor_id', Arr::wrap($vendor));
    }

    public function inventoryType($inventoryType): void
    {
        $this->whereIn('products.inventory_type', collect($inventoryType));
    }

    public function unitOfIssue($unitOfIssue): void
    {
        $this->where('products.unit_of_issue', $unitOfIssue);
    }

    public function trackInventory($trackInventory): void
    {
        $this->where('products.track_inventory', $trackInventory);
    }

    public function sale($sale): void
    {
        $this->where('products.sale', (bool) $sale);
    }

    public function tag(string $tag)
    {
        return $this->whereHas('tags', function ($q) use ($tag) {
            return $q->where('tags.slug', $tag);
        });
    }

    public function tags(array $tags): void
    {
        $this->whereHas('tags', function ($query) use ($tags) {
            return $query->whereIn('tags.id', $tags);
        });
    }

    public function protocol(string $protocol): void
    {
        $this->whereHas('protocols', function ($q) use ($protocol) {
            return $q->where('protocols.slug', $protocol);
        });
    }

    public function collection($collection): void
    {
        $this->whereHas('collections', function ($query) use ($collection) {
            return $query->whereIn('collections.id', Arr::wrap($collection));
        });
    }

    public function showDeleted(): void
    {
        /** @phpstan-ignore-next-line  */
        $this->withTrashed();
    }

    public function outOfStock(): void
    {
        $this->where('products.inventory', '<', 1);
    }

    public function productVisibility($visibility): void
    {
        $this->where('products.visible', $visibility);
    }

    public function accountingClass($accounting_class): void
    {
        $this->where('products.accounting_class', $accounting_class);
    }

    public function isBundle($isBundle): void
    {
        $this->where('products.is_bundle', $isBundle);
    }

    public function taxable($taxable): void
    {
        $this->where('products.taxable', $taxable);
    }
}
