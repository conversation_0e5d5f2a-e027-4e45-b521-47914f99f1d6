<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Filter
 *
 * @property int $id
 * @property string $title
 * @property string $type
 * @property string $url
 * @property int $sort
 * @property string|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|Filter newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Filter newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Filter query()
 * @method static \Illuminate\Database\Eloquent\Builder|Filter whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Filter whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Filter whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Filter whereSort($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Filter whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Filter whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Filter whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Filter whereUrl($value)
 * @method static \Database\Factories\FilterFactory factory($count = null, $state = [])
 * @mixin \Eloquent
 */
class Filter extends Model
{
    use HasFactory;

    protected $guarded = [];
}
