<?php

namespace App\Models;

use <PERSON><PERSON>brock\EloquentSluggable\Sluggable;
use EloquentFilter\Filterable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

/**
 * App\Models\Recipe
 *
 * @property int $id
 * @property int $user_id
 * @property string $title
 * @property string $slug
 * @property string $description
 * @property string $instructions
 * @property string|null $video
 * @property int $prep_time
 * @property int $cook_time
 * @property string|null $servings
 * @property string|null $cover_photo
 * @property bool $published
 * @property \Illuminate\Support\Carbon $published_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\User $author
 * @property-read string $cook_time_formatted
 * @property-read string $prep_time_formatted
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Ingredient[] $ingredients
 * @property-read int|null $ingredients_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Tag[] $tags
 * @property-read int|null $tags_count
 * @method static Builder|Recipe findSimilarSlugs(string $attribute, array $config, string $slug)
 * @method static Builder|Recipe newModelQuery()
 * @method static Builder|Recipe newQuery()
 * @method static Builder|Recipe query()
 * @method static Builder|Recipe recent(\App\Models\Recipe $exclude)
 * @method static Builder|Recipe whereCookTime($value)
 * @method static Builder|Recipe whereCoverPhoto($value)
 * @method static Builder|Recipe whereCreatedAt($value)
 * @method static Builder|Recipe whereDescription($value)
 * @method static Builder|Recipe whereId($value)
 * @method static Builder|Recipe whereInstructions($value)
 * @method static Builder|Recipe wherePrepTime($value)
 * @method static Builder|Recipe wherePublished($value)
 * @method static Builder|Recipe wherePublishedAt($value)
 * @method static Builder|Recipe whereServings($value)
 * @method static Builder|Recipe whereSlug($value)
 * @method static Builder|Recipe whereTitle($value)
 * @method static Builder|Recipe whereUpdatedAt($value)
 * @method static Builder|Recipe whereUserId($value)
 * @method static Builder|Recipe whereVideo($value)
 * @method static Builder|Recipe withUniqueSlugConstraints(\Illuminate\Database\Eloquent\Model $model, string $attribute, array $config, string $slug)
 * @method static \Database\Factories\RecipeFactory factory($count = null, $state = [])
 * @method static Builder|Recipe filter(array $input = [], $filter = null)
 * @method static Builder|Recipe paginateFilter($perPage = null, $columns = [], $pageName = 'page', $page = null)
 * @method static Builder|Recipe simplePaginateFilter($perPage = null, $columns = [], $pageName = 'page', $page = null)
 * @method static Builder|Recipe whereBeginsWith($column, $value, $boolean = 'and')
 * @method static Builder|Recipe whereEndsWith($column, $value, $boolean = 'and')
 * @method static Builder|Recipe whereLike($column, $value, $boolean = 'and')
 * @mixin \Eloquent
 */
class Recipe extends Model
{
    use HasFactory, Sluggable, Filterable;

    protected $guarded = [];

    protected $appends = ['prep_time_formatted', 'cook_time_formatted'];

    protected function casts(): array
    {
        return [
            'published_at' => 'datetime',
            'published' => 'boolean'
        ];
    }

    public function sluggable(): array
    {
        return [
            'slug' => [
                'source' => 'title'
            ]
        ];
    }

    public function isDraft(): bool
    {
        return ! $this->isPublished();
    }

    public function isPublished(): bool
    {
        return $this->published;
    }

    public function scopeRecent(Builder $query, Recipe $exclude): Builder
    {
        return $query
            ->whereNot('id', $exclude->id)
            ->where('published', true)
            ->orderBy('created_at', 'desc')
            ->take(3);
    }

    public function ingredients(): HasMany
    {
        return $this->hasMany(Ingredient::class);
    }

    public function author(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function tags(): BelongsToMany
    {
        return $this->belongsToMany(Tag::class)->withTimestamps();
    }

    public function getPrepTimeFormattedAttribute(): string
    {
        return $this->prep_time
            ? gmdate('H:i:s', $this->prep_time * 60)
            : gmdate('H:i:s', 0);
    }

    public function getCookTimeFormattedAttribute(): string
    {
        return $this->cook_time
            ? gmdate('H:i:s', $this->cook_time * 60)
            : gmdate('H:i:s', 0);
    }

    public function presentPrepTime(): string
    {
        return $this->getRecipeTime($this->prep_time);
    }

    public function presentCookTime(): string
    {
        return $this->getRecipeTime($this->cook_time);
    }

    private function getRecipeTime(int $cookTime): string
    {
        if ($cookTime < 60) {
            return $cookTime . ' ' . __(Str::plural('minute', $cookTime));
        }

        $hours = (int) floor($cookTime / 60);
        $minutes = $cookTime % 60;

        if ($minutes > 0) {
            return $hours . ' ' . __(Str::plural('hour', $hours)) . ' and ' . $minutes . ' ' . __(Str::plural('minute', $minutes));
        }

        return $hours . ' ' . __(Str::plural('hour', $hours));
    }
}
