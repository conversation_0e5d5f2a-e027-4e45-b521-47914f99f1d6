<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Spatie\SchemalessAttributes\Casts\SchemalessAttributes;

class Metric extends Model
{
    public $casts = [
        'extra_attributes' => SchemalessAttributes::class,
    ];

    public $timestamps = false;

    protected $guarded = [];

    public function scopeWithExtraAttributes(): Builder
    {
        return $this->extra_attributes->modelScope();
    }
}
