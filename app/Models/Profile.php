<?php

namespace App\Models;

use App\Actions\UpdateSitemap;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Proile
 *
 * @property int $id
 * @property int $user_id
 * @property string|null $slug
 * @property string|null $position_title
 * @property string|null $bio
 * @property string|null $facebook
 * @property string|null $twitter
 * @property string|null $linkedin
 * @property string|null $photo_path
 * @property string|null $created_at
 * @property string|null $updated_at
 * @property-read \App\Models\User $user
 * @method static \Illuminate\Database\Eloquent\Builder|ProductPriceGroup newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductPriceGroup newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductPriceGroup query()
 * @method static \Database\Factories\ProductPriceGroupFactory factory($count = null, $state = [])
 * @mixin \Eloquent
 */
class Profile extends Model
{
    use HasFactory;

    protected $guarded = ['id', 'user_id'];

    protected static function booted()
    {
        static::saved(function (Profile $profile) {
            if ($profile->user->isStaff()) {
                app(UpdateSitemap::class)->execute();
            }
        });

        static::deleted(function (Profile $profile) {
            if ($profile->user->isStaff()) {
                app(UpdateSitemap::class)->execute();
            }
        });
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function hasSocialAttributes()
    {
        return $this->facebook || $this->twitter || $this->linkedin;
    }
}
