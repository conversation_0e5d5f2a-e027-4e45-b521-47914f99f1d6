<?php

namespace App\Support;

use App\Contracts\Geocoder;
use App\Exceptions\NoGeocodeResultsException;
use App\Services\Geocoding\GeocodedAddress;
use Illuminate\Support\Facades\Cache;

trait FetchesGeocodedAddress
{
    /**
     * @throws NoGeocodeResultsException
     */
    private function fetchGeocodedAddressFromPostalCode(string $postal_code): GeocodedAddress
    {
        $cache_key = 'address_check:'.str_replace(' ', '_', $postal_code);

        $cache_value = Cache::get($cache_key);

        if (! is_null($cache_value)) {
            return GeocodedAddress::makeFromArray($cache_value);
        }

        $geocoded_address = app(Geocoder::class)->fromZipcode($postal_code);

        Cache::put(
            $cache_key,
            $geocoded_address->getAddressParts(),
            now()->addMinutes(120)
        );

        return $geocoded_address;
    }
}
