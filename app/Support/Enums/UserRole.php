<?php

namespace App\Support\Enums;

use Illuminate\Support\Collection;

enum UserRole: int
{
    case OWNER = 1;
    case ADMIN = 2;
    case EDITOR = 3;
    case CUSTOMER = 4;
    case SUPPORT = 6;
    case LEAD = 7;

    public static function get(?int $roleId = null): ?string
    {
        return UserRole::tryFrom($roleId)?->label();
    }

    public function label(): string
    {
        return match ($this) {
            UserRole::OWNER => 'Owner',
            UserRole::ADMIN => 'Admin',
            UserRole::EDITOR => 'Editor',
            UserRole::CUSTOMER => 'Customer',
            UserRole::SUPPORT => 'Support',
            UserRole::LEAD => 'Lead'
        };
    }

    public static function owner(): int
    {
        return UserRole::OWNER->value;
    }

    public static function admin(): int
    {
        return UserRole::ADMIN->value;
    }

    public static function editor(): int
    {
        return UserRole::EDITOR->value;
    }

    public static function customer(): int
    {
        return UserRole::CUSTOMER->value;
    }

    public static function support(): int
    {
        return UserRole::SUPPORT->value;
    }

    public static function lead(): int
    {
        return UserRole::LEAD->value;
    }

    public static function staff(): Collection
    {
        return collect([UserRole::OWNER->value, UserRole::ADMIN->value, UserRole::EDITOR->value]);
    }

    /**
     * @return Collection<int, string>
     */
    public static function whereIn(array $role_ids): Collection
    {
        return UserRole::all()
            ->filter(fn ($value, $key) => in_array($key, $role_ids));
    }

    /**
     * @return Collection<int, string>
     */
    public static function all(): Collection
    {
        return collect(UserRole::cases())
            ->mapWithKeys(fn (UserRole $role): array => UserRole::mappedToLabel($role));
    }

    /**
     * @return array<int, string>
     */
    private static function mappedToLabel(UserRole $role): array
    {
        return [$role->value => $role->label()];
    }
}
