<?php

namespace App\Support\Enums;

use Illuminate\Support\Arr;
use Illuminate\Support\Collection;

enum OrderStatus: int
{
    case CONFIRMED = 1;
    case PROCESSING = 2;
    case PACKED = 3;
    case PICKED_UP = 4;
    case COMPLETED = 5;
    case ON_HOLD = 6;
    case CANCELED = 7;
    case PRE_ORDER = 8;
    case OUT_FOR_DELIVERY = 9;

    /**
     * @return Collection<int, int>
     */
    public static function ids(array $excluded_ids = []): Collection
    {
        return OrderStatus::all()
            ->keys()
            ->filter(fn (int $status_id) => ! in_array($status_id, $excluded_ids));
    }

    /**
     * @return Collection<int, string>
     */
    public static function all(array $excluded = []): Collection
    {
        /** @var Collection<int, string> $statuses */
        $statuses = collect(OrderStatus::cases())
            ->mapWithKeys(fn (OrderStatus $status): array => OrderStatus::mappedToLabel($status));

        if (empty($excluded)) {
            return $statuses;
        }

        return $statuses->filter(fn (string $value, int $key) => ! in_array($key, $excluded) && ! in_array($value, $excluded));
    }

    /**
     * @return array<int, string>
     */
    private static function mappedToLabel(OrderStatus $status): array
    {
        return [$status->value => $status->label()];
    }

    public function label(): string
    {
        return match ($this) {
            OrderStatus::CONFIRMED => 'New',
            OrderStatus::PROCESSING => 'Processing',
            OrderStatus::PACKED => 'Packed',
            OrderStatus::PICKED_UP => 'Picked Up',
            OrderStatus::COMPLETED => 'Completed',
            OrderStatus::ON_HOLD => 'On Hold',
            OrderStatus::CANCELED => 'Canceled',
            OrderStatus::PRE_ORDER => 'Pre Order',
            OrderStatus::OUT_FOR_DELIVERY => 'Out For Delivery',
        };
    }

    /**
     * @return Collection<int, string>
     */
    public static function whereIn(mixed $keys): Collection
    {
        return OrderStatus::all()
            ->filter(fn ($value, $key) => in_array($key, Arr::wrap($keys)));
    }

    public static function get(int $status_id): ?string
    {
        return OrderStatus::tryFrom($status_id)?->label();
    }

    public static function confirmed(): int
    {
        return OrderStatus::CONFIRMED->value;
    }

    public static function processing(): int
    {
        return OrderStatus::PROCESSING->value;
    }

    public static function packed(): int
    {
        return OrderStatus::PACKED->value;
    }

    public static function pickedUp(): int
    {
        return OrderStatus::PICKED_UP->value;
    }

    public static function completed(): int
    {
        return OrderStatus::COMPLETED->value;
    }

    public static function onHold(): int
    {
        return OrderStatus::ON_HOLD->value;
    }

    public static function canceled(): int
    {
        return OrderStatus::CANCELED->value;
    }

    public static function preOrder(): int
    {
        return OrderStatus::PRE_ORDER->value;
    }

    public static function outForDelivery(): int
    {
        return OrderStatus::OUT_FOR_DELIVERY->value;
    }
}
