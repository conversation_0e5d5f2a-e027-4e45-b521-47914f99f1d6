<?php

namespace App\Exports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class DeliverySalesExport implements FromCollection, WithHeadings, WithMapping
{
    use Exportable;

    public function __construct(
        protected Collection $locations,
    ) {}

    public function collection(): Collection
    {
        return $this->locations;
    }

    public function headings(): array
    {
        return [
            'Delivery method',
            'Order count',
            'Weight',
            'Subtotal',
            'Fees',
            'Discounts',
            'Credits',
            'Coupons',
            'Taxes',
            'Subscription savings',
            'Total',
        ];
    }

    public function map($row): array
    {
        $subtotal = $row->subtotal ?? 0;
        $fee_total = $row->fee_total ?? 0;
        $discount_total = $row->discount_total ?? 0;
        $credit_total = $row->credit_total ?? 0;
        $coupon_total = $row->coupon_total ?? 0;
        $sales_tax_total = $row->sales_tax_total ?? 0;
        $subscription_savings_total = $row->subscription_savings_total ?? 0;

        return [
            $row->location,
            $row->order_count,
            $row->weight,
            $subtotal > 0 ? money($subtotal) : '0',
            $fee_total > 0 ? money($fee_total) : '0',
            $discount_total > 0 ? money(-$discount_total) : '0',
            $credit_total > 0 ? money(-$credit_total) : '0',
            $coupon_total > 0 ? money(-$coupon_total) : '0',
            $sales_tax_total > 0 ? money($sales_tax_total) : '0',
            $subscription_savings_total > 0 ? money(-$subscription_savings_total) : '0',
            money($subtotal + $fee_total - $discount_total - $credit_total - $coupon_total + $sales_tax_total - $subscription_savings_total),
        ];
    }
}