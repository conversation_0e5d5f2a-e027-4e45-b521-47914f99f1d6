<?php

namespace App\Exports;

use App\Models\Lead;
use Illuminate\Database\Eloquent\Builder;

class LeadsExport
{
    protected $weightUnit = 'lb';
    protected $chunkSize = 500;
    protected $fileName = 'customer_leads.csv';

    public function export(Builder $builder)
    {
        $rowHeaders = [
            'email',
            'city',
            'state',
            'zip',
            'created_at'
        ];

        $select = [
            'email',
            'city',
            'state',
            'zip',
            'created_at'
        ];

        return response()->stream(function () use ($rowHeaders, $builder, $select) {
            $export = fopen('php://output', 'w');
            fputcsv($export, $rowHeaders);
            $builder->select($select)->chunk($this->chunkSize, function ($resourceChunk) use ($export) {
                foreach ($resourceChunk as $resource) {
                    /** @var Lead $resource */

                    fputcsv($export, [
                        $resource->email,
                        $resource->city,
                        $resource->state,
                        $resource->zip,
                        $resource->created_at,
                    ]);
                }
            });
            fclose($export);
        }, 200, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $this->fileName . '"',
        ]);
    }
}
