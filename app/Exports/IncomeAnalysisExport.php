<?php

namespace App\Exports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class IncomeAnalysisExport implements FromCollection, WithHeadings, WithMapping
{
    use Exportable;

    public function __construct(
        protected Collection $line_item_revenue,
        protected Collection $fee_revenue,
        protected Collection $deductions,
    ) {}

    public function collection(): Collection
    {
        return $this->line_item_revenue
            ->merge($this->fee_revenue)
            ->merge($this->deductions);
    }

    public function headings(): array
    {
        return [
            'Accounting class',
            'Online store weight',
            'Online store total',
            'Wholesale weight',
            'Wholesale total',
            'Distribution weight',
            'Distribution total',
            'Farm store weight',
            'Farm store total',
            'Farmers market weight',
            'Farmers market total',
            'Affiliate weight',
            'Affiliate total',
            'Buying clubs weight',
            'Buying clubs total',
            'Home delivery weight',
            'Home delivery total',
            'Shipping weight',
            'Shipping total',
            'POS weight',
            'POS total',
            'Combined weight',
            'Combined total',
        ];
    }

    public function map($row): array
    {
        if (in_array($row->accounting_class, $this->paddedAccountingClasses())) {
            return $this->mapPaddedRow($row, ! in_array($row->accounting_class, ['Sales tax', 'Fee income']));
        }

        return $this->mapRevenueRow($row);
    }

    private function mapRevenueRow($row): array
    {
        return [
            $row->accounting_class,
            $row->channel_online_store_weight,
            money($row->channel_online_store_total),
            $row->channel_wholesale_weight,
            money($row->channel_wholesale_total),
            $row->channel_distribution_weight,
            money($row->channel_distribution_total),
            $row->channel_farm_store_weight,
            money($row->channel_farm_store_total),
            $row->channel_farmers_market_weight,
            money($row->channel_farmers_market_total),
            $row->channel_affiliate_weight,
            money($row->channel_affiliate_total),
            $row->channel_buying_clubs_weight,
            money($row->channel_buying_clubs_total),
            $row->channel_home_delivery_weight,
            money($row->channel_home_delivery_total),
            $row->channel_shipping_weight,
            money($row->channel_shipping_total),
            $row->channel_pos_weight,
            money($row->channel_pos_total),
            $row->channel_combined_weight,
            money($row->channel_combined_total),
        ];
    }

    private function mapPaddedRow($row, $is_deduction = true): array
    {
        return [
            $row->accounting_class,
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            money($row->total * ($is_deduction ? -1 : 1)) ,
        ];
    }

    private function paddedAccountingClasses(): array
    {
        return [
            'Fee income',
            'Discounts',
            'Credits',
            'Coupons',
            'Sales tax',
            'Subscription savings'
        ];
    }
}