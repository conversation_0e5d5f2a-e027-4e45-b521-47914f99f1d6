<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;

class GoogleSheetsService
{
    private string $baseUrl = 'https://sheets.googleapis.com/v4/spreadsheets/';
    private string $apiKey;

    public function __construct()
    {
        $this->apiKey = config('services.google.sheets_api_key');
    }

    public function getWorksheetContents(string $spreadsheet_id, string $worksheet_name): array
    {
        $response = Http::get("{$this->baseUrl}{$spreadsheet_id}/values/{$worksheet_name}", [
            'key' => $this->apiKey,
        ]);

        if ($response->failed()) {
            throw new \Exception('Failed to fetch Google Sheet data: ' . $response->body());
        }

        return $response->json()['values'] ?? [];
    }
}
