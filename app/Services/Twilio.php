<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;

class Twilio
{
    /**
     * @throws \Illuminate\Http\Client\RequestException
     */
    public function lookup(string $phone_number): array
    {
        return Http::withBasicAuth(
            config('services.twilio.connections.twilio.sid'),
            config('services.twilio.connections.twilio.token')
        )
            ->get("https://lookups.twilio.com/v2/PhoneNumbers/{$phone_number}?Fields=line_type_intelligence")
            ->throw()
            ->json();
    }
}
