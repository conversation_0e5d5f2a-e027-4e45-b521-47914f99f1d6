<?php

namespace App\Services;

use App\Cart\Item;
use App\Contracts\Cartable;
use App\Contracts\CartService;
use App\Models\Cart;
use App\Models\Order;
use App\Models\Pickup;
use App\Models\RecurringOrder;
use App\Services\Geocoding\GeocodedAddress;
use Illuminate\Support\Facades\Cache;

class StoreService
{
    public function rating(): ?float
    {
        return $this->placeDetails()['rating'] ?? null;
    }

    protected function placeDetails(): array
    {
        return Cache::tags('store')
            ->remember('google-place-details', 3600, fn() =>
                app(GooglePlaces::class)
                    ->fetchDetails(config('services.google.place_id'))
            );
    }

    public function reviewCount(): ?int
    {
        return $this->placeDetails()['user_ratings_total'] ?? null;
    }

    public function cartOrCartStub(): Cartable
    {
        return once(function () {
            $cart = $this->cart();

            if (! is_null($cart)) {
                return $cart;
            }

            return Cart::stub(auth()->user());
        });
    }

    public function cart(): ?Cartable
    {
        return once(function () {
            $shopper = $this->shopper();

            return app(CartService::class)
                ->find(shopper_type: $shopper['type'], shopper_id: $shopper['id']);
        });
    }

    public function shopper()
    {
        return once(fn() => request()->shopper());
    }

    public function cartItemCount(): int
    {
        return once(fn() => $this->cart()?->itemsInCart()->sum(fn(Item $item) => $item->quantity) ?? 0);
    }

    public function orderItemCount(): int
    {
        return once(fn() => $this->orderWithoutDeliveryMethodCheck()?->items()->sum('order_items.qty') ?? 0);
    }

    public function orderWithoutDeliveryMethodCheck(): ?Order
    {
        return once(fn() => auth()->user()?->queryOpenOrder());
    }

    public function mostRecentSubscription(): ?RecurringOrder
    {
        return once(fn() => auth()->user()?->recurringOrder()->withTrashed()->latest('id')->first());
    }

    public function subscriptionItemCount(): int
    {
        return once(fn() => $this->subscription()?->items()->sum('qty') ?? 0);
    }

    public function subscription(): ?RecurringOrder
    {
        return once(fn() => auth()->user()?->recurringOrder()->first());
    }

    public function deliveryMethod(?int $default = null): ?Pickup
    {
        return once(function () use ($default) {
            $order = $this->order();
            if (!is_null($order)) {
                return $order->pickup;
            }

            $subscription = $this->subscription();
            if (!is_null($subscription)) {
                return $subscription->fulfillment;
            }

            $cart = $this->cart();
            if (!is_null($cart)) {
                return $cart->cartLocation();
            }

            if (auth()->user()) {
                $shipping = auth()->user()->defaultShippingAttributes();
                $delivery_method = app(DeliveryMethodService::class)
                    ->deliveryZones()
                    ->find(new GeocodedAddress(
                        0.0,
                        0.0,
                        $shipping['city'],
                        $shipping['state'],
                        $shipping['postal_code'],
                        $shipping['country'],
                        1,
                    ))
                    ->first();

                if (!is_null($delivery_method)) {
                    return $delivery_method;
                }
            }

            if (!is_null($default)) {
                return Pickup::find($default);
            }

            return null;
        });
    }

    public function order(): ?Order
    {
        return once(fn() => auth()->user()?->openOrder());
    }

    public function currentPostalCode(?string $default = null): ?string
    {
        return once(function () use ($default) {
            $order = $this->order();
            if (!is_null($order)) {
                return $order->shipping_zip ?? null;
            }

            if (auth()->user()?->hasRecurringOrder() ?? false) {
                return auth()->user()->defaultShippingAttributes()['postal_code'] ?? null;
            }

            $postal_code = null;

            $cart = $this->cart();
            if (!is_null($cart)) {
                $postal_code = $cart->getShippingInfo()['zip'] ?? null;
            }

            if (empty($postal_code) && auth()->user()) {
                $postal_code = auth()->user()->defaultShippingAttributes()['postal_code'] ?? null;
            }

            if (empty($postal_code) && !is_null($default)) {
                $postal_code = $default;
            }

            return !empty($postal_code) ? $postal_code : null;
        });
    }

    public function currentCity(?string $default = null): ?string
    {
        return once(function () use ($default)  {
            $order = $this->order();
            if (!is_null($order)) {
                return $order->shipping_city ?? null;
            }

            if (auth()->user()?->hasRecurringOrder() ?? false) {
                return auth()->user()->defaultShippingAttributes()['city'] ?? null;
            }

            $city = null;

            $cart = $this->cart();
            if (!is_null($cart)) {
                $city = $cart->getShippingInfo()['city'] ?? null;
            }

            if (empty($city) && auth()->user()) {
                $city = auth()->user()->defaultShippingAttributes()['city'] ?? null;
            }

            if (empty($city) && !is_null($default)) {
                $city = $default;
            }

            return !empty($city) ? $city : null;
        });
    }
}
