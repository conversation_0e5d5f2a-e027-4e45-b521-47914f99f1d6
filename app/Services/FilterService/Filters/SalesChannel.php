<?php

namespace App\Services\FilterService\Filters;

use App\Support\Enums\Channel;
use Illuminate\Http\Request;

class SalesChannel extends Filter
{
    public static string $query_param = 'order_type_id';

    protected string $label = 'Sales Channel:';

    public function setValue(Request $request): void
    {
        $id = $request->get(static::$query_param);

        if (is_null($id)) return;

        $this->value = Channel::whereIn(\Arr::wrap($id))
            ->implode(', ');
    }
}
