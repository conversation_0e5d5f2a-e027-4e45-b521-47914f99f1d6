<?php

namespace App\Services\FilterService\Filters;

use Illuminate\Http\Request;

class PickupDate extends Filter
{
    public static string $query_param = 'pickup_date';

    protected string $label = 'Pickup Date:';

    public function setValue(Request $request): void
    {
        $date = $request->get(static::$query_param);

        if (is_null($date)) return;

        $this->value = implode(' - ', \Arr::wrap($date));
    }
}