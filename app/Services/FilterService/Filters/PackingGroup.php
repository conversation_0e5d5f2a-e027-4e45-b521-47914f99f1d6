<?php

namespace App\Services\FilterService\Filters;

use Illuminate\Http\Request;
use Illuminate\Support\Arr;

class PackingGroup extends Filter
{
    public static string $query_param = 'inventory_type';

    protected string $label = 'Packing Group:';

    public function setValue(Request $request): void
    {
        $id = $request->get(static::$query_param);

        if (is_null($id)) return;

        $this->value = \App\Models\PackingGroup::whereIn('id', Arr::wrap($id))
            ->pluck('title', 'id')
            ->implode(', ');
    }
}
