<?php

namespace App\Services\FilterService\Filters;

use App\Models\Tag;
use Illuminate\Http\Request;

class Tags extends Filter
{
    public static string $query_param = 'tags';

    protected string $label = 'Tags:';

    public function setValue(Request $request): void
    {
        $id = $request->get(static::$query_param);

        if (is_null($id)) return;

        $this->value = Tag::whereIn('id', \Arr::wrap($id))
            ->pluck('title')
            ->implode(', ');
    }
}