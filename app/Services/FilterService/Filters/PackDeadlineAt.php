<?php

namespace App\Services\FilterService\Filters;

use Illuminate\Http\Request;

class PackDeadlineAt extends Filter
{
    public static string $query_param = 'pack_deadline_at';

    protected string $label = 'Pack Deadline:';

    public function setValue(Request $request): void
    {
        $date = $request->get(static::$query_param);

        if (is_null($date)) return;

        $this->value = implode(' - ', \Arr::wrap($date));
    }
}