<?php

namespace App\Services\FilterService\Filters;

use App\Models\Tag;
use Illuminate\Http\Request;

class OrderTags extends Filter
{
    public static string $query_param = 'order_tags';

    protected string $label = 'Order Tags:';

    public function setValue(Request $request): void
    {
        $id = $request->get(static::$query_param);

        if (is_null($id)) return;

        $this->value = Tag::whereType(Tag::type('order'))
            ->whereIn('id', \Arr::wrap($id))
            ->pluck('title')
            ->implode(', ');
    }
}