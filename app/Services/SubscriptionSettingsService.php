<?php

namespace App\Services;

use App\Models\Product;
use App\Models\Schedule;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;

class SubscriptionSettingsService
{
    private ?Schedule $schedule = null;

    /**
     * @return Collection<int, Product>
     */
    public function excludedProducts(): Collection
    {
        $excluded_ids = $this->excludedProductIds();

        if ($excluded_ids->isEmpty()) return collect();

        return Product::findMany($excluded_ids);
    }

    /**
     * @return Collection<int, int>
     */
    public function excludedProductIds(): Collection
    {
        /** @var array $ids_array */
        $ids_array = json_decode(setting('recurring_orders_excluded_products', '[]'), true);

        return collect($ids_array)->map(fn($id) => (int) $id);
    }

    public function hasProductIncentive(): bool
    {
        return ! is_null($this->defaultProductIncentiveId());
    }

    public function defaultProductIncentiveId(): ?int
    {
        $value = (int) setting('recurring_orders_default_item');
        return $value === 0 ? null : $value;
    }

    public function usingSchedule(Schedule $schedule): SubscriptionSettingsService
    {
        $this->schedule = $schedule;
        return $this;
    }

    public function toArray(): array
    {
        return [
            'discount_incentive' => $this->discountIncentive(),
            'discount_incentive_applies_to_one_time_items' => true,
            'product_incentive_default' => $this->defaultProductIncentive(),
            'product_incentive_alternative_one' => $this->alternativeOneProductIncentive(),
            'product_incentive_alternative_two' => $this->alternativeTwoProductIncentive(),
            'excluded_product_ids' => $this->excludedProductIds()->toArray(),
            'available_frequencies' => $this->schedule?->availableSubscriptionFrequencies() ?? collect(),
            'default_frequency' => $this->schedule?->defaultSubscriptionFrequency()
        ];
    }

    public function discountIncentive(): int
    {
        return 5;
    }

    public function defaultProductIncentive(): ?Product
    {
        $product_id = $this->defaultProductIncentiveId();

        if (is_null($product_id)) return null;

        return Product::find($product_id);
    }

    public function alternativeOneProductIncentive(): ?Product
    {
        $product_id = $this->alternativeOneProductIncentiveId();

        if (is_null($product_id)) return null;

        return Product::find($product_id);
    }

    public function alternativeOneProductIncentiveId(): ?int
    {
        $value = (int) setting('recurring_orders_option_one');
        return $value === 0 ? null : $value;
    }

    public function alternativeTwoProductIncentive(): ?Product
    {
        $product_id = $this->alternativeTwoProductIncentiveId();

        if (is_null($product_id)) return null;

        return Product::find($product_id);
    }

    public function alternativeTwoProductIncentiveId(): ?int
    {
        $value = (int) setting('recurring_orders_option_two');
        return $value === 0 ? null : $value;
    }

    /**
     * @return Collection<int, int>
     */
    public function productIncentiveIds(): Collection
    {
        return once(function () {
            $ids = collect();

            if ( ! is_null($this->defaultProductIncentiveId())) {
                $ids->push($this->defaultProductIncentiveId());
            }

            if ( ! is_null($this->alternativeOneProductIncentiveId())) {
                $ids->push($this->alternativeOneProductIncentiveId());
            }

            if ( ! is_null($this->alternativeTwoProductIncentiveId())) {
                $ids->push($this->alternativeTwoProductIncentiveId());
            }

            return $ids;
        });

    }

    public function welcomeEmailTemplateId(): ?int
    {
        $id = setting('recurring_orders_welcome_email_template_id');

        if (! $id) return null;

        return (int) $id;
    }

    public function deadlineEmailIsEnabled(): bool
    {
        return (bool) setting('recurring_orders_deadline_email_enabled');
    }

    public function deadlineEmailTemplateId(): ?int
    {
        $id = setting('recurring_orders_deadline_email_template_id');

        if (! $id) return null;

        return (int) $id;
    }

    public function deadlineSmsIsEnabled(): bool
    {
        return (bool) setting('recurring_orders_deadline_sms_enabled');
    }

    public function deadlineReminderSendsDaysBefore(): int
    {
        return (int) setting('recurring_orders_deadline_days_before');
    }

    public function deadlineReminderSendsHoursBefore(): int
    {
        return (int) setting('recurring_orders_deadline_hours_before');
    }

    public function confirmationSmsIsEnabled(): bool
    {
        return (bool) setting('recurring_orders_reorder_sms_enabled');
    }

    public function confirmationEmailTemplateId(): ?int
    {
        $id = setting('recurring_orders_reorder_email_template_id');

        if (! $id) return null;

        return (int) $id;
    }

    public function autoConfirmationDeadlineDate(): Carbon
    {
        $confirmation_date = today();

        $deadline_hour = app(SettingsService::class)->deadlineHour();

        if ($deadline_hour === 24) {
            $deadline_hour = 0;
            $confirmation_date->subDay();
        }

        if ($deadline_hour > now()->hour) {
            $confirmation_date->subDay();
        }

        return $confirmation_date->subDays($this->inventoryManagementDayCount());
    }

    public function inventoryManagementDayCount(): int
    {
        return 3;
    }
}
