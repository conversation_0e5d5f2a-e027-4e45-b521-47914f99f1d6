<?php

namespace App\Listeners\User\UserWasRegistered;

use App\Events\User\UserWasRegistered;
use App\Services\MetaConversionService;
use Illuminate\Contracts\Queue\ShouldQueue;

class SendCompleteRegistrationMetaConversionEvent implements ShouldQueue
{
    public function handle(UserWasRegistered $event): void
    {
        app(MetaConversionService::class)->trackCompleteRegistration($event->user);
    }
}
