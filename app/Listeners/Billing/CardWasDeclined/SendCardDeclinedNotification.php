<?php

namespace App\Listeners\Billing\CardWasDeclined;

use App\Events\Billing\CardWasDeclined;
use App\Mail\CreditCardDeclined;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Mail;

class SendCardDeclinedNotification implements ShouldQueue
{
    public function handle(CardWasDeclined $event): void
    {
        Mail::to($event->order->customer_email ?: $event->order->customer->email)
            ->send(new CreditCardDeclined($event->order->id));
    }
}
