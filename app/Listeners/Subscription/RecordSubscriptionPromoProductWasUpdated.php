<?php

namespace App\Listeners\Subscription;

use App\Events\Subscription\SubscriptionPromoProductWasUpdated;
use App\Models\Event;
use App\Models\RecurringOrder;

class RecordSubscriptionPromoProductWasUpdated
{
    public function handle(SubscriptionPromoProductWasUpdated $event)
    {
        Event::create([
            'user_id' => auth()->id() ?? $event->subscription->customer_id,
            'model_id' => $event->subscription->id,
            'model_type' => RecurringOrder::class,
            'event_id' => SubscriptionPromoProductWasUpdated::class,
            'description' => 'The customer updated their subscription promo product.',
            'metadata' => json_encode([
                'old_promo_product_id' => $event->old_promo_product_id,
                'new_promo_product_id' => $event->new_promo_product_id
            ]),
            'created_at' => now()
        ]);
    }
}
