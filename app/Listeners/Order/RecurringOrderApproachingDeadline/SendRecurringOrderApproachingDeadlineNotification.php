<?php

namespace App\Listeners\Order\RecurringOrderApproachingDeadline;

use App\Events\Subscription\RecurringOrderApproachingDeadline;
use App\Notifications\RecurringOrderDeadlineReminder;

class SendRecurringOrderApproachingDeadlineNotification
{
    public function handle(RecurringOrderApproachingDeadline $event): void
    {
        $sends_at = now();

        if ($this->sendsViaSms() && $this->isQuietHours()) {
            $sends_at = $sends_at->next('08:00');
        }

        $event->order->notify(
            (new RecurringOrderDeadlineReminder($event->schedule->id))->delay($sends_at)
        );
    }

    private function sendsViaSms(): bool
    {
        return (bool) setting('recurring_orders_deadline_sms_enabled', false);
    }

    private function isQuietHours(): bool
    {
        $now = now();

        return $now->hour >= 22 || $now->hour < 8;
    }
}
