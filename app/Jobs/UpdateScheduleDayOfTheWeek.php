<?php

namespace App\Jobs;

use App\Models\Date;
use App\Models\Schedule;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Carbon;

class UpdateScheduleDayOfTheWeek implements ShouldQueue
{
    use Queueable;

    public function __construct(public Schedule $schedule)
    {}

    public function handle(): void
    {
        $this->schedule->extra_attributes['days_of_week'] = Date::where('schedule_id', $this->schedule->id)
            ->latest('pickup_date')
            ->limit(7)
            ->get()
            ->map(fn(Date $date) => Carbon::parse($date->delivery_date)->dayOfWeek)
            ->unique()
            ->toArray();

        $this->schedule->save();
    }
}
