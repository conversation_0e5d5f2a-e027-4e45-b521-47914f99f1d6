<?php

namespace App\Jobs;

use App\Exports\QuickBooks\InvoiceExport;
use App\Models\Order;
use App\Notifications\OrderCreditReport;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Notification;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Storage;

class SendOrderCreditReport implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function handle(): void
    {
        $recipient_emails = config('mail.notifications.order_credit_report');

        if (empty($recipient_emails)) {
            return;
        }

        $filepath = $this->generateReport();

        if (empty($filepath)) {
            return;
        }

        foreach ($recipient_emails as $recipient) {
            Notification::route('mail', $recipient)
                ->notify(new OrderCreditReport($filepath));
        }
    }

    private function generateReport(): ?string
    {
        $startOfMonth = now('America/New_York')->subMonth()->startOfMonth();
        $endOfMonth = now('America/New_York')->subMonth()->endOfMonth();

        $orderQuery = Order::query()
            ->where('credit_applied', '>', 0)
            ->where('canceled', 0)
            ->whereBetween('pickup_date', [$startOfMonth, $endOfMonth]);

        $timestamp = now()->format('Y_m_d_h_i');

        $filepath = config('filesystems.file_upload_prefix') . "/reports/order_credit_report_{$timestamp}.csv";

        $export = new InvoiceExport($orderQuery);
        
        Excel::store($export, $filepath, 's3');

        if (!Storage::disk('s3')->exists($filepath)) {
            return null;
        }

        return $filepath;
    }
}
