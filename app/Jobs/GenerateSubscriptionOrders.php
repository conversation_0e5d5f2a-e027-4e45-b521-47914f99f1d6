<?php

namespace App\Jobs;

use App\Models\RecurringOrder;
use App\Traits\TenantContextMiddleware;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;

class GenerateSubscriptionOrders implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, TenantContextMiddleware;

    public function handle(): void
    {
        RecurringOrder::query()
            ->whereNull('paused_at')
            ->whereNotNull('ready_at')
            ->where('generate_at', '<=', now())
            ->chunkById(
                150,
                fn(Collection $subscriptions) =>
                    $subscriptions->each(fn(RecurringOrder $subscription) =>
                        $subscription->generateNextOrder()
                    )
            );
    }

    public function tags(): array
    {
        return ['subscriptions'];
    }
}
