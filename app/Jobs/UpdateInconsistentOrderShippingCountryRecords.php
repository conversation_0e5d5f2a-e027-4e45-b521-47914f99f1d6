<?php

namespace App\Jobs;

use App\Models\Order;
use App\Services\SettingsService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
class UpdateInconsistentOrderShippingCountryRecords implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function handle(): void
    {
        Order::query()
            ->whereNot('shipping_country', app(SettingsService::class)->farmCountry())
            ->orWhereNull('shipping_country')
            ->update(['shipping_country' => app(SettingsService::class)->farmCountry()]);
    }
}
