<?php

namespace App\Jobs;

use App\Actions\Subscription\SyncSubscriptionDatetimes as SyncSubscriptionDatetimesAction;
use App\Models\RecurringOrder;
use App\Traits\TenantContextMiddleware;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SyncSubscriptionDatetimes implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, TenantContextMiddleware;

    public function __construct(
        public int $subscription_id,
        public string $current_pickup_date,
    ) {}

    public function handle(): void
    {
        /** @var RecurringOrder|null $subscription */
        $subscription = RecurringOrder::find($this->subscription_id);

        if (is_null($subscription)) return;

        $current_delivery_date = Carbon::parse($this->current_pickup_date);
        app(SyncSubscriptionDatetimesAction::class)->handle(
            subscription: $subscription,
            new_delivery_date: $current_delivery_date->copy()->addDays($subscription->reorder_frequency)
        );
    }
}
