<?php

namespace App\Mailers;

use App\Contracts\Mailer;
use App\Services\SettingsService;
use Carbon\Carbon;
use Mailgun\Mailgun;

class MailgunMailer implements Mailer
{
    private string $domain;

    private Mailgun $mailgun;

    private string $from;

    private string $from_name;

    private ?string $reply_to = null;

    public function __construct()
    {
        $this->domain = config('services.mailgun.marketing_domain');
        $this->mailgun = Mailgun::create(config('services.mailgun.secret'));
    }

    public function from(string $from, string $from_name): self
    {
        $this->from = $from;
        $this->from_name = $from_name;

        return $this;
    }

    public function replyTo(string $reply_to): self
    {
        $this->reply_to = $reply_to;

        return $this;
    }

    public function send(array $params)
    {
        $message = [
            'from' => $this->from_name . " <{$this->from}>",
            'to' => $params['recipients'],
            'subject' => $params['subject'],
            'html' => $params['html'],
            'text' => $params['text'] ?? null,
            'h:Reply-To' => $this->reply_to ?: null,
            'o:deliverytime' => $params['deliverytime'] ?? Carbon::now()->toRfc2822String(),
            'recipient-variables' => json_encode($params['recipient_variables'] ?? []),
            'o:tag' => $params['tags'] ?? [],
        ];

        return $this->mailgun
            ->messages()
            ->send($this->domain, $message);
    }

    public function getMergeTags(): array
    {
        return [
            '{customer_first_name}' => '%recipient.first_name%',
            '{customer_last_name}' => '%recipient.last_name%',
            '{customer_name}' => '%recipient.first_name% %recipient.last_name%',
            '{full_name}' => '%recipient.full_name%',
            '{store_credit}' => '%recipient.credit%',
            '{pickup_title}' => '%recipient.pickup_title%',
            '{pickup_subtitle}' => '%recipient.pickup_subtitle%',
            '{pickup_street}' => '%recipient.pickup_street%',
            '{pickup_street_2}' => '%recipient.pickup_street_2%',
            '{pickup_city}' => '%recipient.pickup_city%',
            '{pickup_state}' => '%recipient.pickup_state%',
            '{pickup_zip}' => '%recipient.pickup_zip%',
            '{pickup_address}' => '%recipient.pickup_address%',
            '{pickup_times}' => '%recipient.pickup_times%',
            '{pickup_deadline}' => '%recipient.pickup_deadline%',
            '{order_deadline}' => '%recipient.pickup_deadline%',
            '{pickup_date}' => '%recipient.pickup_next_date%',
            '{pickup_next_date}' => '%recipient.pickup_next_date%',
            '{farm_name}' => setting('farm_name'),
            '{farm_phone}' => setting('farm_phone'),
            '{farm_street}' => setting('farm_street'),
            '{farm_city}' => setting('farm_city'),
            '{farm_state}' => setting('farm_state'),
            '{farm_zip}' => setting('farm_zip'),
            '{farm_postal_code}' => setting('farm_zip'),
            '{farm_address}' => setting('farm_street') . ', ' . setting('farm_city') . ', ' . setting('farm_state') . ' ' . setting('farm_zip'),
            '{order_deadline_end_time}' => app(SettingsService::class)->formattedDeadlineEndTime(),
        ];
    }
}
