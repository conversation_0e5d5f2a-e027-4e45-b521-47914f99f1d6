<?php

namespace App\Observers;

use App\Actions\UpdateSitemap;
use App\Models\Vendor;
use Illuminate\Support\Facades\Cache;

class VendorObserver
{
    /**
     * Listen to the Vendor saving event.
     */
    public function saving(Vendor $vendor): void
    {
        Cache::tags('vendor')->flush();
        (new UpdateSitemap())->execute();
    }

    /**
     * Listen to the Vendor deleting event.
     */
    public function deleting(Vendor $vendor): void
    {
        Cache::tags('vendor')->flush();
        (new UpdateSitemap())->execute();
    }
}
