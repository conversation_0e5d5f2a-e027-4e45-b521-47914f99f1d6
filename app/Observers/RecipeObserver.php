<?php

namespace App\Observers;

use App\Actions\UpdateSitemap;
use App\Models\Recipe;
use Illuminate\Support\Facades\Cache;

class RecipeObserver
{
    public function saving(Recipe $recipe): void
    {
        Cache::tags('recipe')->flush();
        Cache::tags('sitemap')->flush();

        (new UpdateSitemap)->execute();
    }

    public function deleting(Recipe $recipe): void
    {
        Cache::tags('recipe')->flush();
        Cache::tags('sitemap')->flush();

        (new UpdateSitemap)->execute();
    }
}
