<?php

namespace App\Observers;

use App\Models\Widget;
use Illuminate\Support\Facades\Cache;

class WidgetObserver
{
    /**
     * Listen to the Widget saving event.
     */
    public function saving(Widget $widget): void
    {
        Cache::tags('widget')->flush();
    }

    /**
     * Listen to the Widget deleting event.
     */
    public function deleting(Widget $widget): void
    {
        Cache::tags('widget')->flush();
    }
}
