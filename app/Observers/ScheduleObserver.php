<?php

namespace App\Observers;

use App\Models\Schedule;
use Illuminate\Support\Facades\Cache;

class ScheduleObserver
{
    /**
     * Listen to the Schedule saving event.
     */
    public function saving(Schedule $schedule): void
    {
        Cache::tags('schedule')->flush();
    }

    /**
     * Listen to the Schedule deleting event.
     */
    public function deleting(Schedule $schedule): void
    {
        Cache::tags('schedule')->flush();
    }
}
