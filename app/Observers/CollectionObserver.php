<?php

namespace App\Observers;

use App\Actions\UpdateSitemap;
use App\Models\Collection;
use App\Models\MenuItem;
use Illuminate\Support\Facades\Cache;

class CollectionObserver
{
    /**
     * Listen to the Collection saving event.
     */
    public function saving(Collection $collection): void
    {
        MenuItem::where('resource_type', 'collection')
            ->where('resource_id', $collection->id)
            ->update([
                'title' => $collection->title,
                'path' => '/store/' . $collection->slug
            ]);

        Cache::tags(['collection', 'menu'])->flush();

        (new UpdateSitemap())->execute();
    }

    /**
     * Listen to the Collection deleting event.
     */
    public function deleting(Collection $collection): void
    {
        MenuItem::where('resource_type', 'collection')
            ->where('resource_id', $collection->id)
            ->delete();

        Cache::tags(['collection', 'menu'])->flush();

        (new UpdateSitemap())->execute();
    }
}
