<template>
	<div class="gc-modal gc-modal-mask" id="updatePaymentModal" @click="hideModal('updatePaymentModal')">
		<div class="gc-modal-wrapper">
			<div class="gc-modal-container" @click.stop>
				<form id="payment-form" novalidate>
					<div class="gc-modal-header">
						Add Credit Card
					</div>
	
					<div class="gc-modal-body">
					
						<div class="form-group">
				            <label>Name On Card</label>
				            <input 
				            	type="text" 
				            	data-stripe="name" 
				            	class="form-control" 
				            	autocorrect="off" 
				            	v-model="cardholder_name" 
				            	id="cardholder_name"
				            >
				        </div>

				         <div id="card-element"></div>
				         <div id="card-errors" role="alert"></div>
					</div>
	
					<div class="gc-modal-footer">
						<button type="button" class="btn btn-alt" @click="hideModal('updatePaymentModal')">Cancel</button>
		                <button 
		                	type="button" 
		                	class="btn btn-action" 
		                	@click="store()"
		                >Add Card</button>
					</div>
				</form>	
			</div>
		</div>
	</div>
</template>

<script>
	export default {
		props: ['order'],

        emits: ['creditCardAdded'],

		data: function() {
			return {
				saving: false,
				card: null,
				cardholder_name: ''
			}
		},

		created: function()
		{
			eventHub.on('updatePaymentModal:opened', function()
			{
				this.initCard()
			}.bind(this));
		},

		methods: {

			store: function()
			{
				let extraDetails = {
					name: this.cardholder_name,
				};

				stripe.createToken(this.card, extraDetails).then(function(result) {
				    if (result.error) {
				      // Inform the user if there was an error
				      var errorElement = document.getElementById('card-errors');
				      errorElement.textContent = result.error.message;
				    } else {
				      // Send the token to your server
				      // stripeTokenHandler(result.token);
				      let payload = {
			          	cardholder_name: this.cardholder_name,
			          	stripeToken: result.token.id
			          }

			        axios.post('/api/users/'+this.order.customer_id+'/cards', payload).then(function(response) {
			            eventHub.emit('hideProgressBar');
			            this.saving = false;
			            eventHub.emit('hideModal', 'updatePaymentModal');
			            this.$emit('creditCardAdded');
			          }.bind(this)).catch(function(error) {
			          	eventHub.emit('hideProgressBar');
			          	eventHub.emit('error', error);
			          	this.saving = false;
			          	alert('There was an error adding your card.');
			          }.bind(this));
					}
				}.bind(this));
			},

	        initCard: function()
	        {
	        	// Create an instance of Elements
				var elements = stripe.elements();

				// Custom styling can be passed to options when creating an Element.
				// (Note that this demo uses a wider set of styles than the guide below.)
				var style = {
				  base: {
				    lineHeight: '24px',
				    fontSmoothing: 'antialiased',
				    fontSize: '18px',
				    '::placeholder': {
				      color: '#888'
				    }
				  },
				  invalid: {
				    color: '#fa755a',
				    iconColor: '#fa755a'
				  }
				};

				// Create an instance of the card Element
				this.card = elements.create('card', {style: style});

				// Add an instance of the card Element into the `card-element` <div>
				this.card.mount('#card-element');

				// Handle real-time validation errors from the card Element.
				this.card.addEventListener('change', function(event) {
				  var displayError = document.getElementById('card-errors');
				  if (event.error) {
				    displayError.textContent = event.error.message;
				  } else {
				    displayError.textContent = '';
				  }
				});
	        }
		}
	}
</script>
