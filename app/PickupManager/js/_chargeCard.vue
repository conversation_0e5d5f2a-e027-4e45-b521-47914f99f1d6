<template>
	<div class="gc-modal gc-modal-mask" id="chargeCardModal" @click="hideModal('chargeCardModal')">
		<div class="gc-modal-wrapper">
			<div class="gc-modal-container" @click.stop>
				
				<div class="gc-modal-header">
					Charge {{ $filters.currency($filters.cents(order.total)) }}
				</div>
	
				<div class="gc-modal-body">
					<div class="form-group">
						<label>Charge the customer's credit card</label>
						<select class="form-control" v-model="cardPayment.payment_source_id">
							<option 
								v-for="paymentMethod in paymentMethods"
								:value="paymentMethod.id" 
								:key="paymentMethod.id"
							>{{ paymentMethod.description }}</option>
						</select>
					</div>
				</div>
	
				<div class="gc-modal-footer">
					<button type="button" class="btn btn-alt" @click="hideModal('chargeCardModal')">Cancel</button>
	                <button type="button" class="btn btn-danger" @click="charge(order)">Charge {{ $filters.currency($filters.cents(order.total)) }}</button>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
	export default {
		props: ['order','paymentMethods'],

		data: function() {
			return {
				cardPayment: {
					payment_source_id: null
				}
			}
		},

		created: function()
		{
			this.cardPayment.payment_source_id = this.order.payment_source_id;
		},

		methods: {
			charge: function(order)
	        {
	        	if(order.paid)
	        	{
	        		eventHub.emit('hideModal', 'chargeCardModal');
	        		return alert('This order has already been marked as paid');
	        	}
	        	else
	        	{
	        		eventHub.emit('showProgressBar');
	        		var payload = {
	        			payment_source_id: this.cardPayment.payment_source_id
	        		};
	        		axios.post('/api/order/'+order.id+'/charge-customer', payload)
	        		.then(function(response) {
	                    order.paid = true;
	                    eventHub.emit('hideProgressBar');
	                    eventHub.emit('hideModal', 'chargeCardModal');
	        		}.bind(this))
	        		.catch(function(error) {
	        			eventHub.emit('error', error);
	        			eventHub.emit('hideProgressBar');
	        		});
	        	}
	        }
		}
	}
</script>