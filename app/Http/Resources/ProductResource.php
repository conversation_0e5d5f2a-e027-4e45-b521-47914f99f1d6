<?php

namespace App\Http\Resources;

use App\Models\Product;
use App\Support\Enums\ProductType;
use Illuminate\Http\Resources\Json\JsonResource;

class ProductResource extends JsonResource
{
    public function toArray($request): array
    {
        /** @var Product $product */
        $product = $this->resource;

        return [
            'id' => $product->id,
            'type_id' => $product->type_id,
            'type' => ProductType::get($product->type_id),
            'name' => $product->title,
            'description' => $product->description,
            'retail_unit_price' => $product->unit_price,
            'retail_sale_unit_price' => $product->sale_unit_price,
            'unit_description' => $product->unit_description,
            'unit_cost' => $product->item_cost,
            'sku' => $product->sku,
            'vendor' => new VendorResource($product->vendor),
            'accounting_class' => $product->accounting_class,
            'storage_location_id' => $product->custom_sort,
            'created_at' => $product->created_at->toIso8601String(),
            'updated_at' => $product->updated_at->toIso8601String(),
        ];
    }
}
