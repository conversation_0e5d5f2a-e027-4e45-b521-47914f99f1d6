<?php

namespace App\Http\Resources;

use App\Models\OrderItem;
use Illuminate\Http\Resources\Json\JsonResource;

class OrderItemResource extends JsonResource
{
    public function toArray($request): array
    {
        /** @var OrderItem $item */
        $item = $this->resource;

        return [
            'id' => $item->id,
            'product' => new ProductResource($item->product),
            'type' => $item->type,
            'quantity' => $item->qty,
            'fulfilled_quantity' => $item->fulfilled_qty,
            'weight' => $item->weight,
            'subtotal' => $item->subtotal,
        ];
    }
}
