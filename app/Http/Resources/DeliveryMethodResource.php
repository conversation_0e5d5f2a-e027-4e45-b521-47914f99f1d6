<?php

namespace App\Http\Resources;

use App\Models\Pickup;
use Illuminate\Http\Resources\Json\JsonResource;

class DeliveryMethodResource extends JsonResource
{
    public function toArray($request): array
    {
        /** @var Pickup $pickup */
        $pickup = $this->resource;

        return [
            'id' => $pickup->id,
            'title' => $pickup->title,
            'type_id' => $pickup->fulfillment_type,
            'status_id' => $pickup->status_id,
        ];
    }
}
