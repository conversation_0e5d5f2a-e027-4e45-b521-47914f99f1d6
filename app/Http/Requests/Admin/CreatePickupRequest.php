<?php

namespace App\Http\Requests\Admin;

use App\Http\Requests\Request;

class CreatePickupRequest extends Request
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'title' => ['required', 'string', 'max:255'],
            'street' => ['nullable', 'string', 'max:255'],
            'street_2' => ['nullable', 'string', 'max:255'],
            'city' => ['required', 'string', 'max:255'],
            'state' => ['required', 'string', 'max:2'],
            'zip' => ['nullable', 'string', 'max:10'],
            'schedule_id' => ['nullable', 'exists:schedules,id'],
        ];
    }

    public function messages(): array
    {
        return [
            'title.required' => 'A location name is required',
        ];
    }
}
