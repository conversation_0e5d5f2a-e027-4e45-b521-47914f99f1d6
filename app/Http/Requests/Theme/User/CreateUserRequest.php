<?php

namespace App\Http\Requests\Theme\User;

use App\Rules\HoneyPot;
use App\Rules\HoneyTime;
use Illuminate\Foundation\Http\FormRequest;

class CreateUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'email' => [
                'required',
                'email',
            ],
            'pickup_point' => [
                'sometimes',
                'required',
                'integer',
            ],
            'first_name' => [
                'sometimes',
                'required',
            ],
            'last_name' => [
                'sometimes',
                'required',
            ],
            'phone' => [
                'sometimes',
                'required',
            ],
            'password' => [
                'sometimes',
                'required',
            ],
            'username' => [new HoneyPot],
            'timestamp' => [new HoneyTime]
        ];
    }

    public function messages(): array
    {
        return [
            'first_name.required' => 'You must provide your first name',
            'last_name.required' => 'You must provide your last name',
            'email.required' => 'You must provide your email',
            'email.unique' => 'This email has already been taken',
            'password.required' => 'You must provide a password',
            'pickup_point.required' => 'You must choose a pickup/delivery option',
        ];
    }
}
