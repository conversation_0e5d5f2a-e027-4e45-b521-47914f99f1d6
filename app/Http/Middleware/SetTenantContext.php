<?php

namespace App\Http\Middleware;

use App\Models\Integration;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class SetTenantContext
{
    public function handle(Request $request, Closure $next): Response
    {
        date_default_timezone_set(setting('timezone', 'America/Fort_Wayne'));
        app()->setLocale(setting('local', 'en'));

        Integration::initializeAll();

        return $next($request);
    }
}
