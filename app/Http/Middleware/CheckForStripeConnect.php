<?php

namespace App\Http\Middleware;

use Symfony\Component\HttpFoundation\Response;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;

class CheckForStripeConnect
{
    public function handle(Request $request, Closure $next): Response
    {
        if ( ! is_null(setting('stripe_user_id'))) {
            return $next($request);
        }

        if ($request->expectsJson()) {
            return response()->json('Stripe account is not connected.', 412);
        }

        error('A Stripe account has not been connected.');
        return to_route('admin.payments.edit', ['id' => 'card']);
    }
}
