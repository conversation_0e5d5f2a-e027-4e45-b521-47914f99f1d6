<?php

namespace App\Http\Middleware\Theme;

use App\Contracts\CartService;
use App\Livewire\Theme\FetchesCart;
use Illuminate\Http\Request;

class EnsureCartIsNotEmpty
{
    use FetchesCart;

    public function handle(Request $request, \Closure $next): \Symfony\Component\HttpFoundation\Response
    {
        $shopper = request()->shopper();

        $cart = app(CartService::class)
            ->find(shopper_type: $shopper['type'], shopper_id: $shopper['id']);

        if (is_null($cart) || $cart->cartIsEmpty()) {
            return redirect(route('cart.show'));
        }

        return $next($request);
    }
}
