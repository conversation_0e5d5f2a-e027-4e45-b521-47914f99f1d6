<?php

namespace App\Http\Middleware\Theme;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cookie;
use Symfony\Component\HttpFoundation\Response;

class TrackView
{
    protected $excluded = [
        'register',
        'register/postal-code-entry',
        'register/contact',
        'advanced-registration',
        'logout',
        'login',
        'advanced-login',
        'choose-password',
        'account',
        'js/bugsnag-vue.js.map',
        'service-worker.js',
        'vendor/livewire/livewire.js.map',
    ];

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Only track GET requests.
        if ($request->method() !== 'GET') {
            return $next($request);
        }

        // If the request has a redirect_to param
        // then let's bake it into a cookie.
        if ($request->has('redirect_to')) {
            Cookie::queue('last_viewed_page', url($request->input('redirect_to')), 60);
            return $next($request);
        }

        // If the URL is not excluded from being tacked then
        // save the URL to the last_viewed_page session.
        if ( ! in_array($request->path(), $this->excluded) && ! str($request->url())->contains(['.js', '/js/', '/livewire'])) {
            Cookie::queue('last_viewed_page', $request->fullUrl(), 60);
        }

        return $next($request);
    }
}
