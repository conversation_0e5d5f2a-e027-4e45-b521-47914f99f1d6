<?php

namespace App\Http\Middleware;

use App\Services\StoreService;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class GetOpenOrder
{
    public function handle(Request $request, Closure $next): Response
    {
        $order = app(StoreService::class)->order();

        $request->macro('order', fn() => $order);

        view()->share(['openOrder' => $order]);

        return $next($request);
    }
}
