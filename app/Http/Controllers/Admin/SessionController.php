<?php

namespace App\Http\Controllers\Admin;

use App\Events\User\AdminLoggedIn;
use App\Http\Controllers\Controller;
use App\Models\User;
use Auth;
use Carbon\Carbon;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;

class SessionController extends Controller
{
    public function index()
    {
        if (auth()->check() && Gate::allows('admin-access')) {
            return redirect()->to('/admin');
        }

        return view('sessions.login');
    }

    public function store(Request $request): RedirectResponse
    {
        $email = $request->get('email');
        $password = $request->get('password');
        if (is_null(User::where('email', $email)->first())) {
            flash('We could not find a user with that email.');

            return redirect()->to('admin/login')->withInput();
        }

        if (! Auth::attempt(['email' => $email, 'password' => $password], true)) {
            flash('The email or password you entered was incorrect.');

            return redirect()->to('admin/login')->withInput();
        }

        $user = auth()->user();
        $user->last_login = Carbon::now();
        $user->save();
        AdminLoggedIn::dispatch($user);

        return redirect()->intended('/admin');
    }

    public function destroy()
    {
        Auth::logout();

        return to_route('admin.login');
    }
}
