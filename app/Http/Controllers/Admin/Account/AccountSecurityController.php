<?php

namespace App\Http\Controllers\Admin\Account;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\View\View;

class AccountSecurityController extends Controller
{
    public function edit(Request $request): View
    {
        return view('account.security')
            ->with([
                'user' => auth()->user(),
            ]);
    }

    public function update(Request $request)
    {
        $request->validate([
            'current_password' => ['required'],
            'password' => ['required', 'confirmed'],
        ], [
            'password.required' => 'The new password field is required.',
        ]);

        $user = auth()->user();

        if (! Hash::check($request->get('current_password'), $user->password)) {
            error('The current password you entered was not correct.');

            return back();
        }

        $user->password = $request->get('password');
        $user->save();

        flash('Your password is updated.');

        return back();
    }
}
