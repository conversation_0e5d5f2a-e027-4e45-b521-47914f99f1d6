<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Filter;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;

class FilterController extends Controller
{
    public function index(): View
    {
        return view('filters.index')
            ->with(['filters' => Filter::orderBy('type')->orderBy('created_at')->get()]);
    }

    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'title' => ['required'],
            'filters' => ['required'],
            'filter_resource' => ['required'],
        ], [
            'title.required' => 'Filter name is required.',
        ]);

        $filter = Filter::create([
            'title' => $request->input('title'),
            'type' => $request->input('filter_resource'),
            'url' => $request->input('filters'),
        ]);

        return redirect($request->get('previous_path').'?'.$filter->url.'&filter_id='.$filter->id);
    }

    public function edit(int $filterId): View
    {
        return view('filters.edit')
            ->with(['filter' => Filter::findOrFail($filterId)]);
    }

    public function update(Request $request, int $filterId)
    {
        $request->validate([
            'title' => 'required',
        ], [
            'title.required' => 'Filter name is required.',
        ]);

        Filter::findOrFail($filterId)->update($request->all());

        return back();
    }

    public function destroy(Request $request, int $filterId): RedirectResponse
    {
        Filter::findOrFail($filterId)->delete();
        if ($request->has('previous_path')) {
            return redirect()->to($request->get('previous_path'));
        }

        return redirect()->to('/admin/filters');
    }
}
