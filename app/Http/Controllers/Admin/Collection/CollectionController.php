<?php

namespace App\Http\Controllers\Admin\Collection;

use App\Http\Controllers\Controller;
use App\Models\Collection;
use App\Models\Menu;
use App\Services\MenuManagerService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use Illuminate\View\View;

class CollectionController extends Controller
{
    public function index(): View
    {
        $collections = Collection::with('products')->get();

        return view('collections.index', compact('collections'));
    }

    public function store(MenuManagerService $menuManager)
    {
        $validated = request()->validate([
            'title' => ['required'],
            'add-to-menu' => ['in:on,off'],
        ]);

        $collection = Collection::create(['title' => $validated['title']]);

        if (isset($validated['add-to-menu']) && $menu = Menu::where('name', 'store')->first()) {
            $menuManager->add('collection', ['items' => [$collection->id], 'menu_id' => $menu->id]);
        }

        $redirect_to = route('admin.collections.edit', compact('collection'));

        if (request()->expectsJson()) {
            return response()->json(['redirect' => $redirect_to]);
        }

        flash('Collection successfully created!');

        return redirect($redirect_to);
    }

    public function show(Request $request, Collection $collection)
    {
        return to_route('admin.collections.edit', $collection);
    }

    public function edit(Collection $collection): View
    {
        return view('collections.edit')
            ->with(compact('collection'));
    }

    public function update(Request $request, Collection $collection)
    {
        $request['slug'] = Str::slug($request->get('slug'));

        $request->validate([
            'title' => ['sometimes', 'required'],
            'slug' => ['unique:collections,slug,'.$collection->id],
            'settings' => ['array'],
            'settings.display_name' => ['nullable', 'string'],
            'settings.summary' => ['nullable', 'string'],
        ]);

        $fields = $request->all();

        // Check empty settings field
        if (! empty($fields['settings']) && empty($fields['settings']['display_name'])) {
            $fields['settings']['display_name'] = null;
        }

        $collection->update($fields);

        Cache::tags(['store', 'collection', 'product'])->flush();

        if ($request->ajax()) {
            return response()->json('Success');
        }

        flash('Collection successfully updated!');

        return back();
    }

    public function destroy(Collection $collection): RedirectResponse
    {
        $collection->delete();

        return redirect(route('admin.collections.index'));
    }
}
