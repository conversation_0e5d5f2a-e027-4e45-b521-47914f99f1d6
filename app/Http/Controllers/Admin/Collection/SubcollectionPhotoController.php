<?php

namespace App\Http\Controllers\Admin\Collection;

use App\Http\Controllers\Controller;
use App\Models\Subcollection;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class SubcollectionPhotoController extends Controller
{
    public function update(Request $request, int $collectionId): JsonResponse
    {
        Subcollection::findOrFail($collectionId)->update([
            'cover_photo' => $request->get('photo_path'),
        ]);

        return response()->json([
            'responseText' => 'Cover photo set.',
        ]);
    }
}
