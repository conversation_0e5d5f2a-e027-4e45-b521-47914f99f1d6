<?php

namespace App\Http\Controllers\Admin\Order;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Repositories\OrderIndexRepository;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class OrderBulkUpdateTagController extends Controller
{
    public function update(Request $request, int $tagId)
    {
        $request->validate([
            'type' => ['nullable', 'in:page,all'],
            'orders' => [Rule::requiredIf($request->get('type') !== 'all'), 'array'],
        ], [
            'orders.required' => 'Please select some orders and try again.',
        ]);

        if ($request->get('type') === 'all') {
            $orderIds = app(OrderIndexRepository::class)
                ->query(collect(session('orders-filtered')))
                ->pluck('id');
        } else {
            $orderIds = $request->get('orders', []);
        }

        $orders = Order::query()
            ->select('id')
            ->with('tags')
            ->whereIn('id', $orderIds)
            ->get();

        foreach ($orders as $order) {
            if ($order->tags->contains(fn($tag) => $tag->id == $tagId)) {
                $order->tags()->detach($tagId);
            } else {
                $order->tags()->attach($tagId);
            }
        }

        flash("{$orders->count()} orders have had their tags updated.");

        return back();
    }
}
