<?php

namespace App\Http\Controllers\Admin\User;

use App\Actions\Billing\RemoveCard;
use App\Billing\Gateway\GatewayException;
use App\Contracts\Billing;
use App\Exceptions\OrderChargeException;
use App\Http\Controllers\Controller;
use App\Models\Card;
use App\Models\User;
use Illuminate\Http\Request;

class UserCardController extends Controller
{
    public function store(User $user, Billing $billing)
    {
        try {
            return $this->storeStripeCard($user);
        } catch (GatewayException $exception) {
            error($exception->getMessage());

            return back();
        }
    }

    private function storeStripeCard(User $user)
    {
        $validated = request()->validate([
            'token' => ['required'],
            'cardholder_name' => ['required'],
            'make_default' => ['nullable'],
        ]);

        try {
            $user->addCard($validated['token'], $validated);
        } catch (OrderChargeException $exception) {
            error($exception->getMessage());

            return back();
        }

        flash('The card has been added to the customer!');

        return back();
    }

    public function destroy(User $user, Card $card)
    {
        abort_if($card->user_id !== $user->id, 409);

        try {
            app(RemoveCard::class)->handle($user->customer_id, $card->source_id);
        } catch (GatewayException $exception) {
            error($exception->getMessage());

            return back();
        }

        flash('The card has been deleted!');

        return back();
    }
}
