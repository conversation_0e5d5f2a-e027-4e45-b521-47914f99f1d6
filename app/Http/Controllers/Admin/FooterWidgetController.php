<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Widget\CreateWidgetRequest;
use App\Http\Requests\Admin\Widget\UpdateWidgetRequest;
use App\Models\Widget;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Cache;
use Illuminate\View\View;

class FooterWidgetController extends Controller
{
    public function index()
    {
        return Widget::where('page_id', 0)->orderBy('sort')->get();
    }

    public function edit(): View
    {
        return view('footer.index');
    }

    public function store(CreateWidgetRequest $request): JsonResponse
    {
        $widget = new Widget($request->safe()->all());
        $widget->settings = $request->get('settings');

        $template = $request->get('template');

        if ($template !== 'RichText') {
            $widget->content = view("theme::widgets.{$template}.{$template}")
                ->with(['widget' => $widget])
                ->render();
        }

        $widget->save();

        return response()->json([
            'id' => null,
        ]);
    }

    public function update(UpdateWidgetRequest $request, Widget $widget): JsonResponse
    {
        $widget->fill($request->safe()->all());

        $widget->settings = array_merge((array) $widget->settings, $request->get('settings', []));

        $template = $request->get('template');

        if (! in_array($template, ['RichText', 'HTML'])) {
            $widget->content = view("theme::widgets.{$template}.{$template}")
                ->with(['widget' => $widget])
                ->render();
        }

        $widget->save();

        return response()->json('Widget updated');
    }

    public function destroy(Widget $widget): JsonResponse
    {
        $widget->delete();

        Cache::tags('widgets')->flush();

        return response()->json('Widget deleted');
    }
}
