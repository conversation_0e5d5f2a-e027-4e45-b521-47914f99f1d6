<?php

namespace App\Http\Controllers\Admin\Reports;

use App\Models\Filter;
use App\Repositories\Reports\AccountCreditAppliedReport;
use Illuminate\Http\Request;

class AccountCreditAppliedReportController
{
    public function __invoke(Request $request)
    {
        $request->mergeIfMissing([
            'created_at.start' => today()->subDays(7)->format('M jS Y'),
            'created_at.end' => today()->format('M jS Y')
        ]);

        $results = (new AccountCreditAppliedReport)->handle(request()->all());

        $filters = Filter::query()
            ->where('type', 'account_credit_applied_report')
            ->get();

        return view('reports.account-credit-applied.index', [
            'results' => $results,
            'savedFilters' => $filters,
            'appliedFilters' => $request->appliedFilters(),
            'appliedFilter' => $filters->where('id', $request->get('filter_id'))->first(),
        ]);
    }
}
