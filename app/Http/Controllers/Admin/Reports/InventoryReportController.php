<?php

namespace App\Http\Controllers\Admin\Reports;

use App\Exports\InventoryExport;
use App\Http\Controllers\Controller;
use App\Models\Filter;
use App\Repositories\Reports\Inventory;
use Illuminate\Http\Request;

class InventoryReportController extends Controller
{
    /**
     * Show inventory sales report.
     *
     * @return mixed
     */
    public function index(Request $request)
    {
        $request->validate([
            'payment_date' => ['array'],
            'payment_date.start' => ['required_with:payment_date.end'],
            'payment_date.end' => ['nullable', 'required_with:payment_date.start', 'after:payment_date.start'],
        ]);

        if (! $request->filled('order_status')) {
            $request->merge(['order_status' => [1, 2]]);
        }
        $filters = Filter::where('type', 'inventory_report')->get();
        $report = (new Inventory())->get($request);

        if ($request->has('export')) {
            return (new InventoryExport)->export($report);
        }

        return view('reports.inventory.index')
            ->with([
                'savedFilters' => $filters,
                'appliedFilters' => $request->appliedFilters(),
                'appliedFilter' => $filters->where('id', $request->get('filter_id'))->first(),
                'results' => $report,
            ]);
    }
}
