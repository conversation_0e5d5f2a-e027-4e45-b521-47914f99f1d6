<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\Collection;
use App\Models\Menu;
use App\Models\Page;
use Illuminate\View\View;

class MenuController extends Controller
{
    public function index(): View
    {
        return view('menus.index')
            ->with(['menus' => Menu::where('submenu', false)->get()]);
    }

    public function edit(int $menuId): View
    {
        $menu = Menu::with('parent', 'items.submenu')->findOrFail($menuId);
        $pages = Page::select(['id', 'title', 'slug', 'path'])->orderBy('title')->get();
        $collections = Collection::select(['id', 'title', 'slug'])->orderBy('title')->get();
        $categories = Category::select(['id', 'name', 'slug'])
            ->whereNull('category_id')
            ->orderBy('name')
            ->get();
        $subcategories = Category::select(['id', 'name', 'slug', 'category_id'])
            ->with('parentCategory')
            ->whereNotNull('category_id')
            ->orderBy('category_id')
            ->orderBy('name')
            ->get();

        return view('menus.edit')
            ->with(compact('menu', 'pages', 'collections', 'categories', 'subcategories'));
    }
}
