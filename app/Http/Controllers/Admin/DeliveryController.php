<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\CreateDeliveryRequest;
use App\Http\Requests\Admin\UpdateDeliveryRequest;
use App\Models\Filter;
use App\Models\Pickup;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;

class DeliveryController extends Controller
{
    public function index(Request $request): View
    {
        $filters = Filter::where('type', 'delivery')->get();

        $request->mergeIfMissing(['orderBy' => 'title', 'sort' => 'asc']);

        return view('logistics.delivery.index')
            ->with([
                'savedFilters' => $filters,
                'appliedFilters' => $request->appliedFilters(),
                'appliedFilter' => $filters->where('id', $request->get('filter_id'))->first(),
                'pickups' => Pickup::filter($request->all())->delivery()->get(),
            ]);
    }

    public function store(CreateDeliveryRequest $request): RedirectResponse
    {
        $delivery = Pickup::create([
            'title' => $request->get('title'),
            'fulfillment_type' => Pickup::FULFILLMENT_TYPE_DELIVERY,
        ]);

        return redirect()->to(route('admin.delivery.edit', compact('delivery')));
    }

    public function update(UpdateDeliveryRequest $request, Pickup $delivery)
    {
        $delivery->update($request->validated());

        return back();
    }

    public function show(Request $request, Pickup $delivery)
    {
        return to_route('admin.delivery.edit', $delivery);
    }

    public function edit(Pickup $delivery): View
    {
        $delivery->load(['schedule.dates']);

        return view('logistics.delivery.edit', ['option' => $delivery]);
    }
}
