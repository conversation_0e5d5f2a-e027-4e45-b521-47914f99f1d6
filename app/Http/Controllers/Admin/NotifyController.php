<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Jobs\SendMarketingEmail;
use App\Models\User;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;

class NotifyController extends Controller
{
    /**
     * Send custom message
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'subject' => 'required',
            'message' => 'required',
            'pickup_id' => 'required',
        ], [
            'pickup_id.required' => 'You must select some recipients.',
        ]);

        $users = User::whereIn('pickup_point', $request->get('pickup_id'))
            ->where('order_deadline_email_reminder', true)
            ->where('email', '!=', '')
            ->select(['id'])
            ->count();

        flash('Your message has been sent to <strong>'.$users.'</strong> recipients.');

        SendMarketingEmail::dispatch(
            $request->get('subject'),
            $request->get('message'),
            $request->get('pickup_id')
        );

        return back();
    }
}
