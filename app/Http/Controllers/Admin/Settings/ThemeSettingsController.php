<?php

namespace App\Http\Controllers\Admin\Settings;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use App\Models\Theme;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class ThemeSettingsController extends Controller
{
    /**
     * Update the Theme settings.
     */
    public function update(Request $request): RedirectResponse
    {
        // Install a new theme if it doesn't exist.
        if (! Theme::find($request->input('settings')['active_theme'])) {
            Theme::install(
                $request->input('settings')['active_theme'],
                'default'
            );
        }

        Setting::updateAll($request->input('settings'));

        Cache::tags(['theme'])->flush();

        return back();
    }
}
