<?php

namespace App\Http\Controllers\Admin\Product;

use App\Http\Controllers\Controller;
use App\Models\Product;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ProductPhotoController extends Controller
{
    public function update(Request $request, int $productId): JsonResponse
    {
        Product::findOrFail($productId)
            ->update($request->only(['cover_photo', 'cover_photo_thumbnail']));

        return response()->json([
            'responseText' => 'Cover photo set.',
        ]);
    }
}
