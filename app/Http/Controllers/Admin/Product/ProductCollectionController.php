<?php

namespace App\Http\Controllers\Admin\Product;

use App\Http\Controllers\Controller;
use App\Models\Collection;
use App\Models\Product;
use Cache;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ProductCollectionController extends Controller
{
    public function index(int $productId)
    {
        $collections = Collection::select(['id', 'title'])->get();
        $product = Product::with('collections')->select(['id', 'title'])->findOrFail($productId);

        foreach ($collections as $index => $collection) {
            if (in_array($collection->id, $product->collections->pluck('id')->toArray())) {
                $collections[$index]['active'] = true;
            } else {
                $collections[$index]['active'] = false;
            }
        }

        return $collections;
    }

    public function store(Request $request, int $productId): JsonResponse
    {
        $collection = Collection::with('products')->findOrFail($request['id']);
        if (! $collection->products->contains($productId)) {
            $collection->products()->attach([$productId]);
            Cache::tags('collection')->flush();
            Cache::tags('product')->flush();
            Cache::tags('store')->flush();
            Cache::tags('tag')->flush();
        }

        return response()->json('Product added to collection.');
    }

    public function destroy(int $productId, int $collectionId): JsonResponse
    {
        $collection = Collection::with('products')->findOrFail($collectionId);
        if ($collection->products->contains($productId)) {
            $collection->products()->detach([$productId]);
            Cache::tags('collection')->flush();
            Cache::tags('product')->flush();
            Cache::tags('store')->flush();
            Cache::tags('tag')->flush();
        }

        return response()->json('Product deleted from collection.');
    }
}
