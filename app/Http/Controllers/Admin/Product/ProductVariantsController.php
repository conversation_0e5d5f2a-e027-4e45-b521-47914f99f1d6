<?php

namespace App\Http\Controllers\Admin\Product;

use App\Http\Controllers\Controller;
use App\Models\Product;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ProductVariantsController extends Controller
{
    public function index($productId): JsonResponse
    {
        $product = Product::with(['variants'])->findOrFail($productId);

        return response()->json($product->variants ?? []);
    }

    public function store(Request $request, Product $product): JsonResponse
    {
        $validatedInput = $request->validate([
            'variant_id' => ['required'],
        ]);

        try {
            $variant = Product::findOrFail($validatedInput['variant_id']);
            $highestSort = DB::table('product_variants')
                ->where('product_id', $product->id)
                ->max('sort');

            if ($product->id !== $variant->id) {
                $product->variants()->syncWithoutDetaching([
                    $variant->id => ['sort' => $highestSort + 1],
                ]);
            }

            return response()->json([
                'responseText' => 'Product variant added.',
                'data' => [
                    'base_product' => $product,
                    'variant' => $variant,
                ],
            ]);
        } catch (ModelNotFoundException $exception) {
            abort(404, $exception->getMessage());
        }
    }

    public function update(Request $request): JsonResponse
    {
        if ($request->input('action') === 'update_sort') {
            foreach ($request->input('sortable_item') as $item) {
                DB::table('product_variants')
                    ->where('product_id', $item['product_id'])
                    ->where('variant_id', $item['variant_id'])
                    ->update(['sort' => $item['sort']]);
            }

            return response()->json([
                'responseText' => 'Product variants sorted.',
            ]);
        }

        return response()->json([
            'responseText' => 'Product variants sorted.',
        ]);
    }

    public function destroy(Product $product, $variantId): JsonResponse
    {
        $product->variants()->detach(Product::findOrFail($variantId));

        return response()->json([
            'message' => 'Variant removed.',
        ]);
    }
}
