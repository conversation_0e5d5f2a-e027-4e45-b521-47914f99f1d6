<?php

namespace App\Http\Controllers\Theme\Customer;

use Illuminate\View\View;
use App\Exceptions\OrderDoesNotMeetRequirementsException;
use App\Http\Controllers\Controller;
use App\Models\Order;

class CustomerOrderController extends Controller
{
    public function index(): View
    {
        return view('theme::customers.orders')
            ->with([
                'completedOrders' => Order::history(auth()->id())->paginate(20),
                'customer' => auth()->user()
            ]);
    }

    public function show(Order $order): View
    {
        abort_if($order->customer_id !== auth()->id(),404);

        $order->load([
            'paymentMethod', 'items.product', 'items.giftCard',
            'pickup' => fn($q) => $q->withTrashed()
        ]);

        return view('theme::customers.order')
            ->with([
                // cannot use "order" as variable name as that will potentially be overwritten by the shared view variable
                'order_history' => $order,
                'customer' => auth()->user()
            ]);
    }

    public function store(Order $order)
    {
        if ($order->customer_id !== auth()->id()) {
            return back();
        }

        try {
            $reorderTask = auth()->user()->reorderFromOrder($order);
        } catch (\Exception $e) {
            error($e->getMessage());
            return back();
        }

        flash($reorderTask->getMessage());

        return redirect(route('cart.show'));
    }

    public function destroy(Order $order)
    {
        $order->cancel();

        flash('Your order has been canceled.');

        return back();
    }
}
