<?php

namespace App\Http\Controllers\Theme\Customer;

use Illuminate\View\View;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class CustomerPasswordController extends Controller
{
    public function show(): View
    {
        return view('theme::customers.password')
            ->with(['customer' => auth()->user()]);
    }

    public function update(Request $request)
    {
        $request->validate([
            'current_password' => ['required'],
            'password' => ['required', 'confirmed'],
        ]);

        $credentials = [
            'email' => auth()->user()->email,
            'password' => $request->get('current_password')
        ];

        if (!auth()->validate($credentials)) {
            error('You must provide your current password.');
            return back();
        }

        $customer = auth()->user();
        $customer->password = $request->get('password');
        $customer->save();

        flash('Your password has been changed.');

        return back();
    }
}
