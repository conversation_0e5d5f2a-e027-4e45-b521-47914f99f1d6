<?php

namespace App\Http\Controllers\Theme\Pages;

use App\Http\Controllers\Controller;
use App\Models\Page;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\View\View;

class PagePreviewController extends Controller
{
    public function show(string $pageId): View
    {
        try {
            /** @var Page $page */
            $page = Page::select(['id', 'title', 'page_title', 'seo_visibility', 'description', 'body', 'layout', 'settings', 'updated_at'])
                ->with('widgets')
                ->where('slug', $pageId)
                ->firstOrFail();
        } catch (ModelNotFoundException $exception) {
            abort(404);
        }

        $html = $page->renderHTML(auth()->check());

        return view('theme::pages.preview')
            ->with(compact('page', 'html'));
    }
}
