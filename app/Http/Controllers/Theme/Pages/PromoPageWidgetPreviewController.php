<?php

namespace App\Http\Controllers\Theme\Pages;

use App\Http\Controllers\Controller;
use App\Models\Page;
use Illuminate\View\View;

class PromoPageWidgetPreviewController extends Controller
{
    public function show(Page $page, string $widget_id): View
    {
        $widget = collect($page->settings->content)
            ->first(fn(array $widget) => $widget['id'] === $widget_id);

        return view('theme::pages.page-widget-preview')
            ->with(compact('page', 'widget'));
    }
}
