<?php

namespace App\Http\Controllers\Theme\Pages;

use Illuminate\Http\Response;
use App\Http\Controllers\Controller;
use App\Models\Page;

class PageStyleController extends Controller
{
    public function __invoke(Page $page): Response
    {
        $page->load('widgets');

        return response(
            resolve('ThemeBuilder')->renderPageStyles($page),
            200,
            [
                'Content-Type' => 'text/css',
                'Cache-Control' => 'max-age=2628000, public'
            ]
        );
    }
}
