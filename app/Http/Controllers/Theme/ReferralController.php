<?php

namespace App\Http\Controllers\Theme;

use Illuminate\Http\RedirectResponse;
use App\Http\Controllers\Controller;

class ReferralController extends Controller
{
    /**
     * Create the referral url and redirect the user to the sign-up page.
     */
    public function store(string $code): RedirectResponse
    {
        return redirect($this->makeUrl($code), 301);
    }

    private function makeUrl(string $code): string
    {
        return route('register') . '?referral_code=' . $code . '&utm_campaign=Referral_Invite&utm_medium=Referral_Program&utm_source=CopyPaste&utm_content=' . $code;
    }
}
