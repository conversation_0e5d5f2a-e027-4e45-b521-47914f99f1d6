<?php

namespace App\Http\Controllers\Theme\Registration;

use App\Events\User\LeadCreated;
use App\Events\User\UserSubscribedToNewsletter;
use App\Http\Controllers\Controller;
use App\Models\Lead;
use App\Models\User;
use App\Rules\AllowedEmailDomain;
use App\Rules\HoneyPot;
use App\Rules\HoneyTime;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cookie;

class LeadRegistrationController extends Controller
{
    public function show()
    {
        if ( ! session()->get('postal_code') || ! session()->get('address')) {
            return redirect(route('register'));
        }

        return view('theme::authentication.register')
            ->with([
                'postal_code' => session('postal_code'),
                'address' => session('address'),
                'location_id' => session('options')['closest_coming_soon']->id ?? 0,
                'stepView' => 'lead'
            ]);
    }

    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'username' => [new HoneyPot],
            'timestamp' => [new HoneyTime(2)],
            'email' => ['required', 'email:filter', 'indisposable', new AllowedEmailDomain, 'max:255'],
            'location_id' => ['nullable', 'integer'],
            'first_name' => ['required', 'max:255'],
            'last_name' => ['required', 'max:255'],
            'address' => ['nullable', 'array'],
            'address.city' => ['nullable', 'string'],
            'address.state' => ['nullable', 'string'],
            'address.postal_code' => ['nullable', 'string'],
            'address.country' => ['nullable', 'string'],
            'address.lat' => ['nullable', 'numeric'],
            'address.lng' => ['nullable', 'numeric'],
        ], [
            'zip.required' => 'Your postal code is required.',
        ]);

        $user = User::where('email', $validated['email'])->first();

        if ( ! is_null($user)) {
            event(new UserSubscribedToNewsletter($user));
        } else {
            $validated['ip_address'] = $request->ip();
            event(new LeadCreated($this->createLead($validated)));
        }

        session()->forget(['postal_code', 'options', 'address']);

        if (setting('lead_capture_url')) {
            return redirect()->to(url(setting('lead_capture_url')));
        }

        Cookie::queue(Cookie::forget('last_viewed_page'));

        if (request()->query('redirect_to')) {
            Cookie::queue('last_viewed_page', url(request()->query('redirect_to')));
        }

        flash('Your contact information has been submitted.');

        return redirect()->to(url($request->cookie('last_viewed_page', '/store')));
    }

    private function createLead(array $attributes): Lead
    {
        return Lead::create([
            'email' => $attributes['email'],
            'ip_address' => $attributes['ip_address'] ?? null,
            'zip' => $attributes['address']['postal_code'] ?? null,
            'city' => $attributes['address']['city'] ?? null,
            'state' => $attributes['address']['state'] ?? null,
            'lat' => $attributes['address']['lat'] ?? null,
            'lng' => $attributes['address']['lng'] ?? null,
            'first_name' => $attributes['first_name'],
            'last_name' => $attributes['last_name'],
            'location_id' => $attributes['location_id'] ?? 0,
            'has_address' => isset($attributes['address']['lat']) && strlen($attributes['address']['lat']),
        ]);
    }
}
