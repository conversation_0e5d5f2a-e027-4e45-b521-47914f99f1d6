<?php

namespace App\Http\Controllers\Theme\Store;

use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Repositories\StoreRepository;
use Illuminate\Http\Request;

class SubcategoryController extends Controller
{
    public function __construct()
    {
        $this->middleware(['ForceSeoStoreUrls']);
    }

    public function show(Request $request, string $parent_category_slug, string $subcategory_slug)
    {
        session(['store_url' => $request->fullUrl()]);

        /** @var Category|null $category */
        $category = Category::query()
            ->whereNotNull('category_id')
            ->whereHas('parentCategory', fn ($query) =>
                $query->where('slug', $parent_category_slug)
            )
            ->where('slug', $subcategory_slug)
            ->with(['parentCategory' => function ($query) {
                return $query->select(['id', 'category_id', 'slug', 'name', 'extra_attributes']);
            }])
            ->first();

        if (is_null($category)) {
            // TODO: redirect to check if collection exists
            return redirect()->to('/store', 301);
        }

        $products = (new StoreRepository($request))
            ->getProductsInCategory($category)
            ->simplePaginate(setting('store_products_per_page', 50));

        return view('theme::store.categories.show')
            ->with([
                'is_subcategory' => true,
                'category' => $category,
                'products' => $products,
            ]);
    }
}
