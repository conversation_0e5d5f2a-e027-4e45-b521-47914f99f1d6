<?php

namespace App\Http\Controllers\API;

use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use App\Models\Page;

class DuplicatePageController extends Controller
{
    public function __invoke(Page $page): JsonResponse
    {
        $duplicate = $page->replicate(['visible', 'needs_published', 'seo_visibility'])
            ->fill(['title' => $page->title . ' (Copy)']);

        $duplicate->save();
        $duplicate->refresh();

        foreach ($page->widgets as $widget) {
            $duplicate_widget = $widget->replicate(['page_id', 'container_id', 'description', 'visible'])
                ->fill(['page_id' => $duplicate->id]);

            $duplicate_widget->save();
        }

        return response()->json(['page' => $duplicate], 201);
    }
}
