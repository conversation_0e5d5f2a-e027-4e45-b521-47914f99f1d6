<?php

namespace App\Traits;

use App\Exceptions\PasswordResetTokenException;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

trait ResetPasswordTrait
{
    protected int $tokenExpirationTime = 3600;

    public function makePasswordResetToken(User $user): string
    {
        $token = hash_hmac('sha256', Str::random(40), config('app.key'));

        DB::table('password_resets')
            ->where('email', $user->email)
            ->delete();

        DB::table('password_resets')
            ->insert([
                'email' => $user->email,
                'token' => $token,
                'created_at' => new Carbon
            ]);

        return $token;
    }

    public function tokenIsValid(string $token): bool
    {
        $password_reset = DB::table('password_resets')
            ->where('token', $token)
            ->first();

        return ! is_null($password_reset)
            && ! $this->passwordResetHasExpired($password_reset);
    }

    public function passwordResetHasExpired(object $password_reset): bool
    {
        return (strtotime($password_reset->created_at) + $this->tokenExpirationTime) < time();
    }

    public function tokenIsInvalid(string $token): bool
    {
        return ! $this->tokenIsValid($token);
    }

    /**
     * @throws PasswordResetTokenException
     */
    public function resetPassword(User $user, string $token, string $password): void
    {
        $password_reset = DB::table('password_resets')
            ->where('email', $user->email)
            ->where('token', $token)
            ->first();

        if (is_null($password_reset) || $this->passwordResetHasExpired($password_reset)) {
            throw new PasswordResetTokenException;
        }

        $this->deletePasswordReset($password_reset);

        $user->password = $password;
        $user->save();
    }

    public function deletePasswordReset(object $password_reset): void
    {
        DB::table('password_resets')
            ->where('token', $password_reset->token)
            ->delete();
    }
}
