<?php

namespace App\Livewire\Theme;

use App\Actions\Subscription\SyncItemToCurrentOrder;
use App\Events\Order\OrderUpdated;
use App\Events\Subscription\SubscriptionPromoProductWasUpdated;
use App\Events\Subscription\SubscriptionUpdated;
use App\Models\Product;
use App\Models\RecurringOrder;
use App\Models\RecurringOrderItem;
use App\Services\SubscriptionSettingsService;
use Livewire\Component;

class SubscriptionItems extends Component
{
    use FetchesSubscription, SendsStorefrontNotifications;

    public RecurringOrder $subscription;

    public bool $show_promotion_change;

    public bool $can_be_modified;

    protected $listeners = [
        'subscriptionUpdated' => 'refreshSubscription',
    ];

    public function mount(bool $show_promotion_change = false, bool $can_be_modified = true)
    {
        $this->show_promotion_change = $show_promotion_change;
        $this->can_be_modified = $can_be_modified;
    }

    public function render()
    {
        return view('theme::livewire.subscription-items', [
            'available_promotion_products' => Product::query()
                ->with(['price' => fn($q) => $q->where('group_id', $this->subscription->pricingGroupId() ?? 0)])
                ->findMany(app(SubscriptionSettingsService::class)->productIncentiveIds())
        ]);
    }

    public function incrementItemQuantity(int $id)
    {
        /** @var RecurringOrderItem|null $item */
        $item = $this->subscription->items()->find($id);

        if (is_null($item)) return;

        $item->load([
            'product.price' => fn($q) => $q->where('group_id', $this->subscription->pricingGroupId() ?? 0)
        ]);

        try {
           $updated_subscription_item =  $this->subscription->updateItemQuantity($item, $item->qty + 1);
        } catch (\Exception $exception) {
            $this->dispatch('openModal', title: 'Unable to update quantity', message: $exception->getMessage());
            return;
        }

        event(new SubscriptionUpdated($this->subscription));

        if ($this->shouldSyncItemToCurrentOrder()) {
            app(SyncItemToCurrentOrder::class)->handle($updated_subscription_item);
        } else {
            $updated_subscription_item->product
                ->load(['price' => fn($q) => $q->where('group_id', $this->subscription->pricingGroupId() ?? 0)]);

            $updated_subscription_item->unit_price_override = $updated_subscription_item->product->getUnitPrice();
            $updated_subscription_item->save();
        }

        $this->dispatch('subscriptionUpdated');
    }

    private function shouldSyncItemToCurrentOrder(): bool
    {
        return ! is_null($this->subscription->currentOrder)
            && ! $this->subscription->currentOrder->deadlineHasPassed();
    }

    public function decrementItemQuantity(int $id)
    {
        /** @var RecurringOrderItem|null $item */
        $item = $this->subscription->items()->find($id);

        if (is_null($item)) return;

        $desired_quantity = $item->qty - 1;
        
        if ($desired_quantity <= 0) {
            $this->removeItem($id);
            return;
        }

        $item->load([
            'product.price' => fn($q) => $q->where('group_id', $this->subscription->pricingGroupId() ?? 0)
        ]);

        try {
            $updated_subscription_item = $this->subscription->updateItemQuantity($item, $desired_quantity);
        } catch (\Exception $exception) {
            $this->dispatch('openModal', title: 'Unable to update quantity', message: $exception->getMessage());
            return;
        }

        event(new SubscriptionUpdated($this->subscription));

        if ($this->shouldSyncItemToCurrentOrder()) {
            app(SyncItemToCurrentOrder::class)->handle($updated_subscription_item);
            $this->subscription->currentOrder->updateTotals();
            event(new OrderUpdated($this->subscription->currentOrder));
            $this->dispatch('orderUpdated');
        } else {
            $updated_subscription_item->product
                ->load(['price' => fn($q) => $q->where('group_id', $this->subscription->pricingGroupId() ?? 0)]);

            $updated_subscription_item->unit_price_override = $updated_subscription_item->product->getUnitPrice();
            $updated_subscription_item->save();
        }

        $this->dispatch('subscriptionUpdated');
    }

    public function removeItem(int $id)
    {
        /** @var RecurringOrderItem|null $item */
        $item = $this->subscription->items()->find($id);

        if (is_null($item)) return;

        $item->load([
            'product.price' => fn($q) => $q->where('group_id', $this->subscription->pricingGroupId() ?? 0)
        ]);

        try {
            $this->subscription->updateItemQuantity($item, 0);
        } catch (\Exception $exception) {
            $this->dispatch('openModal', title: 'Unable to remove item', message: $exception->getMessage());
            return;
        }

        event(new SubscriptionUpdated($this->subscription));

        if ($this->shouldSyncItemToCurrentOrder()) {
            $this->subscription->currentOrder
                ->items()
                ->where([
                    'order_items.product_id' => $item->product_id,
                    'order_items.type' => $item->type === 'recurring' ? 'standard' : $item->type
                ])
                ->delete();
        }

        $this->dispatch('subscriptionUpdated');
    }

    public function refreshSubscription()
    {
        $this->subscription = $this->fetchCustomerSubscription();
    }

    public function updatePromoProduct(int $product_id): void
    {
        if (app(SubscriptionSettingsService::class)->productIncentiveIds()->doesntContain($product_id)) {
            return;
        }

        $current_promo_item = $this->subscription->promoItem();

        if ($current_promo_item?->id === $product_id) return;

        $product = Product::query()
            ->with(['price' => fn($q) => $q->where('group_id', $this->subscription->pricingGroupId() ?? 0)])
            ->find($product_id);

        if (is_null($product)) {
            $this->dispatch('openModal', title: 'Unable to update product', message: 'The product is no longer available.');
            return;
        }

        try {
            $updated_promo_item = $this->subscription->addItem(product: $product, type: 'promo');

        } catch (\Exception $exception) {
            $this->dispatch('openModal', title: 'Unable to update promo product', message: $exception->getMessage());
            return;
        }

        event(new SubscriptionPromoProductWasUpdated(
            subscription: $this->subscription,
            old_promo_product_id: $current_promo_item?->product_id,
            new_promo_product_id: $product->id
        ));

        $current_promo_item?->delete();

        if ($this->shouldSyncItemToCurrentOrder()) {
            app(SyncItemToCurrentOrder::class)->handle($updated_promo_item);
            $this->subscription->currentOrder->updateTotals();
            event(new OrderUpdated($this->subscription->currentOrder));
            $this->dispatch('orderUpdated');
        }

        event(new SubscriptionUpdated($this->subscription));

        $this->sendStorefrontNotification([
            'level' => 'success',
            'title' => 'Success!',
            'message' => 'Your promo item has been updated.',
        ]);

        $this->dispatch('subscriptionUpdated');
    }
}
