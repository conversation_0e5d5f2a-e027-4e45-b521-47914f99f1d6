<?php

namespace App\Livewire\Theme\Modals;

use Livewire\Attributes\On;
use Livewire\Component;

class DeleteAddressConfirmation extends Component
{
    use ModalAttributes;

    public int $address_id;

    public function render()
    {
        return view('theme::livewire.modals.delete-address-confirmation');
    }

    public function submit()
    {
        $user = auth()->user();

        if ($user->addresses()->count() === 1) {
            error("You cannot delete your last address!");
            return $this->redirect(route('customer.addresses'));
        }

        if ($user->addresses()->where('address_id', $this->address_id)->first()?->getRelationValue('location')->is_default) {
            error("You cannot delete your default address! Please set another address as default first.");
            return $this->redirect(route('customer.addresses'));
        }

        $user->addresses()->detach($this->address_id);

        flash('The address has been deleted!');

        return $this->redirect(route('customer.addresses'));
    }

    #[On('open-modal-delete-address-confirmation')]
    public function open(int $address_id): void
    {
        $this->address_id = $address_id;
        $this->openModal();
    }

    #[On('close-modal-delete-address-confirmation')]
    public function close(): void
    {
        $this->closeModal();
    }
}
