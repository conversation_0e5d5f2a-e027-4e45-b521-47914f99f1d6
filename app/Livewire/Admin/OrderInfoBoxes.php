<?php

namespace App\Livewire\Admin;

use App\Models\Order;
use Livewire\Attributes\On;
use Livewire\Component;

class OrderInfoBoxes extends Component
{
    public Order $order;

    public function mount(Order $order)
    {
        $this->order = $order;
    }

    public function render()
    {
        return view('livewire.order-info-boxes');
    }

    #[On('order-updated')]
    public function updateOrder(int $order_id)
    {
        if ($this->order->id !== $order_id) {
            return;
        }

        $this->order = $this->order->refresh();
    }
}
