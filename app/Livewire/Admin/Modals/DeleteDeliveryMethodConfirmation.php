<?php

namespace App\Livewire\Admin\Modals;

use App\Livewire\Theme\Modals\ModalAttributes;
use App\Models\Pickup;
use Livewire\Attributes\On;
use Livewire\Component;

class DeleteDeliveryMethodConfirmation extends Component
{
    use ModalAttributes;

    public ?int $delivery_method_id = null;

    public string $name = '';

    protected $rules = [
        'name' => 'required',
    ];

    public function render()
    {
        return view('livewire.modals.delete-delivery-method-confirmation', [
            'delivery_method' => Pickup::find($this->delivery_method_id),
        ]);
    }

    public function submit()
    {
        $this->validate();

        $delivery_method = Pickup::find($this->delivery_method_id);

        if ($this->name !== $delivery_method->title) {
            $this->addError('name', 'The delivery method name does not match.');

            return;
        }

        $is_delivery_zone = $delivery_method->isDeliveryZone();

        $delivery_method->delete();

        flash('Delivery method was successfully deleted!');

        return $this->redirect(route($is_delivery_zone ? 'admin.delivery.index' : 'admin.pickups.index'));
    }

    #[On('close-modal-delete-delivery-method-confirmation')]
    public function close(): void
    {
        $this->reset(['name']);
        $this->closeModal();
    }

    #[On('open-modal-delete-delivery-method-confirmation')]
    public function open(int $delivery_method_id): void
    {
        $this->delivery_method_id = $delivery_method_id;
        $this->openModal();
    }
}
