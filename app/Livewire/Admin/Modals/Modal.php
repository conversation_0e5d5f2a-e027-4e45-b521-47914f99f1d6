<?php

namespace App\Livewire\Admin\Modals;

use Illuminate\Contracts\View\View as ViewContract;
use Livewire\Component;

class Modal extends Component
{
    public bool $open = false;
    public string $component = '';
    public array $params = [];

    protected $listeners = [
        'openModal',
        'closeModal'
    ];

    public function openModal(string $component, array $params): void
    {
        $this->open = true;
        $this->component = $component;
        $this->params = $params;
    }

    public function closeModal(): void
    {
        $this->reset('open', 'component', 'params');
    }

    public function render(): ViewContract
    {
        return view('livewire.modals.modal');
    }
}