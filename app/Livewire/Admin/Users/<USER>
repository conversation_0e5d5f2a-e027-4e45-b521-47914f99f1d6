<?php

namespace App\Livewire\Admin\Users;

use App\Actions\Billing\RemoveCard;
use App\Actions\Billing\SetDefaultCard;
use App\Livewire\Admin\SendsAdminNotifications;
use App\Models\User;
use Livewire\Component;

class CreditCards extends Component
{
    use SendsAdminNotifications;

    public User $user;

    public ?string $default_source_id;

    public function mount(User $user)
    {
        $this->user = $user;
        $this->setDefaultSource();
    }

    private function setDefaultSource()
    {
        $this->default_source_id =  $this->user
            ->defaultCard()
            ->value('source_id');
    }

    public function render()
    {
        $cards = $this->user->stripePaymentMethods();
        return view('livewire.users.credit-cards', compact('cards'));
    }

    public function placeholder()
    {
        return <<<'HTML'
        <div class="w-full flex justify-center py-6">
           <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-gray-200" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
        </div>
        HTML;
    }

    public function setAsDefault(string $source_id)
    {
        $card = $this->user
            ->cards()
            ->where('source_id', $source_id)
            ->first();

        if (is_null($card)) {
            $this->sendAdminNotification([
                'level' => 'error',
                'title' => 'Error!',
                'message' => 'The card could not be set as the default.',
            ]);
            return;
        }

        try {
            app(SetDefaultCard::class)->handle($card);
        } catch (\Exception $exception) {
            $this->sendAdminNotification([
                'level' => 'error',
                'title' => 'Error!',
                'message' => 'The default card could not be updated.',
            ]);
            return;
        }

        $this->default_source_id = $source_id;

        $this->sendAdminNotification([
            'level' => 'success',
            'title' => 'Success!',
            'message' => 'The default card has been updated!',
        ]);
    }

    public function removeCard(string $source_id)
    {
        try {
            app(RemoveCard::class)->handle($this->user->customer_id, $source_id);
        } catch (\Exception $exception) {
            $this->sendAdminNotification([
                'level' => 'error',
                'title' => 'Error!',
                'message' => 'The card could not be deleted.',
            ]);
            return;
        }

        $this->setDefaultSource();

        $this->sendAdminNotification([
            'level' => 'success',
            'title' => 'Card deleted!',
            'message' => 'The card has been deleted!',
        ]);
    }
}
