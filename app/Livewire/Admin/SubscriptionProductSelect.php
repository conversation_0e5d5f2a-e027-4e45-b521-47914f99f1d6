<?php

namespace App\Livewire\Admin;

use App\Models\Product;
use App\Services\SubscriptionSettingsService;
use Livewire\Component;

class SubscriptionProductSelect extends Component
{
    public ?int $selected_product_id = null;
    public ?Product $selected_product = null;

    public bool $open = false;

    public string $term = '';

    public function mount(?int $selected_product_id = null)
    {
        if ( ! is_null($selected_product_id)) {
            $this->selected_product = Product::find($selected_product_id);
        }
    }

    public function render()
    {
        $current_promo_products = Product::query()
            ->whereIn('id', app(SubscriptionSettingsService::class)->productIncentiveIds())
            ->when( ! empty($this->term), function ($query) {
                return $query->where(function ($query) {
                    return $query
                        ->where('title', 'LIKE', '%'.$this->term.'%')
                        ->orWhere('sku', 'LIKE', '%'.$this->term.'%');
                });
            })
            ->select(['id', 'title', 'sku'])
            ->get();

        $other_products = Product::query()
            ->whereNotIn('id', app(SubscriptionSettingsService::class)->productIncentiveIds())
            ->when( ! empty($this->term), function ($query) {
                return $query->where(function ($query) {
                    return $query
                        ->where('title', 'LIKE', '%'.$this->term.'%')
                        ->orWhere('sku', 'LIKE', '%'.$this->term.'%');
                });
            })
            ->select(['id', 'title', 'sku'])
            ->limit(10)
            ->orderBy('title')
            ->get();

        return view(
            'livewire.subscription-product-select',
            compact('current_promo_products', 'other_products')
        );
    }

    public function selectProduct(int $product_id)
    {
        $product = Product::find($product_id);

        $this->selected_product = $product;
        $this->dispatch('productSelected', product_id: $product->id);
        $this->open = false;
    }
}
