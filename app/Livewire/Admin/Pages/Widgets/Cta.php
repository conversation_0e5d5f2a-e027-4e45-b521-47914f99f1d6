<?php

namespace App\Livewire\Admin\Pages\Widgets;

use Illuminate\Validation\Rule;
use Livewire\Component;

class Cta extends Component
{
    use HasPageWidgetSettings;
    use HasIdentifiers;
    use HasSizing;

    public string $heading_image = '';
    public string $heading_max_width = '';
    public string $button_size;
    public array $ctas = [];


    public function mount(int $page_id, array $widget)
    {
        $this->page_id = $page_id;
        $this->widget = $widget;
        $this->initializeWidget($widget);
    }

    protected function initializeWidget(array $widget): void
    {
        $this->initializeIdentifiers($widget['settings']);
        $this->initializeSizing($widget['settings']);

        $this->heading_image = $widget['settings']['heading']['image'] ?? '';
        $this->heading_max_width = $widget['settings']['heading']['max_width'] ?? false;
        $this->button_size = $widget['settings']['button_size'] ?? 'md';
        $this->ctas = $widget['settings']['ctas'];
    }

    public function save()
    {
        $this->validate(array_merge(
            $this->identiferRules(),
            $this->sizingRules(),
            $this->rules()
        ));

        $this->savePageWidgetSettings([
            'name' => $this->name,
            'html_id' => $this->html_id,
            'max_width' => $this->max_width,
            'padding' => [
                'top' => $this->padding_top,
                'bottom' => $this->padding_bottom,
            ],
            'heading' => [
                'image' => $this->heading_image,
                'max_width' => $this->heading_max_width,
            ],
            'button_size' => $this->button_size,
            'ctas' => $this->ctas,
        ]);

        $this->dispatch('widget-updated');
    }

    protected function rules(): array
    {
        return array_merge(
            $this->identiferRules(),
            $this->sizingRules(),
            [
                'heading_image' => ['nullable', 'string'],
                'heading_max_width' => ['nullable', Rule::in(['none', 'sm', 'md', 'lg', 'xl'])],
                'button_size' => ['nullable', Rule::in(['sm', 'md', 'lg', 'xl'])],
                'ctas' => ['required', 'array'],
                'ctas.*.label' => ['required', 'string'],
                'ctas.*.url' => ['required', 'string'],
                'ctas.*.style' => ['required', 'in:brand,action,white,transparent'],
            ]
        );
    }

    public function addCta(string $label, string $url, string $style = 'primary')
    {
        $this->ctas[] = [
            'label' => $label,
            'url' => $url,
            'style' => $style,
        ];

        $this->saveCurrentSettings();
    }

    protected function saveCurrentSettings(): void
    {
        $this->savePageWidgetSettings([
            'name' => $this->name,
            'padding' => [
                'top' => $this->padding_top,
                'bottom' => $this->padding_bottom,
            ],
            'heading' => [
                'image' => $this->heading_image,
                'max_width' => $this->heading_max_width,
            ],
            'button_size' => $this->button_size,
            'ctas' => $this->ctas,
        ]);
    }

    public function deleteCta(int $index)
    {
        $this->ctas = array_values(array_filter(
            $this->ctas,
            fn($value, $key) => $key !== $index,
            ARRAY_FILTER_USE_BOTH
        ));

        $this->saveCurrentSettings();
    }

    public function render()
    {
        return view('livewire.pages.widgets.cta');
    }
}
