<?php

namespace App\Livewire\Admin\Pages\Widgets;

use Illuminate\Validation\Rule;

trait HasSizing
{
    public string $padding_top;

    public string $padding_bottom;

    public string $max_width;

    protected function initializeSizing(array $settings)
    {
        $this->padding_top = $settings['padding']['top'] ?? 'none';
        $this->padding_bottom = $settings['padding']['bottom'] ?? 'none';
        $this->max_width = $settings['max_width'] ?? 'none';
    }

    protected function sizingRules()
    {
        return [
            'padding_top' => ['nullable', Rule::in(['none', 'sm', 'md', 'lg', 'xl'])],
            'padding_bottom' => ['nullable', Rule::in(['none', 'sm', 'md', 'lg', 'xl'])],
            'max_width' => ['nullable', Rule::in(['none', 'sm', 'md', 'lg', 'xl'])],
        ];
    }
}
