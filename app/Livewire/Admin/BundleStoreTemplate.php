<?php

namespace App\Livewire\Admin;

use App\Models\Product;
use Livewire\Component;

class BundleStoreTemplate extends Component
{
    use SendsAdminNotifications;

    public $contents_description;
    public $heading;
    public $subheading;
    public $video_embed;

    public $farming_practices;
    public $related_products;
    public $related_recipes;

    public $product_id;

    public function mount($product_id)
    {
        $this->product_id = $product_id;

        $bundle_template_settings = Product::query()
            ->select('settings')
            ->find($product_id)
            ?->setting('bundle_template');

        $this->contents_description = $bundle_template_settings->contents_description ?? '';
        $this->heading = $bundle_template_settings->heading ?? '';
        $this->subheading = $bundle_template_settings->subheading ?? '';
        $this->video_embed = $bundle_template_settings->video_embed ?? '';
        $this->farming_practices = $bundle_template_settings->farming_practices ?? 'mixed';
        $this->related_products = $bundle_template_settings->related_products ?? '';
        $this->related_recipes = $bundle_template_settings->related_recipes ?? '';
    }

    public function render()
    {
        return view('livewire.bundle-store-template');
    }

    public function save()
    {
        $validated = $this->validate([
            'contents_description' => ['nullable', 'string'],
            'heading' => ['nullable', 'string'],
            'subheading' => ['nullable', 'string'],
            'video_embed' => ['nullable', 'string'],
            'farming_practices' => ['nullable', 'in:beef,chicken,pork,mixed,seafood,lamb,bison'],
            'related_products' => ['nullable', 'string'],
            'related_recipes' => ['nullable', 'string'],
        ]);

        $product = Product::find($this->product_id);

        $product->setting(['bundle_template' => $validated]);

        $this->sendAdminNotification([
            'type' => 'success',
            'title' => 'Bundle Template Updated!',
            'message' => 'The bundle template has been updated.',
        ]);
    }
}
