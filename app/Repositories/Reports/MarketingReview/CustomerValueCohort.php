<?php

namespace App\Repositories\Reports\MarketingReview;

use App\Repositories\Reports\MarketingReview\Concerns\OrderScope;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class CustomerValueCohort
{
    use OrderScope;

    public function handle(Carbon $confirmed_start, Carbon $confirmed_end): array
    {
        $result = DB::table(function ($sub_query) use ($confirmed_start, $confirmed_end) {
            return $this->applyScope(
                $sub_query->from('orders')
                    ->selectRaw('customer_id, COUNT(*) AS order_count, SUM(total) AS order_total')
                    ->whereIn('customer_id', function ($customer_query) use ($confirmed_start, $confirmed_end) {
                        return $this->applyScope(
                            $customer_query->from('orders')
                                ->where('first_time_order', true)
                                ->whereBetween('confirmed_date', [$confirmed_start, $confirmed_end])
                                ->selectRaw('customer_id')
                        );
                    })
                ->groupBy('customer_id')
            );
        }, 'sub')
            ->selectRaw('count(customer_id) as cohort_size, cast(avg(order_count) as DECIMAL(5,2)) AS average_order_count, cast((sum(order_total) / sum(order_count)) as DECIMAL(8,0)) AS average_order_value')
            ->first();

        $average_order_value = (int) round($result->average_order_value ?? 0);
        $average_customer_order_count = round($result->average_order_count ?? 0, 2);

        return [
            'first_order_confirmed_start_date' => $confirmed_start,
            'first_order_confirmed_end_date' => $confirmed_end,
            'cohort_size' => (int) $result->cohort_size,
            'average_order_value' => $average_order_value,
            'average_customer_order_count' => $average_customer_order_count,
            'average_customer_value' => (int) round($average_order_value * $average_customer_order_count)
        ];
    }
}
