<?php

namespace App\Repositories;

use App\Models\Pickup;
use App\Services\Geocoding\GeocodedAddress;
use App\Support\Enums\PickupStatus;
use Illuminate\Support\Facades\DB;

class DeliveryOptionRepository
{
    protected array $options = [
        'closest' => null,
        'closest_zone' => null,
        'closest_pickup' => null,
        'closest_coming_soon' => null,
        'closest_zone_coming_soon' => null,
        'closest_pickup_coming_soon' => null,
        'pickup' => [],
        'delivery' => [],
    ];

    protected array $select = [
        'id', 'title', 'display_name', 'slug', 'fulfillment_type', 'status_id', 'delivery_rate', 'street', 'city', 'state', 'zip', 'settings', 'schedule_id'
    ];

    public function handle(GeocodedAddress $address, array $product_ids = [], bool $include_coming_soon = false): DeliveryOptionRepository
    {
        return $this
            ->findByState($address, $product_ids, $include_coming_soon)
            ->findByCoordinates($address, $product_ids, $include_coming_soon)
            ->findByZip($address, $product_ids, $include_coming_soon)
            ->findClosestResult();
    }

    public function findByZip(GeocodedAddress $address, array $product_ids = [], bool $include_coming_soon = false): DeliveryOptionRepository
    {
        $ids = DB::table('pickup_zips')
            ->select('pickup_id')
            ->where('zip', $address->postalCode)
            ->pluck('pickup_id');


        if ($ids->isEmpty()) {
            return $this;
        }

        $options = Pickup::select($this->select)
            ->where('visible', true)
            ->whereIn('status_id', $include_coming_soon ? [PickupStatus::open(), PickupStatus::comingSoon()] : [PickupStatus::open()])
            ->whereIn('pickups.id', $ids)
            ->when( ! empty($product_ids), function ($query) use ($product_ids) {
                $query->whereDoesntHave('products', function ($query) use ($product_ids) {
                    $query->whereIn('products.id', $product_ids);
                });
            })
            ->get();

        foreach ($options as $option) {
            array_unshift($this->options['delivery'], $option);
        }

        return $this;
    }

    public function findByState(GeocodedAddress $address, array $product_ids = [], bool $include_coming_soon = false): DeliveryOptionRepository
    {
        if ( ! $address->state) {
            return $this;
        }

        $ids = DB::table('pickup_states')
            ->select('pickup_id')
            ->where('state', $address->state)
            ->pluck('pickup_id');

        if ($ids->isEmpty()) {
            return $this;
        }

        $options = Pickup::select($this->select)
            ->where('visible', true)
            ->whereIn('status_id', $include_coming_soon ? [PickupStatus::open(), PickupStatus::comingSoon()] : [PickupStatus::open()])
            ->whereIn('pickups.id', $ids)
            ->when( ! empty($product_ids), function ($query) use ($product_ids) {
                $query->whereDoesntHave('products', function ($query) use ($product_ids) {
                    $query->whereIn('products.id', $product_ids);
                });
            })
            ->get();

        foreach ($options as $option) {
            $this->options['delivery'][] = $option;
        }

        return $this;
    }

    public function getOptions(): array
    {
        /** @var array<int, Pickup> $deliveries */
        $deliveries = $this->options['delivery'] ?? [];

        /** @var array<int, Pickup> $pickups */
        $pickups = $this->options['pickup'] ?? [];

        $this->options['delivery'] = collect($deliveries)->unique('id')->all();
        $this->options['pickup'] = collect($pickups)->unique('id')->all();

        return $this->options;
    }

    public function simpleOptions(): array
    {
        $delivery_options = collect($this->options['delivery'])->unique('id');

        $this->options['delivery'] = $delivery_options->first(fn(Pickup $pickup) => $pickup->isOpen() && $pickup->isCurrentlyAcceptingOrders());

        if (is_null($this->options['delivery'])) {
            $delivery_options->first(fn(Pickup $pickup) => $pickup->isOpen());
        }

        $this->options['pickup'] = collect($this->options['pickup'])->unique('id')->all();

        return $this->options;
    }

    /**
     * Find the closest option of all the results.
     * Delivery zones are selected before pickups.
     */
    private function findClosestResult(): DeliveryOptionRepository
    {
        $this->findClosestDeliveryZone();
        $this->findClosestPickupLocations();

        return $this;
    }

    private function findClosestDeliveryZone(): void
    {
        // Find the closest delivery option.
        foreach ($this->options['delivery'] as $zone) {
            if ($zone->isOpen()) {
                // Set the first open delivery zone as closest.
                if (!$this->options['closest']) {
                    $this->options['closest'] = $zone;
                }

                // Set the first closest zone.
                if (!$this->options['closest_zone']) {
                    $this->options['closest_zone'] = $zone;
                }
            }

            if ($zone->isComingSoon()) {
                // Set the first closest coming soon zone.
                if (!$this->options['closest_coming_soon']) {
                    $this->options['closest_coming_soon'] = $zone;
                }

                if (!$this->options['closest_zone_coming_soon']) {
                    $this->options['closest_zone_coming_soon'] = $zone;
                }
            }
        }

        /** @var array<int, Pickup> $deliveries */
        $deliveries = $this->options['delivery'] ?? [];

        // Filter out any coming soon zones so all we have are open ones.
        $this->options['delivery'] = collect($deliveries)
            ->filter(fn(Pickup $zone) => $zone->isOpen());

        // If there are no open delivery zones, then use the closest coming zone if we have one.
        if ($this->options['delivery']->count() === 0 && $this->options['closest_zone_coming_soon']) {
            $this->options['delivery']->push($this->options['closest_zone_coming_soon']);
        }

        // Only return the first result unless show the closest zone is disabled.
        if (count($this->options['delivery']) && setting('show_closest_zone', true)) {
            $this->options['delivery'] = [$this->options['delivery']->first()];
        }
    }

    private function findClosestPickupLocations(): void
    {
        foreach ($this->options['pickup'] as $pickup) {
            if ($pickup->isOpen()) {
                if (!$this->options['closest']) {
                    $this->options['closest'] = $pickup;
                }
                if (!$this->options['closest_pickup']) {
                    $this->options['closest_pickup'] = $pickup;
                }
            }

            if ($pickup->isComingSoon()) {
                if (!$this->options['closest_coming_soon']) {
                    $this->options['closest_coming_soon'] = $pickup;
                }

                if (!$this->options['closest_pickup_coming_soon']) {
                    $this->options['closest_pickup_coming_soon'] = $pickup;
                }
            }
        }

        /** @var array<int, Pickup> $pickups */
        $pickups = $this->options['pickup'] ?? [];

        // Filter out the coming soon locations.
        $this->options['pickup'] = collect($pickups)->filter(fn(Pickup $pickup) => $pickup->isOpen());

        if (!count($this->options['pickup']) && $this->options['closest_pickup_coming_soon']) {
            $this->options['pickup']->push($this->options['closest_pickup_coming_soon']);
        }
    }

    private function findByCoordinates(GeocodedAddress $address, array $product_ids = [], bool $include_coming_soon = false): DeliveryOptionRepository
    {
        $maxDistance = setting('pickup_results_radius', 400);

        $this->options['pickup'] = Pickup::where('fulfillment_type', Pickup::FULFILLMENT_TYPE_PICKUP)
            ->where('visible', true)
            ->whereIn('status_id', $include_coming_soon ? [PickupStatus::open(), PickupStatus::comingSoon()] : [PickupStatus::open()])
            ->when( ! empty($product_ids), function ($query) use ($product_ids) {
                $query->whereDoesntHave('products', function ($query) use ($product_ids) {
                    $query->whereIn('products.id', $product_ids);
                });
            })
            ->select($this->select)
            ->coordinates($address->lat, $address->lng)
            ->having('distance', '<=', $maxDistance)
            ->orderBy('distance')
            ->limit(setting('pickup_results_count', 100))
            ->get();

        return $this;
    }
}
