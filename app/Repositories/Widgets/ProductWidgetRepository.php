<?php

namespace App\Repositories\Widgets;

use App\Models\Collection;
use App\Models\Pickup;
use App\Models\Product;
use App\Models\Widget;
use Illuminate\Support\Collection as IlluminateCollection;

class ProductWidgetRepository
{
    public int $count;

    public ?string $source;

    public string $orderBy = 'title';

    public string $sort = 'asc';
    public bool $showHidden = false;

    public array $select = [
        'title', 'type_id', 'slug', 'products.id', 'sale', 'cover_photo',
        'weight', 'unit_of_issue', 'unit_price', 'sale_unit_price',
        'unit_description', 'sort_order', 'is_bundle', 'track_inventory', 'settings', 'fulfillment_id',
        'inventory', 'oos_threshold_inventory', 'back_order', 'other_inventory',
    ];

    /**
     * @var IlluminateCollection<int, int>
     */
    protected IlluminateCollection $excluded;

    protected int $max_products = 50;

    public function __construct(
        public Widget $widget,
        public ?int $delivery_method_id = null,
        public ?int $price_group_id = null
    ) {
        $this->count = $widget->settings->count ?? 4;
        $this->source = $widget->settings->collection ?? null;
        $this->orderBy = $widget->setting('order_by', 'title');
        $this->sort = $widget->setting('sort', 'asc');
        $this->showHidden = $widget->setting('show_hidden', false);
        $this->setExcludedProducts();
    }

    private function setExcludedProducts(): void
    {
        $this->excluded = collect();

        if ( ! is_null($this->delivery_method_id)) {
            $this->excluded = Pickup::find($this->delivery_method_id)
                ?->products()
                ->pluck('products.id')
                ?? collect();
        }
    }

    /**
     * @return IlluminateCollection<int, Product>
     */
    public function get(): IlluminateCollection
    {
        $products = Product::query()
            ->select($this->select)
            ->with([
                'price' => fn($q) => $q->where('group_id', $this->price_group_id),
                'mainPhoto'
            ])
            ->with(['variants' => function ($q) {
                if (count($this->excluded)) {
                    $q->excluding($this->excluded);
                }

                $q->with(['price' => fn($q) => $q->where('group_id', $this->price_group_id)]);
            }])
            ->join('collection_product', 'collection_product.product_id', '=', 'products.id')
            ->when(count($this->excluded), fn($q) => $q->excluding($this->excluded))
            ->when($this->source, fn($q) => $q->where('collection_id', $this->source));

        if (!$this->showHidden) {
            $products->where('visible', true);
        }

        if ($this->widget->setting('sort_by_collection', true)) {
            $this->sortByCollection();
        }

        return $products
            ->orderBy($this->orderBy, $this->sort)
            ->take($this->productCount())
            ->get();
    }

    private function sortByCollection(): void
    {
        if ($collection = Collection::find($this->source)) {
            /** @var array<int, string> $ordering */
            $ordering = explode('-', $collection->order);
            $this->orderBy = $ordering[0] ?? 'title';
            $this->sort = $ordering[1] ?? 'asc';

            // Map the key "Custom" sort to the database field.
            if ($this->orderBy === 'custom_sort') {
                $this->orderBy = 'sort_order';
            }
        }
    }

    private function productCount(): int
    {
        return min($this->count, $this->max_products);
    }
}
