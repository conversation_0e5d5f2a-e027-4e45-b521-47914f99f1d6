<?php

namespace App\API\V2\Controllers;

use App\Http\Controllers\Controller;
use App\Http\Resources\OrderResource;
use App\Models\Order;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;

class OrderController extends Controller
{
    public function index(): AnonymousResourceCollection
    {
        return OrderResource::collection(
            Order::query()->where(['confirmed' => true, 'canceled' => false])->latest()->paginate(10)
        );
    }

    public function show(Order $order): JsonResource
    {
        return new OrderResource($order);
    }
}
