<?php

namespace App\API\V2\Requests;

use App\Models\User;
use App\Support\Enums\Channel;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class OrderRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed> $rules
     */
    public function rules(): array
    {
        return [
            'user_id' => ['required', Rule::exists(User::class,'id')],
            'deadline_date' => ['sometimes', 'date'],
            'pickup_date' => ['sometimes', 'date'],
            'payment_id' => ['sometimes', 'integer'],
            'type_id' => ['sometimes', 'integer', Rule::in(Channel::ids())],
        ];
    }
}
