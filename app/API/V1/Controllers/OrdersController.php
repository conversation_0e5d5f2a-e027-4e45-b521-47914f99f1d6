<?php

namespace App\API\V1\Controllers;

use Illuminate\Http\JsonResponse;
use App\API\V1\Actions\UpdateOrderStatus;
use App\API\V1\Resources\OrderResource;
use App\Http\Controllers\Controller;
use App\Models\Order;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class OrdersController extends Controller
{
    public function index(Request $request)
    {
        if ( ! $request['sanctum_authenticated'] && $request['_apiKey']->doesNotHaveScope('orders:index')) {
            return response()->json([
                'error' => 'You do not have permission to view orders.'
            ], 403);
        }

        $limit = min($request->input('limit', 100), 100);
        $request['confirmed'] = $request->input('confirmed', true); // Only return confirmed orders by default.
        $request['canceled'] = $request->input('canceled', false); // Only return none canceled orders by default.
        return OrderResource::collection(
            Order::filter($request->all())->paginate($limit)
        );
    }

    public function show(Request $request, $orderId)
    {
        if ( ! $request['sanctum_authenticated'] && $request['_apiKey']->doesNotHaveScope('orders:show')) {
            return response()->json([
                'error' => 'You do not have permission to view this order.'
            ], 403);
        }

        try {
            return new OrderResource(Order::with(['items.product'])->findOrFail($orderId));
        } catch (ModelNotFoundException $exception) {
            return response()->json([
                'error' => 'An order with that ID could not be found.'
            ], 404);
        }
    }

    public function update(Request $request, $orderId): JsonResponse
    {
        if ( ! $request['sanctum_authenticated'] && $request['_apiKey']->doesNotHaveScope('orders:update')) {
            return response()->json([
                'error' => 'You do not have permission to update orders.'
            ], 403);
        }

        $validatedData = $request->validate([
            'status' => ['required', Rule::in([
                'new', 'processing', 'packed', 'picked_up',
                'completed', 'on_hold', 'canceled', 'pre_order'
            ])],
        ]);

        try {
            $order = Order::findOrFail($orderId);

            if (isset($validatedData['status'])) {
                (new UpdateOrderStatus())->run($order, $validatedData['status']);
            }

            // Dispatch event that order was updated.
            return response()->json([
                'message' => 'Order update successful.',
                'order' => new OrderResource($order->refresh())
            ]);
        } catch (ModelNotFoundException $exception) {
            return response()->json([
                'error' => 'An order with that ID could not be found.'
            ], 404);
        }
    }
}
