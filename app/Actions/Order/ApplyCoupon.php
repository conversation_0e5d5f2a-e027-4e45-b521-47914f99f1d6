<?php

namespace App\Actions\Order;

use App\Exceptions\CouponInvalidException;
use App\Models\Coupon;
use App\Models\Order;

class ApplyCoupon
{
    /**
     * @throws CouponInvalidException
     */
    public function handle(Order $order, Coupon $coupon): Coupon
    {
        $coupon->validateForCart($order);

        $order->discounts()->attach($coupon->id, ['user_id' => $order->customer_id, 'savings' => $coupon->valueForOrder($order)]);

        $coupon->increment('total_uses');

        $order->updateTotals();

        return $coupon;
    }
}
