<?php

namespace App\Actions\Order;

use App\Models\OrderItem;
use App\Models\Product;
use App\Models\RecurringOrder;
use App\Models\RecurringOrderItem;

class SyncItemToSubscription
{
    public function handle(OrderItem $order_item): void
    {
        /** @var RecurringOrder|null $subscription */
        $subscription = $order_item->order->blueprint;

        if (is_null($subscription)) {
            return;
        }

        match($order_item->type) {
            'addon' => $this->handleAddonItemSync($order_item, $subscription),
            'promo' => $this->handlePromoItemSync($order_item, $subscription),
            default => $this->handleRecurringItemSync($order_item, $subscription),
        };
    }

    private function handleAddonItemSync(OrderItem $order_item, RecurringOrder $subscription): void
    {
        $current_item = $this->findProductOnSubscription($order_item->product, $subscription, 'addon');

        ! is_null($current_item)
            ? $this->updateExistingItemOnSubscription($current_item, $subscription, $order_item->qty)
            : $this->addItemToSubscription($order_item, $subscription, 'addon');
    }

    private function handlePromoItemSync(OrderItem $order_item, RecurringOrder $subscription): void
    {
        $current_item = $subscription->items()
            ->where(['type' => 'promo'])
            ->first();

        if ($order_item->qty > 0) {
            $subscription->addPromoItem($order_item->product);
        }


        $current_item?->delete();
    }

    private function handleRecurringItemSync(OrderItem $order_item, RecurringOrder $subscription): void
    {
        $current_item = $this->findProductOnSubscription($order_item->product, $subscription, 'recurring');

        ! is_null($current_item)
            ? $this->updateExistingItemOnSubscription($current_item, $subscription, $order_item->qty)
            : $this->addItemToSubscription($order_item, $subscription, 'recurring');

    }

    private function findProductOnSubscription(Product $product, RecurringOrder $subscription, string $type): ?RecurringOrderItem
    {
        return $subscription->items()
            ->where([
                'product_id' => $product->id,
                'type' => $type
            ])
            ->first();
    }

    private function addItemToSubscription(OrderItem $order_item, RecurringOrder $subscription, string $type): void
    {
        $subscription->addItem(product: $order_item->product, quantity: $order_item->qty, type: $type, force: true);
    }

    private function updateExistingItemOnSubscription(RecurringOrderItem $subscription_item, RecurringOrder $subscription, int $quantity): void
    {
        $subscription->updateItemQuantity(item: $subscription_item, quantity: $quantity, force: true);
    }
}