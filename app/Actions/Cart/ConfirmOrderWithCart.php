<?php

namespace App\Actions\Cart;

use App\Actions\Cart\Pipeline\UpdateOrderBillingFromCart;
use App\Actions\Cart\Pipeline\UpdateOrderCustomerFromCart;
use App\Actions\Cart\Pipeline\UpdateOrderDiscountFromCart;
use App\Actions\Cart\Pipeline\UpdateOrderAttributesFromCart;
use App\Actions\Cart\Pipeline\UpdateOrderShippingFromCart;
use App\Actions\Cart\Pipeline\UpdateOrderSubscriptionFromCart;
use App\Cart\Cart;
use App\Events\User\UserSubscribedToSmsMarketing;
use App\Models\Order;
use Exception;
use Illuminate\Pipeline\Pipeline;

class ConfirmOrderWithCart
{
    /**
     * This function should be called from one-page checkout context only
     *
     * @throws Exception
     */
    public function handle(Order $order, Cart $cart): Order
    {
        $order->load(['paymentMethod', 'customer.cards', 'items.product.bundle', 'pickup.schedule', 'pickup.fees']);

        return app(Pipeline::class)
            ->send([$order, $cart])
            ->through([
                UpdateOrderAttributesFromCart::class,
                UpdateOrderCustomerFromCart::class,
                UpdateOrderShippingFromCart::class,
                UpdateOrderBillingFromCart::class,
                UpdateOrderDiscountFromCart::class,
                UpdateOrderSubscriptionFromCart::class
            ])
            ->then(function ($passable) {
                /**
                 * @var Order $order
                 * @var Cart $cart
                 */
                list($order, $cart) = $passable;

                if ($order->isDirty()) {
                    $order->save();
                }

                if ($order->customer->isDirty()) {
                    $order->customer->save();

                    if ($order->customer->wasChanged('subscribed_to_sms_marketing_at') && ! is_null($order->customer->subscribed_to_sms_marketing_at)) {
                        event(new UserSubscribedToSmsMarketing($order->customer, 'during_checkout'));
                    }
                }

                return $order->confirm();
            });
    }
}