<?php

namespace App\Cart;

use App\Contracts\Cartable;
use App\Http\Requests\API\CartConfirmationRequest;
use App\Models\Pickup;
use App\Models\User;
use App\Services\SubscriptionSettingsService;
use Illuminate\Support\Collection;
use Ramsey\Uuid\Uuid;

class Cart
{
    public array $params;

    public function __construct(array $params)
    {
        $this->params = $params;
    }

    public static function fromRequest(CartConfirmationRequest $request): Cart
    {
        return new Cart(array_merge($request->validated(), [
            'discounts' => ['coupons' => collect($request->get('discounts')['coupons'] ?? [])]
        ]));
    }

    public static function fromGiftCardCheckoutRequest(User $user, array $request): Cart
    {
        return new Cart(array_merge($request, [
            'delivery_method_id' => Pickup::forGiftCards()->id,
            'customer' => [
                'id' => $user->id,
                'first_name' => $request['customer']['first_name'] ?? $user->first_name,
                'last_name' => $request['customer']['last_name'] ?? $user->last_name,
                'email' => $user->email,
                'phone' => $request['customer']['phone'] ?? $user->phone,
                'save_for_later' => $request['customer']['save_for_later'] ?? false,
                'opt_in_to_sms' => $request['customer']['opt_in_to_sms'] ?? false,
            ],
            'items' => collect($request['items'])
                ->map(function(array $item) {
                    $product = \App\Models\Product::find($item['product_id']);
                    return new Item(id: Uuid::uuid4()->toString(), product: $product, quantity: $item['quantity']);
                }),
        ]));
    }

    public function cartIsEmpty(): bool
    {
        return $this->itemsInCart()->isEmpty();
    }

    public function itemsInCart(): Collection
    {
        return $this->params['items'];
    }

    public function cartTotal(): int
    {
        return max(0, $this->cartTotalBeforeStoreCredit() - $this->cartStoreCreditTotal());
    }

    public function cartTotalBeforeStoreCredit(): int
    {
        return $this->cartSubtotal()
            + $this->cartLocationFeeTotal()
            + $this->cartDeliveryTotal()
            + $this->cartTaxTotal()
            - $this->cartSubscriptionSavingsTotal()
            - $this->cartCouponTotal();
    }

    public function cartSubtotal(): int
    {
        return $this->itemsInCart()->sum(fn (Item $item) => $item->subtotal());
    }

    public function cartLocationFeeTotal(): int
    {
        if ($this->cartCustomer()?->exemptFromFees()) {
            return 0;
        }

        return $this->cartLocation()
            ?->fees()->sum('amount')
            ?? 0;
    }

    public function cartCustomer(): ?User
    {
        if ( ! $this->params['customer']['id']) {
            return null;
        }

        return User::find($this->params['customer']['id']);
    }

    public function cartLocation(): ?Pickup
    {
        if ( ! $this->params['delivery_method_id']) {
            return null;
        }

        return Pickup::withVirtual()->find($this->params['delivery_method_id']);
    }

    public function cartDeliveryTotal(): int
    {
        if ($this->cartCustomer()?->exemptFromFees()) {
            return 0;
        }

        $location = $this->cartLocation();

        $amount = $location->delivery_rate;

        if ($location->deliveryFeeByWeight()) {
            $amount = (int) round($amount * $this->cartWeight());
        }

        if ($location->deliveryFeeHasCap() && $this->cartSubtotal() >= $location->deliveryCapThreshold()) {
            $amount = $location->deliveryCap();
        }

        return $amount;
    }

    public function cartWeight(): float
    {
        return $this->itemsInCart()->sum(fn (Item $item) => $item->weight());
    }

    public function cartTaxTotal(): int
    {
        $location = $this->cartLocation();

        $rate = $location->tax_rate ?? 0;

        if ( ! $rate || $this->cartCustomer()?->exemptFromTax()) {
            return 0;
        }

        $total = $this->cartItemTaxTotal($rate) + $this->locationFeeTaxTotal($rate);

        if ($location->deliveryFeeIsTaxed()) {
            $total += $this->cartDeliveryTaxTotal($rate);
        }

        return $total;
    }

    private function cartItemTaxTotal(float $rate): int
    {
        $taxable_subtotal = $this->itemsInCart()
            ->filter(fn(Item $item) => (bool) $item->product->taxable)
            ->sum(fn(Item $item) => $item->subtotal());

        return (int) round($taxable_subtotal * $rate);
    }

    private function locationFeeTaxTotal(float $rate): int
    {
        $taxable_subtotal = $this->cartLocation()
            ->fees()
            ->where(['taxable' => true])
            ->sum('amount');

        return (int) round($taxable_subtotal * $rate);
    }

    private function cartDeliveryTaxTotal(float $rate): int
    {
        return (int) round($this->cartDeliveryTotal() * $rate);
    }

    public function cartSubscriptionSavingsTotal(): int
    {
        if ($this->purchaseType() === Cartable::ONE_TIME_PURCHASE) {
            return 0;
        }

        return $this->rawRecurringOrderSavings();
    }

    public function purchaseType(): int
    {
        return $this->isRecurring()
            ? Cartable::SUBSCRIPTION_PURCHASE
            : Cartable::ONE_TIME_PURCHASE;
    }

    public function isRecurring(): bool
    {
        return ($this->params['purchase_type'] ?? false) === 'recurring';
    }

    public function rawRecurringOrderSavings(): int
    {
        $subtotal = $this->itemsInCart()
            ->filter(fn (Item $item) => $item->isEligibleForSubscriptionSavings())
            ->sum(fn (Item $item) => $item->subtotal());

        return (int) round(($subtotal * ( (float) app(SubscriptionSettingsService::class)->discountIncentive() / 100)));
    }

    public function cartCouponTotal(): int
    {
        return $this->coupons()->sum(fn (Coupon $coupon) => $coupon->amount);
    }

    /** @return Collection<Coupon> */
    public function coupons(): Collection
    {
        return $this->params['discounts']['coupons'] ?? collect();
    }

    public function cartStoreCreditTotal(): int
    {
        if ($this->containsGiftCards()) {
            return 0;
        }

        return $this->cartCustomer()->credit ?? 0;
    }

    private function containsGiftCards(): bool
    {
        return $this->itemsInCart()->contains(fn (Item $item) => $item->product->isGiftCard());
    }

    public function toArray(): array
    {
        return $this->params;
    }
}
