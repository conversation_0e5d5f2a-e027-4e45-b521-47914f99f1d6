<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ContactFormSubmitted
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $topic;
    public $email;
    public $full_name;
    public $body;

    public function __construct(string $topic, string $email, string $full_name, string $body)
    {
        $this->topic = $topic;
        $this->email = $email;
        $this->full_name = $full_name;
        $this->body = $body;
    }
}
