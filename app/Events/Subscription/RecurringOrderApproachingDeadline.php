<?php

namespace App\Events\Subscription;

use App\Models\Order;
use App\Models\Schedule;
use Illuminate\Queue\SerializesModels;

class RecurringOrderApproachingDeadline
{
    use SerializesModels;

    public Order $order;
    public Schedule $schedule;

    public function __construct(Order $order, Schedule $schedule)
    {
        $this->order = $order;
        $this->schedule = $schedule;
    }
}
