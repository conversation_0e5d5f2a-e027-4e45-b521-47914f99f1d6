<?php

namespace App\Events\Subscription;

use App\Models\RecurringOrder;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class SubscriptionPromoProductWasUpdated
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(
        public RecurringOrder $subscription,
        public int $old_promo_product_id,
        public int $new_promo_product_id
    ) {}
}
