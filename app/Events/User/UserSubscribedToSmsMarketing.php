<?php

namespace App\Events\User;

use App\Models\User;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;

class UserSubscribedToSmsMarketing
{
    use Dispatchable, InteractsWithSockets;

    /** @var User */
    public $user;

    public $opt_in_location;

    public function __construct(User $user, string $opt_in_location)
    {
        $this->user = $user;
        $this->opt_in_location = $opt_in_location;
    }
}
