<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class InventoryAdjusted
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $productId;
    public $originalQty;
    public $newQty;
    public $description;

    public function __construct($productId, $originalQty, $newQty, $description = null)
    {
        $this->productId = $productId;
        $this->originalQty = $originalQty;
        $this->newQty = $newQty;
        $this->description = $description;
    }
}
