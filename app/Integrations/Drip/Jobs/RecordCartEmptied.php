<?php

namespace App\Integrations\Drip\Jobs;

use App\Contracts\Cartable;
use App\Contracts\CartService;
use App\Integrations\Drip\DripIntegration;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class RecordCartEmptied implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(
        public int $user_id,
        public string $cart_id
    ) {}

    public function handle()
    {
        $drip = DripIntegration::getInstance();

        if (is_null($drip)) return;

        $user = User::findOrFail($this->user_id);

        $cart = app(CartService::class)
            ->findById($this->cart_id);

        if (is_null($cart)) return;

        $order_window = $cart->cartLocation()?->activeOrderWindow();

        // Add additional metadata
        $user['pickup_date'] = $order_window?->deliveryDatetime();

        $drip->recordEvent($user->email, 'Emptied cart', [
            'id' => $cart->cartId(),
            'schedule_id' => $cart->cartLocation()?->schedule_id,
            'fulfillment_id' => $cart->cartLocation()?->id,
            'pickup_date' => $order_window?->deliveryDatetime()?->format('m/d/y')
        ]);
    }

    public function tags()
    {
        return ['integration', 'drip'];
    }
}
