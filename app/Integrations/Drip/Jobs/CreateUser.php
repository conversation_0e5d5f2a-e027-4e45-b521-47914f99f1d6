<?php

namespace App\Integrations\Drip\Jobs;

use App\Integrations\Drip\DripIntegration;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class CreateUser implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $userId;

    public $tags;

    public function __construct($userId, array $tags = [])
    {
        $this->userId = $userId;
        $this->tags = $tags;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $drip = DripIntegration::getInstance();

        if (is_null($drip)) return;

        $user = User::findOrFail($this->userId);

        $drip->recordEvent($user->email, 'Created an account', [
            'first_name' => $user['first_name'] ?? null,
            'last_name' => $user['last_name'] ?? null,
            'fulfillment_id' => $user['pickup_point'] ?? null,
            'fulfillment_name' => $user['pickup'] ? $user['pickup']['title'] : null,
            'schedule_id' => $user['pickup'] ? $user['pickup']['schedule_id'] : null,
            'postal_code' => $user['zip'] ?? null,
            'store_credit' => $user['credit'] ?? null,
            'is_active' => $user['active'] ? 'Yes' : 'No',
        ]);
    }

    public function tags()
    {
        return ['integration', 'drip'];
    }
}
