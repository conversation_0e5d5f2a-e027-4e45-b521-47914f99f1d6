<?php

return [
    'default' => [
        'type' => 'order',
        'id' => null,
        'purchase_type' => 'one_time_purchase',
        'date_id' => null,
        'delivery_method_id' => null,
        'items' => collect(),
        'subscription' => null,
        'notes' => '',
        'is_gift' => false,
        'recipient_email' => '',
        'recipient_notes' => '',
        'discounts' => [
            'coupons' => collect(),
            'gift_card' => [
                'name' => '',
                'code' => '',
                'amount' => 0,
            ],
            'store_credit' => [
                'amount' => 0,
            ]
        ],
        'customer' => [
            'id' => null,
            'first_name' => '',
            'last_name' => '',
            'email' => '',
            'phone' => '',
            'save_for_later' => false,
            'opt_in_to_sms' => false,
            'subscribed_to_sms' => false,
        ],
        'shipping' => [
            'address_id' => null,
            'street' => '',
            'street_2' => '',
            'city' => '',
            'state' => '',
            'zip' => '',
            'country' => '',
            'save_for_later' => false
        ],
        'billing' => [
            'method' => null,
            'source_id' => null,
            'save_for_later' => false
        ]
    ],

    'database' => [
        'date_id' => null,
        'delivery_method_id' => null,
        'items' => [],
        'subscription' => null,
        'notes' => '',
        'is_gift' => false,
        'recipient_email' => '',
        'recipient_notes' => '',
        'discounts' => [
            'coupons' => [],
            'gift_card' => [
                'name' => '',
                'code' => '',
                'amount' => 0,
            ],
            'store_credit' => [
                'amount' => 0,
            ]
        ],
        'customer' => [
            'id' => null,
            'first_name' => '',
            'last_name' => '',
            'email' => '',
            'phone' => '',
            'save_for_later' => false,
            'opt_in_to_sms' => false,
            'subscribed_to_sms' => false,
        ],
        'shipping' => [
            'address_id' => null,
            'street' => '',
            'street_2' => '',
            'city' => '',
            'state' => '',
            'zip' => '',
            'country' => '',
            'save_for_later' => false
        ],
        'billing' => [
            'method' => null,
            'source_id' => null,
            'save_for_later' => false
        ]
    ],

    'preorder' => [
        'type' => 'pre-order',
        'date_id' => null,
        'delivery_method_id' => null,
        'items' => collect(),
        'notes' => '',
        'is_gift' => false,
        'recipient_email' => '',
        'recipient_notes' => '',
        'discounts' => [
            'coupons' => collect(),
            'gift_card' => [
                'name' => '',
                'code' => '',
                'amount' => 0,
            ],
            'store_credit' => [
                'amount' => 0,
            ]
        ],
        'customer' => [
            'id' => null,
            'first_name' => '',
            'last_name' => '',
            'email' => '',
            'phone' => '',
            'save_for_later' => false,
            'opt_in_to_sms' => false,
            'subscribed_to_sms' => false,
        ],
        'shipping' => [
            'street' => '',
            'street_2' => '',
            'city' => '',
            'state' => '',
            'zip' => '',
            'country' => '',
            'save_for_later' => false
        ],
        'billing' => [
            'method' => null,
            'source_id' => null,
            'save_for_later' => false
        ]
    ],

    'gift-card' => [
        'type' => 'gift-card',
        'items' => collect(),
        'notes' => '',
        'discounts' => [
            'coupons' => collect(),
            'gift_card' => [
                'name' => '',
                'code' => '',
                'amount' => 0,
            ],
            'store_credit' => [
                'amount' => 0,
            ]
        ],
        'customer' => [
            'id' => null,
            'first_name' => '',
            'last_name' => '',
            'email' => '',
            'phone' => '',
            'save_for_later' => false,
            'opt_in_to_sms' => false,
            'subscribed_to_sms' => false,
        ],
        'shipping' => [
            'street' => '',
            'street_2' => '',
            'city' => '',
            'state' => '',
            'zip' => '',
            'country' => '',
            'save_for_later' => false
        ],
        'billing' => [
            'method' => 'card',
            'source_id' => null,
            'save_for_later' => false
        ]
    ]
];
