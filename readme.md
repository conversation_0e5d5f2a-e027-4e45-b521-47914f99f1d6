## Setup Local Environment

### Configure Docker Environment
Copy the example docker environment file:
```shell
cp docker.env.example docker.env
```

### Configure Environment Variables
Next, we'll create our application `.env` and `.env.test` files.
```bash
cp .env.docker.example .env
```

### Configure Environment Variables
Next, we'll create our application `.env` file.
```bash
cp .env.docker.example .env.test
```

Populate both of these files with the local environment settings found in 1Password
under:
- Local .env
- Local .env.test

### Register an account with mailtrap.io
Sign up for an account at https://mailtrap.io.

Update your `.env` and `.env.test` files with your mailtrap settings:

```text
MAIL_MAILER=smtp
MAIL_HOST=sandbox.smtp.mailtrap.io
MAIL_PORT=2525
MAIL_USERNAME=<username>
MAIL_PASSWORD=<password>
```

### Configure Composer Authentication for Nova
We'll need to add authentication credentials in order to install <PERSON>
via Composer.

Ask another developer for the Nova license key and add it to the
`docker.env` as the password.

```text
COMPOSER_AUTH_USERNAME=<EMAIL>
COMPOSER_AUTH_PASSWORD=<license-key>
```

### Build and Run Docker Containers
We're now ready to spin up our Docker containers.  This will create our Laravel app,
install Composer dependencies, and create containers for MySQL, Redis, and MailHog.
```shell
docker compose up --build
```

Note: It might take a couple of minutes to start the app container on first run
as it installs dependencies.

### Update Hosts File
We need to update our hosts file with some custom domains.

```shell
sudo nano /etc/hosts
```

Add these entries:
```text
127.0.0.1   grazecart.local
127.0.0.1   admin.grazecart.local
127.0.0.1   api.grazecart.local
127.0.0.1   accounts.grazecart.local
127.0.0.1   <your-subdomain>.grazecart.local
127.0.0.1   testfarm.grazecart.local
```

### Database Migrations
Once the Docker containers are running, we need to run our migrations:
```shell
docker compose exec app bash
php artisan migrate
```

### Register an Account
At this point, the site should be up and running!  Now we need to
create our local test account.

To create an account, visit:
http://accounts.grazecart.local/register

Fill in the forms to complete the registration and wait for the site to be ready.

Note: After the site is ready it will display buttons for the Storefront and Admin portal.
These URLs will be `https://` but you will need to use `http://` to access them on local.

## Tools & Utilities

### PHPUnit
To run the PHP unit tests:

```shell
docker compose exec app bash
vendor/bin/phpunit
```

### Laravel Nova
To access the Nova admin panel, we need to create a user:

```shell
docker compose exec app bash
php artisan nova:user
```

Follow the prompts to create the account.

You can access the Nova installation at:
http://admin.grazecart.local/nova

Note: If you can't sign in, you may need to reset your password.  You can find the
password reset email in your mailtrap.io inbox.

### Laravel Horizon
To start Horizon:
```shell
docker compose exec app bash
php artisan horizon
```

You can access the Horizon installation at:
http://admin.grazecart.local/horizon

## E2E Testing with Playwright

### Setup Local E2E
To set up your local environment to run E2E tests, first run the setup command:
```bash
npm run e2e:local-setup
```

Now your environment is ready to run tests with a local environment configured 
and authentication credentials stored for use in future tests.

### Run E2E Tests
To run E2E tests in headless mode, run command:
```bash
npm run e2e:test
```

To run E2E tests via the Playwright UI, run command:
```bash
npm run e2e:test-ui
```

### Other Playwright Commands
Inside the root directory, you can run several commands:

```shell
# Runs the end-to-end tests.
npx playwright test
```

```shell
# Starts the interactive UI mode.
npx playwright test --ui
```

```shell
# Runs the tests only on Desktop Chrome.
npx playwright test --project=chromium
```

```shell
# Runs the tests in a specific file.
npx playwright test example
```

```shell
# Runs the tests in debug mode.
npx playwright test --debug
```

```shell
# Auto generate tests with Codegen.
npx playwright codegen
```

We suggest that you begin by typing:
```shell
npx playwright test
```

And check out the following files:
- `./e2e/example.spec.ts` - Example end-to-end test
- `./tests-examples/demo-todo-app.spec.ts` - Demo Todo App end-to-end tests
- `./playwright.config.ts` - Playwright Test configuration

Visit https://playwright.dev/docs/intro for more information. ✨
