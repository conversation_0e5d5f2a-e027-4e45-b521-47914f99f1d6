<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class PageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $date = date('Y-m-d H:i:s');
        DB::table('pages')->truncate();

        // Homepage
        DB::table('pages')->insert([
            'id' => 1,
            'title' => 'Homepage',
            'slug' => 'homepage',
            'body' => '',
            'path' => '/',
            'created_at' => $date,
            'updated_at' => $date
        ]);

        // About us page
        DB::table('pages')->insert([
            'id' => 2,
            'title' => 'About Us',
            'slug' => 'about-us',
            'body' => '',
            'path' => '/',
            'created_at' => $date,
            'updated_at' => $date
        ]);

        // Store Landing Page
        DB::table('pages')->insert([
            'id' => 3,
            'title' => 'Store Landing Page',
            'slug' => Str::slug('Store Landing Page'),
            'body' => '',
            'path' => '/',
            'created_at' => $date,
            'updated_at' => $date
        ]);
    }
}
