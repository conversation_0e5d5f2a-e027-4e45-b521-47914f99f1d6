<?php

namespace Database\Seeders;

use App\Models\Collection;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class CollectionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $date = date('Y-m-d H:i:s');
        DB::statement('SET foreign_key_checks = 0');
        DB::table('collections')->truncate();

        // Grass-fed Beef
        DB::table('collections')->insert([
            [
                'id' => 1,
                'title' => 'BEEF',
                'slug' => 'beef',
                'description' => 'This is your category description that can be changed in your admin portal.',
                'cover_photo' => 'https://s3.amazonaws.com/grazecart/newtrialtemplate/images/1642778133_61eace1507630.jpg',
                'visible' => true,
                'order' => 'title-desc',
                'created_at' => $date,
                'updated_at' => $date
            ],
            [
                'id' => 2,
                'title' => 'PORK',
                'slug' => 'pork',
                'description' => 'This is your category description that can be changed in your admin portal.',
                'cover_photo' => 'https://s3.amazonaws.com/grazecart/newtrialtemplate/images/1642778127_61eace0f30c13.jpg',
                'visible' => true,
                'order' => 'title-desc',
                'created_at' => $date,
                'updated_at' => $date
            ],
            [
                'id' => 3,
                'title' => 'POULTRY',
                'slug' => 'poultry',
                'description' => 'This is your category description that can be changed in your admin portal.',
                'cover_photo' => 'https://s3.amazonaws.com/grazecart/newtrialtemplate/images/1642778121_61eace094ad24.jpg',
                'visible' => true,
                'order' => 'title-desc',
                'created_at' => $date,
                'updated_at' => $date
            ],
        ]);
    }
}
