<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pickup_states', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('pickup_id')->unsigned()->index();
            $table->string('state', 4)->index();
            $table->foreign('pickup_id')
                ->references('id')->on('pickups')
                ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pickup_states');
    }
};
