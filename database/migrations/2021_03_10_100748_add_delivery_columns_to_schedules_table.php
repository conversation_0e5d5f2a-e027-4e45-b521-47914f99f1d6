<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('schedules', function (Blueprint $table) {
            $table->dateTime('first_delivery_date')->nullable()->default(null)->after('type_id');
            $table->dateTime('first_delivery_deadline')->nullable()->default(null)->after('first_delivery_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('schedules', function (Blueprint $table) {
            $table->dropColumn('first_delivery_date');
            $table->dropColumn('first_delivery_deadline');
        });
    }
};
