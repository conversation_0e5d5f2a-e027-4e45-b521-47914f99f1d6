<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('product_price_groups', function (Blueprint $table) {
            $table->tinyInteger('type')->unsigned()->default(0)->after('title');
            $table->integer('amount')->unsigned()->nullable()->after('type');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('product_price_groups', function (Blueprint $table) {
            $table->dropTimestamps();
            $table->drop('amount');
            $table->drop('type');
        });
    }
};
