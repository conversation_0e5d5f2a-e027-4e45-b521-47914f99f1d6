<?php

use App\Models\Product;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->unsignedInteger('vendor_id')->nullable()->default(null)->change();
        });

        // remove all invalid relationships
        if (class_exists(Product::class)) {
            Product::withTrashed()
                ->whereNotIn('vendor_id', function ($query) {
                    $query->select('id')->from('vendors');
                })
                ->update(['vendor_id' => null]);
        }

        Schema::table('products', function (Blueprint $table) {
            $table->foreign('vendor_id')->references('id')->on('vendors');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropForeign(['vendor_id']);
        });

        Schema::table('products', function (Blueprint $table) {
            $table->unsignedInteger('vendor_id')->nullable(false)->default(0)->change();
        });
    }
};
