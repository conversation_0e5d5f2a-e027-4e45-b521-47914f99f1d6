<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('metrics', function (Blueprint $table) {
            $table->string('name');
            $table->dateTime('captured_at');
            $table->schemalessAttributes('extra_attributes');

            $table->primary(['name', 'captured_at']);
        });
    }
};
