<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('themes', function (Blueprint $table) {
            $table->string('id')->unique();
            $table->string('title');
            $table->string('view_path')->nullable();
            $table->mediumText('settings');
            $table->mediumText('css');
            $table->mediumText('css_preview');
            $table->mediumText('custom_css');
            $table->boolean('active')->default(true);
            $table->integer('user_id')->unsigned();
            $table->timestamps();
            $table->primary('id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('themes');
    }
};
