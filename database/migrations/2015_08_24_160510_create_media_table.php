<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('media', function (Blueprint $table) {
            $table->increments('id');
            $table->string('title');
            $table->string('path');
            $table->string('thumbnail_path');
            $table->integer('height')->unsigned();
            $table->integer('width')->unsigned();
            $table->integer('size')->unsigned();
            $table->enum('layout', ['landscape','portrait']);
            $table->text('caption');
            $table->enum('type', ['image','video','document']);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::drop('media');
    }
};
