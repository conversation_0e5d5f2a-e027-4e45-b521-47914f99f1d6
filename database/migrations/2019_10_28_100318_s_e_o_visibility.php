<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('collections', function (Blueprint $table) {
            $table->boolean('seo_visibility')->default(true);
        });

        Schema::table('products', function (Blueprint $table) {
            $table->boolean('seo_visibility')->default(true)->after('accounting_class');
        });

        Schema::table('pages', function (Blueprint $table) {
            $table->boolean('seo_visibility')->default(true)->after('needs_published');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('collections', function (Blueprint $table) {
            $table->dropColumn('seo_visibility');
        });

        Schema::table('products', function (Blueprint $table) {
            $table->dropColumn('seo_visibility');
        });

        Schema::table('pages', function (Blueprint $table) {
            $table->dropColumn('seo_visibility');
        });
    }
};
