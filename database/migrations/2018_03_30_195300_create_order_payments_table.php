<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_payments', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('order_id')->unsigned()->default(0);
            $table->tinyInteger('payment_type_id')->unsigned()->nullable();
            $table->string('payment_type', 32)->nullable();
            $table->string('description', 500)->nullable();
            $table->integer('customer_id')->unsigned()->nullable();
            $table->integer('admin_id')->unsigned()->nullable();
            $table->integer('amount')->unsigned()->default(0);
            $table->string('payment_id')->nullable();
            $table->string('source_id')->nullable();
            $table->timestamp('refunded_at')->nullable();
            $table->integer('amount_refunded')->nullable();
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_payments');
    }
};
