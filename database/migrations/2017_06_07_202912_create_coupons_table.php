<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('coupons', function (Blueprint $table) {
            $table->increments('id');
            $table->string('discount_type')->default('fixed');
            $table->string('application_type')->default('order');
            $table->string('code')->unique();
            $table->integer('discount_amount')->unsigned();
            $table->tinyInteger('discount_percentage')->unsigned();
            $table->text('settings');
            $table->string('description')->nullable();
            $table->boolean('min_order')->default(false);
            $table->integer('min_order_amount')->unsigned();
            $table->boolean('auto_apply')->default(false);
            $table->boolean('limit_usage')->default(false);
            $table->integer('max_uses')->unsigned();
            $table->integer('total_uses')->unsigned()->default(0);
            $table->boolean('once_per_customer')->default(false);
            $table->boolean('expires')->default(false);
            $table->timestamp('expires_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('coupons');
    }
};
