<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pages', function (Blueprint $table) {
            $table->increments('id');
            $table->string('title');
            $table->string('subtitle');
            $table->string('slug')->unique();
            $table->string('page_title');
            $table->string('description');
            $table->longText('body');
            $table->string('path');
            $table->string('layout')->nullable();
            $table->text('settings')->nullable();
            $table->boolean('visible')->default(true);
            $table->boolean('needs_published')->default(false);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::drop('pages');
    }
};
