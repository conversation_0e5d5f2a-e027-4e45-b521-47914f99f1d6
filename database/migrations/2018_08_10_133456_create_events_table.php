<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('events', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('user_id')->unsugned()->index(); // Who performed the event.
            $table->integer('model_id')->unsigned()->nullable()->index(); // The model associated with the events
            $table->string('model_type')->nullable()->index(); // The type of model associated with the event.
            $table->string('event_id')->nullable()->index(); // The actual event performed.
            $table->string('description')->nullable(); // Short description of the event.
            $table->text('metadata')->nullable();
            $table->timestamp('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('events');
    }
};
