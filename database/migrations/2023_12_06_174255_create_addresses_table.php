<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('addresses', function (Blueprint $table) {
            $table->id();
            $table->string('street', 100);
            $table->string('city', 60);
            $table->string('state', 50);
            $table->string('postal_code', 15);
            $table->string('country', 60);
            $table->geometry(column: 'point', subtype: 'point', srid: 4326)->nullable();
            $table->timestamps();
        });
    }
};
