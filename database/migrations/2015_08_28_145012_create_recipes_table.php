<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('recipes', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('user_id')->unsigned();
            $table->string('title');
            $table->string('slug')->unique();
            $table->text('description');
            $table->longText('instructions');
            $table->string('video')->nullable();
            $table->integer('prep_time')->unsigned();
            $table->integer('cook_time')->unsigned();
            $table->string('servings')->nullable();
            $table->string('cover_photo')->nullable();
            $table->boolean('published')->default(true);
            $table->timestamp('published_at');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::drop('recipes');
    }
};
