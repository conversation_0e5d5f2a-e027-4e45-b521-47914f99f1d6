<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('recurring_orders', function (Blueprint $table) {
            $table->unsignedInteger('customer_id')->change();
            $table->unsignedInteger('fulfillment_id')->change();
            $table->foreign('customer_id')->references('id')->on('users');
            $table->foreign('fulfillment_id')->references('id')->on('pickups');
        });
    }
};
