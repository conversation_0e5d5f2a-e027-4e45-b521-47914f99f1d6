<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pickups', function (Blueprint $table) {
            $table->string('canonical_url')->after('settings')->nullable();
            $table->string('page_heading')->after('canonical_url')->nullable();
            $table->string('page_title')->after('page_heading')->nullable();
            $table->string('page_description')->after('page_title')->nullable();
            $table->mediumText('head_tags')->after('page_description')->nullable();
            $table->mediumText('body_tags')->after('head_tags')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pickups', function (Blueprint $table) {
            $table->dropColumn('page_heading');
            $table->dropColumn('canonical_url');
            $table->dropColumn('page_title');
            $table->dropColumn('page_description');
            $table->dropColumn('head_tags');
            $table->dropColumn('body_tags');
        });
    }
};
