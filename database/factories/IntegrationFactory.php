<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\Integration;

class IntegrationFactory extends Factory
{
    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->unique()->md5(),
            'category' =>  $this->faker->slug(),
            'enabled' => true,
            'autoload' => true,
            'settings' => [
                'api_key' => $this->faker->md5(),
                'subscriptions' => [
                    [
                        'name' => $this->faker->word(),
                        'enabled' => $this->faker->boolean()
                    ]
                ]
            ]
        ];
    }

    public function integrationName($name = 'attentive')
    {
        return $this->state(fn (array $attributes) => ['name' => $name]);
    }
}
