<?php

namespace Database\Factories;

use App\Models\Order;
use App\Models\Product;
use Illuminate\Database\Eloquent\Factories\Factory;

class OrderItemFactory extends Factory
{
    public function definition(): array
    {
        return [
            'order_id' => Order::factory(),
            'user_id' => 0,
            'type' => 'standard',
            'product_id' => Product::factory(),
            'title' => $this->faker->word(),
            'unit_of_issue' => 'package',
            'store_price' => 150,
            'unit_price' => 150,
            'original_unit_price' => 150,
            'stock_status' => 'full',
            'qty' => 2,
            'original_qty' => 2,
            'fulfilled_qty' => 2,
            'original_weight' => 1.234,
            'weight' => 1.234,
            'subtotal' => 300,
            'tax' => 0,
            'discount' => 0,
            'created_year' => now()->year,
            'created_month' => now()->month,
            'created_day' => now()->day,
        ];
    }
}
