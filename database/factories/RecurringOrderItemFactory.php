<?php

namespace Database\Factories;

use App\Models\Product;
use App\Models\RecurringOrder;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class RecurringOrderItemFactory extends Factory
{
    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'order_id' => RecurringOrder::factory(),
            'customer_id' => User::factory(),
            'product_id' => Product::factory(),
            'qty' => $this->faker->numberBetween(1,4),
            'type' => 'recurring', // recurring, promo, addon
            'deleted_at' => null,
            'unit_price_override' => null
        ];
    }
}
