<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\BundleProduct;
use App\Models\Product;

class BundleProductFactory extends Factory
{
    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'bundle_id' => Product::factory(),
            'product_id' =>  Product::factory(),
            'qty' => 2,
            'sort' => 1,
        ];
    }
}
