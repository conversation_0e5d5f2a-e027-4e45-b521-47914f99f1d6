<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\SubscriptionFeatures;

class SubscriptionFeaturesFactory extends Factory
{
    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'feature_id' => $this->faker->unique()->name(),
            'limit' => $this->faker->randomNumber(1,10),
        ];
    }
}
